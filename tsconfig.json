{
    "compilerOptions": {
        "types": ["./types"],
        "target": "ESNext",
        "useDefineForClassFields": true,
        "lib": ["DOM", "DOM.Iterable", "ESNext"],
        "allowJs": false,
        "skipLibCheck": true,
        "esModuleInterop": false,
        "allowSyntheticDefaultImports": true,
        "strict": true,
        "forceConsistentCasingInFileNames": true,
        "module": "ESNext",
        "moduleResolution": "bundler",
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "react-jsx",
        "baseUrl": ".",
        "paths": {
            "@/*": ["src/ui/*"]  // ✅ Match the actual folder structure
        }
    },
    "include": ["src"],
    "exclude": ["src/electron"],
    "references": [{ "path": "./tsconfig.node.json" }]
}
