{"name": "alpine-intellect", "private": true, "version": "0.0.0", "type": "module", "main": "dist-electron/main.js", "productName": "Alpine Intellect", "scripts": {"dev": "npm-run-all --parallel dev:react dev:electron", "dev:react": "vite", "dev:electron": "yarn transpile:electron && cross-env NODE_ENV=development electron .", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview", "transpile:electron": "tsc --project src/electron/tsconfig.json && node src/scripts/copy-assets.cjs", "dist:mac:arm64": "yarn transpile:electron && yarn build && electron-builder --mac --arm64", "dist:mac:x64": "yarn transpile:electron && yarn build && electron-builder --mac --x64", "dist:win": "yarn transpile:electron && yarn build && electron-builder --win --x64", "dist:linux": "yarn transpile:electron && yarn build && electron-builder --linux --x64", "rebuild": "electron-rebuild -f --only better-sqlite3,sharp", "rebuild:all": "electron-rebuild -f", "postinstall": "electron-builder install-app-deps && npm run rebuild"}, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.830.0", "@aws-sdk/credential-providers": "^3.830.0", "@hookform/resolvers": "^3.9.0", "@lancedb/lancedb": "^0.20.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "@xenova/transformers": "^2.17.2", "apache-arrow": "^18.1.0", "async": "^3.2.6", "better-sqlite3": "^12.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "color-name": "^2.0.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dotenv": "^17.1.0", "electron-log": "^5.4.1", "embla-carousel-react": "^8.3.0", "express": "^5.1.0", "fs-extra": "^11.3.0", "ignore": "^7.0.5", "input-otp": "^1.2.4", "js-tiktoken": "^1.0.20", "keytar": "^7.9.0", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "onnxruntime-node": "^1.22.0-rev", "openai": "^5.5.1", "os-utils": "^0.0.14", "p-limit": "^6.1.0", "portfinder": "^1.0.37", "react": "^19.1.0", "react-day-picker": "^8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "semver": "^7.7.2", "sharp": "^0.34.2", "sonner": "^1.5.0", "sqlite3": "^5.1.7", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tiktoken": "^1.0.21", "tree-sitter": "^0.25.0", "tree-sitter-bash": "^0.25.0", "tree-sitter-c": "^0.24.1", "tree-sitter-c-sharp": "^0.23.1", "tree-sitter-cpp": "^0.23.4", "tree-sitter-css": "^0.23.2", "tree-sitter-dockerfile": "^0.0.1-security", "tree-sitter-go": "^0.23.4", "tree-sitter-html": "^0.23.2", "tree-sitter-java": "^0.23.5", "tree-sitter-javascript": "^0.23.1", "tree-sitter-json": "^0.24.8", "tree-sitter-php": "^0.23.12", "tree-sitter-python": "^0.23.6", "tree-sitter-ruby": "^0.23.1", "tree-sitter-rust": "^0.24.0", "tree-sitter-scss": "^1.0.0", "tree-sitter-typescript": "^0.23.2", "uri-js": "^4.4.1", "uuid": "^9.0.1", "vaul": "^0.9.3", "web-tree-sitter": "^0.22.6", "which": "^5.0.0", "wink-nlp-utils": "^2.1.0", "workerpool": "^9.3.2", "zod": "^3.23.8"}, "devDependencies": {"@electron/asar": "latest", "@electron/rebuild": "^4.0.1", "@electron/universal": "^3.0.0", "@eslint/js": "^9.25.0", "@tailwindcss/typography": "^0.5.15", "@types/async": "^3.2.24", "@types/better-sqlite3": "^7.6.13", "@types/cors": "^2.8.18", "@types/crypto-js": "^4.2.2", "@types/express": "^5.0.1", "@types/fs-extra": "^11.0.4", "@types/node": "^24.0.10", "@types/os-utils": "^0.0.4", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/semver": "^7.7.0", "@types/uuid": "^10.0.0", "@types/which": "^3.0.4", "@vitejs/plugin-react": "^4.4.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "electron": "^36.1.0", "electron-builder": "latest", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "lovable-tagger": "^1.1.7", "npm-run-all": "^4.1.5", "postcss": "^8.4.47", "rollup": "^4.40.2", "tailwindcss": "^3.4.11", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6"}, "resolutions": {"rollup": "npm:@rollup/wasm-node@*", "tree-sitter-scss": "1.0.0", "tree-sitter-css": "0.21.0"}, "engines": {"node": ">=22.12.0"}, "packageManager": "yarn@3.6.1"}