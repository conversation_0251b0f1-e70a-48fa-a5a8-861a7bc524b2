{"appId": "com.alpine-intellect.desktop-app", "files": ["dist-electron", "dist-react"], "productName": "Alpine Intellect", "extraResources": ["dist-electron/preload.cjs", "src/assets/**", {"from": "src/electron/core/tree-sitter", "to": "tree-sitter"}, {"from": "dist-electron/core/indexing/encoders", "to": "workers"}], "icon": "src/assets/ain-desktop-icon.png", "mac": {"target": "dmg", "category": "public.app-category.utilities", "hardenedRuntime": true, "entitlements": "entitlements.mac.plist", "gatekeeperAssess": false, "minimumSystemVersion": "10.12", "extendInfo": {"CFBundleURLTypes": [{"CFBundleURLName": "alpine-intellect-login", "CFBundleURLSchemes": ["thealpinecode.alpineintellect"]}], "CFBundleDisplayName": "Alpine Intellect", "CFBundleName": "Alpine Intellect"}}, "linux": {"target": "AppImage", "category": "Utility"}, "win": {"target": ["portable", "msi"]}}