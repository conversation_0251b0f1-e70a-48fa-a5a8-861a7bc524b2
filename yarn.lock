# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"7zip-bin@npm:~5.2.0":
  version: 5.2.0
  resolution: "7zip-bin@npm:5.2.0"
  checksum: 85d3102275342f1f4ba7d17e778e526dee3dbec0f57d29be7afaa6e3c26687d40a6eccf520e9140143f85a51f3353f6b545f760eff3f776c6ffb30dc5252fb7c
  languageName: node
  linkType: hard

"@alloc/quick-lru@npm:^5.2.0":
  version: 5.2.0
  resolution: "@alloc/quick-lru@npm:5.2.0"
  checksum: bdc35758b552bcf045733ac047fb7f9a07c4678b944c641adfbd41f798b4b91fffd0fdc0df2578d9b0afc7b4d636aa6e110ead5d6281a2adc1ab90efd7f057f8
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: d3ad7b89d973df059c4e8e6d7c972cbeb1bb2f18f002a3bd04ae0707da214cb06cc06929b65aa2313b9347463df2914772298bae8b1d7973f246bb3f2ab3e8f0
  languageName: node
  linkType: hard

"@aws-crypto/crc32@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/crc32@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": ^5.2.0
    "@aws-sdk/types": ^3.222.0
    tslib: ^2.6.2
  checksum: 1ddf7ec3fccf106205ff2476d90ae1d6625eabd47752f689c761b71e41fe451962b7a1c9ed25fe54e17dd747a62fbf4de06030fe56fe625f95285f6f70b96c57
  languageName: node
  linkType: hard

"@aws-crypto/sha256-browser@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-browser@npm:5.2.0"
  dependencies:
    "@aws-crypto/sha256-js": ^5.2.0
    "@aws-crypto/supports-web-crypto": ^5.2.0
    "@aws-crypto/util": ^5.2.0
    "@aws-sdk/types": ^3.222.0
    "@aws-sdk/util-locate-window": ^3.0.0
    "@smithy/util-utf8": ^2.0.0
    tslib: ^2.6.2
  checksum: 773f12f2026d82a6bb4a23a8f491894a6d32525bd9b8bfbc12896526cf11882a7607a671c478c45f9cd7d6ba1caaed48a62b67c6f725244bd83a1275108f46c7
  languageName: node
  linkType: hard

"@aws-crypto/sha256-js@npm:5.2.0, @aws-crypto/sha256-js@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-js@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": ^5.2.0
    "@aws-sdk/types": ^3.222.0
    tslib: ^2.6.2
  checksum: 007fbe0436d714d0d0d282e2b61c90e45adcb9ad75eac9ac7ba03d32b56624afd09b2a9ceb4d659661cf17c51d74d1900ab6b00eacafc002da1101664955ca53
  languageName: node
  linkType: hard

"@aws-crypto/supports-web-crypto@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/supports-web-crypto@npm:5.2.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 6ffc21de48b2b2c3e918193101d7e8fe949d47b37688892e1c39eaedaa938be80c0f404fe1c874c30cce16781026777a53bf47d5d90143ca91d0feb7c4a6f830
  languageName: node
  linkType: hard

"@aws-crypto/util@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/util@npm:5.2.0"
  dependencies:
    "@aws-sdk/types": ^3.222.0
    "@smithy/util-utf8": ^2.0.0
    tslib: ^2.6.2
  checksum: f0f81d9d2771c59946cfec48b86cb23d39f78a966c4a1f89d4753abdc3cb38de06f907d1e6450059b121d48ac65d612ab88bdb70014553a077fc3dabddfbf8d6
  languageName: node
  linkType: hard

"@aws-sdk/client-bedrock-runtime@npm:^3.830.0":
  version: 3.844.0
  resolution: "@aws-sdk/client-bedrock-runtime@npm:3.844.0"
  dependencies:
    "@aws-crypto/sha256-browser": 5.2.0
    "@aws-crypto/sha256-js": 5.2.0
    "@aws-sdk/core": 3.844.0
    "@aws-sdk/credential-provider-node": 3.844.0
    "@aws-sdk/eventstream-handler-node": 3.840.0
    "@aws-sdk/middleware-eventstream": 3.840.0
    "@aws-sdk/middleware-host-header": 3.840.0
    "@aws-sdk/middleware-logger": 3.840.0
    "@aws-sdk/middleware-recursion-detection": 3.840.0
    "@aws-sdk/middleware-user-agent": 3.844.0
    "@aws-sdk/middleware-websocket": 3.844.0
    "@aws-sdk/region-config-resolver": 3.840.0
    "@aws-sdk/token-providers": 3.844.0
    "@aws-sdk/types": 3.840.0
    "@aws-sdk/util-endpoints": 3.844.0
    "@aws-sdk/util-user-agent-browser": 3.840.0
    "@aws-sdk/util-user-agent-node": 3.844.0
    "@smithy/config-resolver": ^4.1.4
    "@smithy/core": ^3.7.0
    "@smithy/eventstream-serde-browser": ^4.0.4
    "@smithy/eventstream-serde-config-resolver": ^4.1.2
    "@smithy/eventstream-serde-node": ^4.0.4
    "@smithy/fetch-http-handler": ^5.1.0
    "@smithy/hash-node": ^4.0.4
    "@smithy/invalid-dependency": ^4.0.4
    "@smithy/middleware-content-length": ^4.0.4
    "@smithy/middleware-endpoint": ^4.1.14
    "@smithy/middleware-retry": ^4.1.15
    "@smithy/middleware-serde": ^4.0.8
    "@smithy/middleware-stack": ^4.0.4
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/node-http-handler": ^4.1.0
    "@smithy/protocol-http": ^5.1.2
    "@smithy/smithy-client": ^4.4.6
    "@smithy/types": ^4.3.1
    "@smithy/url-parser": ^4.0.4
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-body-length-node": ^4.0.0
    "@smithy/util-defaults-mode-browser": ^4.0.22
    "@smithy/util-defaults-mode-node": ^4.0.22
    "@smithy/util-endpoints": ^3.0.6
    "@smithy/util-middleware": ^4.0.4
    "@smithy/util-retry": ^4.0.6
    "@smithy/util-stream": ^4.2.3
    "@smithy/util-utf8": ^4.0.0
    "@types/uuid": ^9.0.1
    tslib: ^2.6.2
    uuid: ^9.0.1
  checksum: 622f6cfad85ca6d6374530586a45d81a0be655d46f2490e75038c86e5b48cff9d96db7b72e7f82a5c94e9c61f02f1f3133bf6cd22c533d2b3c9fe2ab6d582b46
  languageName: node
  linkType: hard

"@aws-sdk/client-cognito-identity@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/client-cognito-identity@npm:3.844.0"
  dependencies:
    "@aws-crypto/sha256-browser": 5.2.0
    "@aws-crypto/sha256-js": 5.2.0
    "@aws-sdk/core": 3.844.0
    "@aws-sdk/credential-provider-node": 3.844.0
    "@aws-sdk/middleware-host-header": 3.840.0
    "@aws-sdk/middleware-logger": 3.840.0
    "@aws-sdk/middleware-recursion-detection": 3.840.0
    "@aws-sdk/middleware-user-agent": 3.844.0
    "@aws-sdk/region-config-resolver": 3.840.0
    "@aws-sdk/types": 3.840.0
    "@aws-sdk/util-endpoints": 3.844.0
    "@aws-sdk/util-user-agent-browser": 3.840.0
    "@aws-sdk/util-user-agent-node": 3.844.0
    "@smithy/config-resolver": ^4.1.4
    "@smithy/core": ^3.7.0
    "@smithy/fetch-http-handler": ^5.1.0
    "@smithy/hash-node": ^4.0.4
    "@smithy/invalid-dependency": ^4.0.4
    "@smithy/middleware-content-length": ^4.0.4
    "@smithy/middleware-endpoint": ^4.1.14
    "@smithy/middleware-retry": ^4.1.15
    "@smithy/middleware-serde": ^4.0.8
    "@smithy/middleware-stack": ^4.0.4
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/node-http-handler": ^4.1.0
    "@smithy/protocol-http": ^5.1.2
    "@smithy/smithy-client": ^4.4.6
    "@smithy/types": ^4.3.1
    "@smithy/url-parser": ^4.0.4
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-body-length-node": ^4.0.0
    "@smithy/util-defaults-mode-browser": ^4.0.22
    "@smithy/util-defaults-mode-node": ^4.0.22
    "@smithy/util-endpoints": ^3.0.6
    "@smithy/util-middleware": ^4.0.4
    "@smithy/util-retry": ^4.0.6
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: e3e1e8bd44530b95a7346b0176d3a8c62c5d4f5f4cbf49ce60f5a51fffe66b8cb0f5e9bec11d6b283721f9bc2f5cfc556ef7c730bc48aa9c406ddcf5109fc0f7
  languageName: node
  linkType: hard

"@aws-sdk/client-sso@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/client-sso@npm:3.844.0"
  dependencies:
    "@aws-crypto/sha256-browser": 5.2.0
    "@aws-crypto/sha256-js": 5.2.0
    "@aws-sdk/core": 3.844.0
    "@aws-sdk/middleware-host-header": 3.840.0
    "@aws-sdk/middleware-logger": 3.840.0
    "@aws-sdk/middleware-recursion-detection": 3.840.0
    "@aws-sdk/middleware-user-agent": 3.844.0
    "@aws-sdk/region-config-resolver": 3.840.0
    "@aws-sdk/types": 3.840.0
    "@aws-sdk/util-endpoints": 3.844.0
    "@aws-sdk/util-user-agent-browser": 3.840.0
    "@aws-sdk/util-user-agent-node": 3.844.0
    "@smithy/config-resolver": ^4.1.4
    "@smithy/core": ^3.7.0
    "@smithy/fetch-http-handler": ^5.1.0
    "@smithy/hash-node": ^4.0.4
    "@smithy/invalid-dependency": ^4.0.4
    "@smithy/middleware-content-length": ^4.0.4
    "@smithy/middleware-endpoint": ^4.1.14
    "@smithy/middleware-retry": ^4.1.15
    "@smithy/middleware-serde": ^4.0.8
    "@smithy/middleware-stack": ^4.0.4
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/node-http-handler": ^4.1.0
    "@smithy/protocol-http": ^5.1.2
    "@smithy/smithy-client": ^4.4.6
    "@smithy/types": ^4.3.1
    "@smithy/url-parser": ^4.0.4
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-body-length-node": ^4.0.0
    "@smithy/util-defaults-mode-browser": ^4.0.22
    "@smithy/util-defaults-mode-node": ^4.0.22
    "@smithy/util-endpoints": ^3.0.6
    "@smithy/util-middleware": ^4.0.4
    "@smithy/util-retry": ^4.0.6
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: e2e22ccc5e029f044b7647e87140f6f60c637f3791d1f1b26dd171c8b6db71197004040f83f7228b9a31b92ff746ad82c0575f9f71f32499b8df153883a53feb
  languageName: node
  linkType: hard

"@aws-sdk/core@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/core@npm:3.844.0"
  dependencies:
    "@aws-sdk/types": 3.840.0
    "@aws-sdk/xml-builder": 3.821.0
    "@smithy/core": ^3.7.0
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/property-provider": ^4.0.4
    "@smithy/protocol-http": ^5.1.2
    "@smithy/signature-v4": ^5.1.2
    "@smithy/smithy-client": ^4.4.6
    "@smithy/types": ^4.3.1
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-middleware": ^4.0.4
    "@smithy/util-utf8": ^4.0.0
    fast-xml-parser: 5.2.5
    tslib: ^2.6.2
  checksum: 28f7397c058a39009965b0e76055c78ff599bce13c8204bd6fcb4f04f1a3a514d009489478e089f8f9b79193b294f3b0240994411bba449230e17947cc2af115
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-cognito-identity@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/credential-provider-cognito-identity@npm:3.844.0"
  dependencies:
    "@aws-sdk/client-cognito-identity": 3.844.0
    "@aws-sdk/types": 3.840.0
    "@smithy/property-provider": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 38b721be3c5981b9f9408021b418256dcf7ea2401758b2ff60bf1ee134f67238d88f1f9d95e8abef6ede6eaa8a47809345aca247c3be54c0cf158ff729d9ca42
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-env@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/credential-provider-env@npm:3.844.0"
  dependencies:
    "@aws-sdk/core": 3.844.0
    "@aws-sdk/types": 3.840.0
    "@smithy/property-provider": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 292a9b4c4e5402546ec78b971fa3be8e07e0af7fb795dceb038e5dcdbcccf9da6cba484185764a5895a6d681ea5008966313572be241024f379726634abca60b
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-http@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/credential-provider-http@npm:3.844.0"
  dependencies:
    "@aws-sdk/core": 3.844.0
    "@aws-sdk/types": 3.840.0
    "@smithy/fetch-http-handler": ^5.1.0
    "@smithy/node-http-handler": ^4.1.0
    "@smithy/property-provider": ^4.0.4
    "@smithy/protocol-http": ^5.1.2
    "@smithy/smithy-client": ^4.4.6
    "@smithy/types": ^4.3.1
    "@smithy/util-stream": ^4.2.3
    tslib: ^2.6.2
  checksum: 1b4c6ce7bedd77bfe10d2f9b7384ab5b1e0a110044f14e73da92be9969dfd84e07c25b854e252bb34e4032ed61b7863a9581e6c0b302f9adfb6ac3c69f47a20b
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-ini@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/credential-provider-ini@npm:3.844.0"
  dependencies:
    "@aws-sdk/core": 3.844.0
    "@aws-sdk/credential-provider-env": 3.844.0
    "@aws-sdk/credential-provider-http": 3.844.0
    "@aws-sdk/credential-provider-process": 3.844.0
    "@aws-sdk/credential-provider-sso": 3.844.0
    "@aws-sdk/credential-provider-web-identity": 3.844.0
    "@aws-sdk/nested-clients": 3.844.0
    "@aws-sdk/types": 3.840.0
    "@smithy/credential-provider-imds": ^4.0.6
    "@smithy/property-provider": ^4.0.4
    "@smithy/shared-ini-file-loader": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 5f5acce165d5de9ba8f90aba3f43b4efb741d0106ad3d47ad26dc3c8b59c3e77e8c8d8a76dd410338f9459c85527ec6f7be27d9a98766ebb3677dee495af5473
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-node@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/credential-provider-node@npm:3.844.0"
  dependencies:
    "@aws-sdk/credential-provider-env": 3.844.0
    "@aws-sdk/credential-provider-http": 3.844.0
    "@aws-sdk/credential-provider-ini": 3.844.0
    "@aws-sdk/credential-provider-process": 3.844.0
    "@aws-sdk/credential-provider-sso": 3.844.0
    "@aws-sdk/credential-provider-web-identity": 3.844.0
    "@aws-sdk/types": 3.840.0
    "@smithy/credential-provider-imds": ^4.0.6
    "@smithy/property-provider": ^4.0.4
    "@smithy/shared-ini-file-loader": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 5b859fb4ff746906053e0686951e268743a2b9e025fa70284ed341956917b47d803a16cd38879cfd124407623969e248a90044202909fe4d281e7e5ee331ec8a
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-process@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/credential-provider-process@npm:3.844.0"
  dependencies:
    "@aws-sdk/core": 3.844.0
    "@aws-sdk/types": 3.840.0
    "@smithy/property-provider": ^4.0.4
    "@smithy/shared-ini-file-loader": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 0dbf2fd52d308ecc51a456799f48fcb17094fbe46c4f03f9eff7693dda9c15f3d667637b23e0cc810a6b64f00d41c63eba2419637c3c44af3da27bbfd7c09ed4
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-sso@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/credential-provider-sso@npm:3.844.0"
  dependencies:
    "@aws-sdk/client-sso": 3.844.0
    "@aws-sdk/core": 3.844.0
    "@aws-sdk/token-providers": 3.844.0
    "@aws-sdk/types": 3.840.0
    "@smithy/property-provider": ^4.0.4
    "@smithy/shared-ini-file-loader": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 9775b782f12fee185fdc13e047a6cf0940e3b5abec99208500a12707ccff4d1c65f0181bb9494f47968fc1159a657387c014bc41b7c4b906ede8e18f3d78f175
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-web-identity@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/credential-provider-web-identity@npm:3.844.0"
  dependencies:
    "@aws-sdk/core": 3.844.0
    "@aws-sdk/nested-clients": 3.844.0
    "@aws-sdk/types": 3.840.0
    "@smithy/property-provider": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 90186be1fa65421141c62571a059203756c3b79ae198ed0413b4d9970a832738e87c62121a1834bfbce1cd956e35ca15e6db9ca07fe855729fbe89cdea0938f5
  languageName: node
  linkType: hard

"@aws-sdk/credential-providers@npm:^3.830.0":
  version: 3.844.0
  resolution: "@aws-sdk/credential-providers@npm:3.844.0"
  dependencies:
    "@aws-sdk/client-cognito-identity": 3.844.0
    "@aws-sdk/core": 3.844.0
    "@aws-sdk/credential-provider-cognito-identity": 3.844.0
    "@aws-sdk/credential-provider-env": 3.844.0
    "@aws-sdk/credential-provider-http": 3.844.0
    "@aws-sdk/credential-provider-ini": 3.844.0
    "@aws-sdk/credential-provider-node": 3.844.0
    "@aws-sdk/credential-provider-process": 3.844.0
    "@aws-sdk/credential-provider-sso": 3.844.0
    "@aws-sdk/credential-provider-web-identity": 3.844.0
    "@aws-sdk/nested-clients": 3.844.0
    "@aws-sdk/types": 3.840.0
    "@smithy/config-resolver": ^4.1.4
    "@smithy/core": ^3.7.0
    "@smithy/credential-provider-imds": ^4.0.6
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/property-provider": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 20f98117e9344297e4c099ed5903dbe006a444cf8bba725bf606fab63c2300b88972aa75a1444607a1c2c366126ecf0ca2e42e8894af800b183f9d193577d5a8
  languageName: node
  linkType: hard

"@aws-sdk/eventstream-handler-node@npm:3.840.0":
  version: 3.840.0
  resolution: "@aws-sdk/eventstream-handler-node@npm:3.840.0"
  dependencies:
    "@aws-sdk/types": 3.840.0
    "@smithy/eventstream-codec": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 7450a36fba3626664661257462d97d84319a82cbeb16054aff5fc3c5d1d78982324fc52ad8105dda3b50f301e148d412cf7b9794a672c6800a17564811b7d9cb
  languageName: node
  linkType: hard

"@aws-sdk/middleware-eventstream@npm:3.840.0":
  version: 3.840.0
  resolution: "@aws-sdk/middleware-eventstream@npm:3.840.0"
  dependencies:
    "@aws-sdk/types": 3.840.0
    "@smithy/protocol-http": ^5.1.2
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: c05edf8e0074158133d939f1becf060f1c4c736d413799393088b0179c8dab680d25cb8e8b5a3474b9c0cfd49eada1282383d118aa574b4b53737d6eb12cd9d1
  languageName: node
  linkType: hard

"@aws-sdk/middleware-host-header@npm:3.840.0":
  version: 3.840.0
  resolution: "@aws-sdk/middleware-host-header@npm:3.840.0"
  dependencies:
    "@aws-sdk/types": 3.840.0
    "@smithy/protocol-http": ^5.1.2
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 8d4a51007aa740daeea1c8427d7f2bf5d91d8fa9bd890ed7212a7460b68878bd651666585ef7cf2f553fe34aac141b1eaa8cd9b3520da0fc62918e7e43473b02
  languageName: node
  linkType: hard

"@aws-sdk/middleware-logger@npm:3.840.0":
  version: 3.840.0
  resolution: "@aws-sdk/middleware-logger@npm:3.840.0"
  dependencies:
    "@aws-sdk/types": 3.840.0
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 2d9744eb17f969057956008d74a34adc27ee810f8a95e26547b2c8d8987bbe42f585ac6a1d033e341761245cd34c58a670155cfec01ee6ae3d29ed5c1531bc48
  languageName: node
  linkType: hard

"@aws-sdk/middleware-recursion-detection@npm:3.840.0":
  version: 3.840.0
  resolution: "@aws-sdk/middleware-recursion-detection@npm:3.840.0"
  dependencies:
    "@aws-sdk/types": 3.840.0
    "@smithy/protocol-http": ^5.1.2
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: aa8aed9a33edb472dceb5eca4f92af4db814415422282ed9910d60ac585c1e99eaf46fed9b5890d358cee65631708a22014ac558a9404c6bd6487387046e6886
  languageName: node
  linkType: hard

"@aws-sdk/middleware-user-agent@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/middleware-user-agent@npm:3.844.0"
  dependencies:
    "@aws-sdk/core": 3.844.0
    "@aws-sdk/types": 3.840.0
    "@aws-sdk/util-endpoints": 3.844.0
    "@smithy/core": ^3.7.0
    "@smithy/protocol-http": ^5.1.2
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: c693072fb6b63d88d80b6675ab195b47166d53bcd8ae94c1941d284c08271b1dc9903bc121df074da685e5c302d325572e7444974e15bc1d767e424a46dc8c1c
  languageName: node
  linkType: hard

"@aws-sdk/middleware-websocket@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/middleware-websocket@npm:3.844.0"
  dependencies:
    "@aws-sdk/types": 3.840.0
    "@aws-sdk/util-format-url": 3.840.0
    "@smithy/eventstream-codec": ^4.0.4
    "@smithy/eventstream-serde-browser": ^4.0.4
    "@smithy/fetch-http-handler": ^5.1.0
    "@smithy/protocol-http": ^5.1.2
    "@smithy/signature-v4": ^5.1.2
    "@smithy/types": ^4.3.1
    "@smithy/util-hex-encoding": ^4.0.0
    tslib: ^2.6.2
  checksum: 2d002587ff73d6cb15fe4fb215aed5ab5259d06d4f4b94b44ee1f3f8bb27bcc1890b8582a0b6bb14bf201650e474b333c888aa849f79f00ce723ed1aa056d8e2
  languageName: node
  linkType: hard

"@aws-sdk/nested-clients@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/nested-clients@npm:3.844.0"
  dependencies:
    "@aws-crypto/sha256-browser": 5.2.0
    "@aws-crypto/sha256-js": 5.2.0
    "@aws-sdk/core": 3.844.0
    "@aws-sdk/middleware-host-header": 3.840.0
    "@aws-sdk/middleware-logger": 3.840.0
    "@aws-sdk/middleware-recursion-detection": 3.840.0
    "@aws-sdk/middleware-user-agent": 3.844.0
    "@aws-sdk/region-config-resolver": 3.840.0
    "@aws-sdk/types": 3.840.0
    "@aws-sdk/util-endpoints": 3.844.0
    "@aws-sdk/util-user-agent-browser": 3.840.0
    "@aws-sdk/util-user-agent-node": 3.844.0
    "@smithy/config-resolver": ^4.1.4
    "@smithy/core": ^3.7.0
    "@smithy/fetch-http-handler": ^5.1.0
    "@smithy/hash-node": ^4.0.4
    "@smithy/invalid-dependency": ^4.0.4
    "@smithy/middleware-content-length": ^4.0.4
    "@smithy/middleware-endpoint": ^4.1.14
    "@smithy/middleware-retry": ^4.1.15
    "@smithy/middleware-serde": ^4.0.8
    "@smithy/middleware-stack": ^4.0.4
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/node-http-handler": ^4.1.0
    "@smithy/protocol-http": ^5.1.2
    "@smithy/smithy-client": ^4.4.6
    "@smithy/types": ^4.3.1
    "@smithy/url-parser": ^4.0.4
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-body-length-node": ^4.0.0
    "@smithy/util-defaults-mode-browser": ^4.0.22
    "@smithy/util-defaults-mode-node": ^4.0.22
    "@smithy/util-endpoints": ^3.0.6
    "@smithy/util-middleware": ^4.0.4
    "@smithy/util-retry": ^4.0.6
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: a64f94c2f07a919719c5f9e902874b35f96feef0657462a4f11238f9915c00cf395b4246ccc658684545327c1ad6fad8a89af4b8b69f8da51e9bd048af9129cc
  languageName: node
  linkType: hard

"@aws-sdk/region-config-resolver@npm:3.840.0":
  version: 3.840.0
  resolution: "@aws-sdk/region-config-resolver@npm:3.840.0"
  dependencies:
    "@aws-sdk/types": 3.840.0
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/types": ^4.3.1
    "@smithy/util-config-provider": ^4.0.0
    "@smithy/util-middleware": ^4.0.4
    tslib: ^2.6.2
  checksum: c0368460299c12da578f03cfcdfb3b0fe5f0c29103e4d49fa7b1323fc4ed6b8059801597d1b68b95967df92397cda8d02fe8326eaa31431c26e0ace30cb0d272
  languageName: node
  linkType: hard

"@aws-sdk/token-providers@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/token-providers@npm:3.844.0"
  dependencies:
    "@aws-sdk/core": 3.844.0
    "@aws-sdk/nested-clients": 3.844.0
    "@aws-sdk/types": 3.840.0
    "@smithy/property-provider": ^4.0.4
    "@smithy/shared-ini-file-loader": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: f7756f7213e52f7736a24e98a848036074d7f9be050fc79d8409cd93f0c8b1d0b44165a321c14a6ce147d94a9b0bfb5eac484f83a8a8385e646b282cdf1db847
  languageName: node
  linkType: hard

"@aws-sdk/types@npm:3.840.0, @aws-sdk/types@npm:^3.222.0":
  version: 3.840.0
  resolution: "@aws-sdk/types@npm:3.840.0"
  dependencies:
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 01c30bb35090b8105a120ac10bfb5adb291e2b07b15813eebc45a25e8febe79bb4c363600f52abd5348e73b5171611f5e7da8d7f7aeafb7cb3c7b22ac83a1cf8
  languageName: node
  linkType: hard

"@aws-sdk/util-endpoints@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/util-endpoints@npm:3.844.0"
  dependencies:
    "@aws-sdk/types": 3.840.0
    "@smithy/types": ^4.3.1
    "@smithy/url-parser": ^4.0.4
    "@smithy/util-endpoints": ^3.0.6
    tslib: ^2.6.2
  checksum: 8610a2c6c21be65ada7795c892f1c0ffabbe84537d02bb8df18f19373165089c10d82ef0bc4aa4c45179fa8a7d36544586635f762a0fce3af6bfe95d694ff938
  languageName: node
  linkType: hard

"@aws-sdk/util-format-url@npm:3.840.0":
  version: 3.840.0
  resolution: "@aws-sdk/util-format-url@npm:3.840.0"
  dependencies:
    "@aws-sdk/types": 3.840.0
    "@smithy/querystring-builder": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: e7b8573bb405fb0062030aa51ed1af0ffd81330fab32975eb9ac8f591161c2922f23d25afd8ac50a3d6772d7e68f7e4eca7018c0edf4f054a00f6e3ab659507a
  languageName: node
  linkType: hard

"@aws-sdk/util-locate-window@npm:^3.0.0":
  version: 3.804.0
  resolution: "@aws-sdk/util-locate-window@npm:3.804.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 87b384533ba5ceade6e212f5783b6134551ade3ecb413c93ea453c2d5af76651137c4dc7b270b643e8ac810b072119a273790046c31921aaf0f6a664d1a31c99
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-browser@npm:3.840.0":
  version: 3.840.0
  resolution: "@aws-sdk/util-user-agent-browser@npm:3.840.0"
  dependencies:
    "@aws-sdk/types": 3.840.0
    "@smithy/types": ^4.3.1
    bowser: ^2.11.0
    tslib: ^2.6.2
  checksum: eb99a07b7d96f0555aca25f11cd9e2f579e149d102cc78300c47cc0031a40e7ea1d559bfe15b47bccd675d33fe56ee8e4855198d8eb2fb6e9bb6517e10f39700
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-node@npm:3.844.0":
  version: 3.844.0
  resolution: "@aws-sdk/util-user-agent-node@npm:3.844.0"
  dependencies:
    "@aws-sdk/middleware-user-agent": 3.844.0
    "@aws-sdk/types": 3.840.0
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  peerDependencies:
    aws-crt: ">=1.0.0"
  peerDependenciesMeta:
    aws-crt:
      optional: true
  checksum: 847ae34d65bd4ccefd4ff0ac1ba9c078f73d8f333003053a23b81348d80f7c25ae68344120f73bf5d1ea0c2c8be7ae743313bb3f089b0de79c7ec1b58c674965
  languageName: node
  linkType: hard

"@aws-sdk/xml-builder@npm:3.821.0":
  version: 3.821.0
  resolution: "@aws-sdk/xml-builder@npm:3.821.0"
  dependencies:
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: f6ee1e5f5336afeb72e2b5e712593d1dcaa626729d0a12941c32e14136308b8729b225d4c75e7b6606bc17eeb79ea28076212aced93cc6460fefb9b712b07e28
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": ^7.27.1
    js-tokens: ^4.0.0
    picocolors: ^1.1.1
  checksum: 5874edc5d37406c4a0bb14cf79c8e51ad412fb0423d176775ac14fc0259831be1bf95bdda9c2aa651126990505e09a9f0ed85deaa99893bc316d2682c5115bdc
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.27.2":
  version: 7.28.0
  resolution: "@babel/compat-data@npm:7.28.0"
  checksum: 37a40d4ea10a32783bc24c4ad374200f5db864c8dfa42f82e76f02b8e84e4c65e6a017fc014d165b08833f89333dff4cb635fce30f03c333ea3525ea7e20f0a2
  languageName: node
  linkType: hard

"@babel/core@npm:^7.27.4":
  version: 7.28.0
  resolution: "@babel/core@npm:7.28.0"
  dependencies:
    "@ampproject/remapping": ^2.2.0
    "@babel/code-frame": ^7.27.1
    "@babel/generator": ^7.28.0
    "@babel/helper-compilation-targets": ^7.27.2
    "@babel/helper-module-transforms": ^7.27.3
    "@babel/helpers": ^7.27.6
    "@babel/parser": ^7.28.0
    "@babel/template": ^7.27.2
    "@babel/traverse": ^7.28.0
    "@babel/types": ^7.28.0
    convert-source-map: ^2.0.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.2.3
    semver: ^6.3.1
  checksum: 86da9e26c96e22d96deca0509969d273476f61c30464f262dec5e5a163422e07d5ab690ed54619d10fcab784abd10567022ce3d90f175b40279874f5288215e3
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/generator@npm:7.28.0"
  dependencies:
    "@babel/parser": ^7.28.0
    "@babel/types": ^7.28.0
    "@jridgewell/gen-mapping": ^0.3.12
    "@jridgewell/trace-mapping": ^0.3.28
    jsesc: ^3.0.2
  checksum: 3fc9ecca7e7a617cf7b7357e11975ddfaba4261f374ab915f5d9f3b1ddc8fd58da9f39492396416eb08cf61972d1aa13c92d4cca206533c553d8651c2740f07f
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": ^7.27.2
    "@babel/helper-validator-option": ^7.27.1
    browserslist: ^4.24.0
    lru-cache: ^5.1.1
    semver: ^6.3.1
  checksum: 7b95328237de85d7af1dea010a4daa28e79f961dda48b652860d5893ce9b136fc8b9ea1f126d8e0a24963b09ba5c6631dcb907b4ce109b04452d34a6ae979807
  languageName: node
  linkType: hard

"@babel/helper-globals@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/helper-globals@npm:7.28.0"
  checksum: d8d7b91c12dad1ee747968af0cb73baf91053b2bcf78634da2c2c4991fb45ede9bd0c8f9b5f3254881242bc0921218fcb7c28ae885477c25177147e978ce4397
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": ^7.27.1
    "@babel/types": ^7.27.1
  checksum: 92d01c71c0e4aacdc2babce418a9a1a27a8f7d770a210ffa0f3933f321befab18b655bc1241bebc40767516731de0b85639140c42e45a8210abe1e792f115b28
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-module-transforms@npm:7.27.3"
  dependencies:
    "@babel/helper-module-imports": ^7.27.1
    "@babel/helper-validator-identifier": ^7.27.1
    "@babel/traverse": ^7.27.3
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: c611d42d3cb7ba23b1a864fcf8d6cde0dc99e876ca1c9a67e4d7919a70706ded4aaa45420de2bf7f7ea171e078e59f0edcfa15a56d74b9485e151b95b93b946e
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 5d715055301badab62bdb2336075a77f8dc8bd290cad2bc1b37ea3bf1b3efc40594d308082229f239deb4d6b5b80b0a73bce000e595ea74416e0339c11037047
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 0a8464adc4b39b138aedcb443b09f4005d86207d7126e5e079177e05c3116107d856ec08282b365e9a79a9872f40f4092a6127f8d74c8a01c1ef789dacfc25d6
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 3c7e8391e59d6c85baeefe9afb86432f2ab821c6232b00ea9082a51d3e7e95a2f3fb083d74dc1f49ac82cf238e1d2295dafcb001f7b0fab479f3f56af5eaaa47
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: db73e6a308092531c629ee5de7f0d04390835b21a263be2644276cb27da2384b64676cab9f22cd8d8dbd854c92b1d7d56fc8517cf0070c35d1c14a8c828b0903
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.6":
  version: 7.27.6
  resolution: "@babel/helpers@npm:7.27.6"
  dependencies:
    "@babel/template": ^7.27.2
    "@babel/types": ^7.27.6
  checksum: 12f96a5800ff677481dbc0a022c617303e945210cac4821ad5377a31201ffd8d9c4d00f039ed1487cf2a3d15868fb2d6cabecdb1aba334bd40a846f1938053a2
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.25.9, @babel/parser@npm:^7.27.2, @babel/parser@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/parser@npm:7.28.0"
  dependencies:
    "@babel/types": ^7.28.0
  bin:
    parser: ./bin/babel-parser.js
  checksum: 718e4ce9b0914701d6f74af610d3e7d52b355ef1dcf34a7dedc5930e96579e387f04f96187e308e601828b900b8e4e66d2fe85023beba2ac46587023c45b01cf
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-self@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-self@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 72cbae66a58c6c36f7e12e8ed79f292192d858dd4bb00e9e89d8b695e4c5cb6ef48eec84bffff421a5db93fd10412c581f1cccdb00264065df76f121995bdb68
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-source@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-source@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.27.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e2843362adb53692be5ee9fa07a386d2d8883daad2063a3575b3c373fc14cdf4ea7978c67a183cb631b4c9c8d77b2f48c24c088f8e65cc3600cb8e97d72a7161
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.5.5, @babel/runtime@npm:^7.8.7":
  version: 7.27.6
  resolution: "@babel/runtime@npm:7.27.6"
  checksum: 3f7b879df1823c0926bd5dbc941c62f5d60faa790c1aab9758c04799e1f04ee8d93553be9ec059d4e5882f19fe03cbe8933ee4f46212dced0f6d8205992c9c9a
  languageName: node
  linkType: hard

"@babel/template@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": ^7.27.1
    "@babel/parser": ^7.27.2
    "@babel/types": ^7.27.1
  checksum: ff5628bc066060624afd970616090e5bba91c6240c2e4b458d13267a523572cbfcbf549391eec8217b94b064cf96571c6273f0c04b28a8567b96edc675c28e27
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.3, @babel/traverse@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/traverse@npm:7.28.0"
  dependencies:
    "@babel/code-frame": ^7.27.1
    "@babel/generator": ^7.28.0
    "@babel/helper-globals": ^7.28.0
    "@babel/parser": ^7.28.0
    "@babel/template": ^7.27.2
    "@babel/types": ^7.28.0
    debug: ^4.3.1
  checksum: f1b6ed2a37f593ee02db82521f8d54c8540a7ec2735c6c127ba687de306d62ac5a7c6471819783128e0b825c4f7e374206ebbd1daf00d07f05a4528f5b1b4c07
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.25.8, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.6, @babel/types@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/types@npm:7.28.0"
  dependencies:
    "@babel/helper-string-parser": ^7.27.1
    "@babel/helper-validator-identifier": ^7.27.1
  checksum: 3cb33bbe79e9629c3e4ed1592340f936481e7aef2c3df11f8b1f91e54b45e89b3ad92f2d20f8acdb5a7e00157174ffe8b1d174069bb839303e7f39f579d60969
  languageName: node
  linkType: hard

"@develar/schema-utils@npm:~2.6.5":
  version: 2.6.5
  resolution: "@develar/schema-utils@npm:2.6.5"
  dependencies:
    ajv: ^6.12.0
    ajv-keywords: ^3.4.1
  checksum: e1c3771af7fb934a0a985c31b901ece41a3015ef352b58e8e1c4bce691fe5792ebb65712e43ec70fa91a8fa0c929ccacf6b52c8f8de0fd83681db2cbeb62d143
  languageName: node
  linkType: hard

"@electron/asar@npm:3.2.18":
  version: 3.2.18
  resolution: "@electron/asar@npm:3.2.18"
  dependencies:
    commander: ^5.0.0
    glob: ^7.1.6
    minimatch: ^3.0.4
  bin:
    asar: bin/asar.js
  checksum: bbb983d9395d92a6cb7405435d734e465f13346b78b398ba787ed04b8d626b634eecaadfa04f43a73ba5883613acb96807869b31953304038352c8b9d4197532
  languageName: node
  linkType: hard

"@electron/asar@npm:^3.2.7":
  version: 3.4.1
  resolution: "@electron/asar@npm:3.4.1"
  dependencies:
    commander: ^5.0.0
    glob: ^7.1.6
    minimatch: ^3.0.4
  bin:
    asar: bin/asar.js
  checksum: 3eaf9368c6b553679714936f85a7eeef6b6b978dc19d88d01285ad37edc2f820181fc372bcbc32e4f0fcf26c12b1c8ecbff45f921f73f053b0b1a3275b442e92
  languageName: node
  linkType: hard

"@electron/asar@npm:^4.0.0, @electron/asar@npm:latest":
  version: 4.0.0
  resolution: "@electron/asar@npm:4.0.0"
  dependencies:
    commander: ^13.1.0
    glob: ^11.0.1
    minimatch: ^10.0.1
  bin:
    asar: bin/asar.mjs
  checksum: 75c0edf25cc69b7605fd23fb3f080c40ab5c8f17d6ce88327b64a043d27d47722a9ed2cdd4e2d5be65dad2bd0c6a4e1610b6a65bca7464110f90ac49d1db6f65
  languageName: node
  linkType: hard

"@electron/fuses@npm:^1.8.0":
  version: 1.8.0
  resolution: "@electron/fuses@npm:1.8.0"
  dependencies:
    chalk: ^4.1.1
    fs-extra: ^9.0.1
    minimist: ^1.2.5
  bin:
    electron-fuses: dist/bin.js
  checksum: 94cb8ba10d69c0750401fc29cde38215c2b1934014008e98c231f16e78a653209b716a56223a09063e7b6a8526cfeed9223cd8d95559ff4a244d1c3783dec23f
  languageName: node
  linkType: hard

"@electron/get@npm:^2.0.0":
  version: 2.0.3
  resolution: "@electron/get@npm:2.0.3"
  dependencies:
    debug: ^4.1.1
    env-paths: ^2.2.0
    fs-extra: ^8.1.0
    global-agent: ^3.0.0
    got: ^11.8.5
    progress: ^2.0.3
    semver: ^6.2.0
    sumchecker: ^3.0.1
  dependenciesMeta:
    global-agent:
      optional: true
  checksum: 98f7713e1dda6d1b9d1598890e4e12e38e2d2cb7634e44c31bd494c60a1e97583cdfe4a38408985daaa8deee0a1ea3b6b1add3520874bdb00b6bffba86e7e30d
  languageName: node
  linkType: hard

"@electron/node-gyp@git+https://github.com/electron/node-gyp.git#06b29aafb7708acef8b3669835c8a7857ebc92d2":
  version: 10.2.0-electron.1
  resolution: "@electron/node-gyp@https://github.com/electron/node-gyp.git#commit=06b29aafb7708acef8b3669835c8a7857ebc92d2"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    glob: ^8.1.0
    graceful-fs: ^4.2.6
    make-fetch-happen: ^10.2.1
    nopt: ^6.0.0
    proc-log: ^2.0.1
    semver: ^7.3.5
    tar: ^6.2.1
    which: ^2.0.2
  bin:
    node-gyp: ./bin/node-gyp.js
  checksum: c3a5c3b3a69286db9beceb5beb5141e37e2ccc2dc5f0dffbf53b623c8a6b6e1a762939d143bb2e38b7323b2db8f97f2777309e1cd6351c37b7b2e00ad7797576
  languageName: node
  linkType: hard

"@electron/notarize@npm:2.5.0":
  version: 2.5.0
  resolution: "@electron/notarize@npm:2.5.0"
  dependencies:
    debug: ^4.1.1
    fs-extra: ^9.0.1
    promise-retry: ^2.0.1
  checksum: b8935a9648ae53429d6d476b473ae180d76ad281ccd34318ddc45c030c112501b966cd2609f00b74d0127f6f77dec8de134c2b8bbe356da9a84cfa3fea2d06ab
  languageName: node
  linkType: hard

"@electron/osx-sign@npm:1.3.1":
  version: 1.3.1
  resolution: "@electron/osx-sign@npm:1.3.1"
  dependencies:
    compare-version: ^0.1.2
    debug: ^4.3.4
    fs-extra: ^10.0.0
    isbinaryfile: ^4.0.8
    minimist: ^1.2.6
    plist: ^3.0.5
  bin:
    electron-osx-flat: bin/electron-osx-flat.js
    electron-osx-sign: bin/electron-osx-sign.js
  checksum: b35f3ee3220473b3578c5e7b279b3e0596d3161e19cede6ff283dcb1ba92183982fbd8d9c31929ecc3d1b57aa2e7cb787a9c0fce1c82f3a24a612bb3280033a0
  languageName: node
  linkType: hard

"@electron/rebuild@npm:3.7.0":
  version: 3.7.0
  resolution: "@electron/rebuild@npm:3.7.0"
  dependencies:
    "@electron/node-gyp": "git+https://github.com/electron/node-gyp.git#06b29aafb7708acef8b3669835c8a7857ebc92d2"
    "@malept/cross-spawn-promise": ^2.0.0
    chalk: ^4.0.0
    debug: ^4.1.1
    detect-libc: ^2.0.1
    fs-extra: ^10.0.0
    got: ^11.7.0
    node-abi: ^3.45.0
    node-api-version: ^0.2.0
    node-gyp: latest
    ora: ^5.1.0
    read-binary-file-arch: ^1.0.6
    semver: ^7.3.5
    tar: ^6.0.5
    yargs: ^17.0.1
  bin:
    electron-rebuild: lib/cli.js
  checksum: b51e4782032e7ae3bc4bef581a257efff4f4afad833d30b22dbbe5d11c727817748aeb7032cdf12c6edf3613108654fde0c777a37dbeb156d1301683893e5400
  languageName: node
  linkType: hard

"@electron/rebuild@npm:^4.0.1":
  version: 4.0.1
  resolution: "@electron/rebuild@npm:4.0.1"
  dependencies:
    "@malept/cross-spawn-promise": ^2.0.0
    chalk: ^4.0.0
    debug: ^4.1.1
    detect-libc: ^2.0.1
    got: ^11.7.0
    graceful-fs: ^4.2.11
    node-abi: ^4.2.0
    node-api-version: ^0.2.1
    node-gyp: ^11.2.0
    ora: ^5.1.0
    read-binary-file-arch: ^1.0.6
    semver: ^7.3.5
    tar: ^6.0.5
    yargs: ^17.0.1
  bin:
    electron-rebuild: lib/cli.js
  checksum: bb23197ad974e0f0e8f6f6e41f71e3c7aa18793d10094ca2ec13f6c25b4d57fb0dee9b5551d97ba61dca972b0cae025a19f93224bac083464b6764c9d0b4679d
  languageName: node
  linkType: hard

"@electron/universal@npm:2.0.1":
  version: 2.0.1
  resolution: "@electron/universal@npm:2.0.1"
  dependencies:
    "@electron/asar": ^3.2.7
    "@malept/cross-spawn-promise": ^2.0.0
    debug: ^4.3.1
    dir-compare: ^4.2.0
    fs-extra: ^11.1.1
    minimatch: ^9.0.3
    plist: ^3.1.0
  checksum: adbfcc4306d39dcbff97030f86c96559c17cc76858a93a598dce7d1f7a735c71379e74a74baf4dea35a4bda959e6b42356886e8765f8e6ff7d43f92ab846f316
  languageName: node
  linkType: hard

"@electron/universal@npm:^3.0.0":
  version: 3.0.0
  resolution: "@electron/universal@npm:3.0.0"
  dependencies:
    "@electron/asar": ^4.0.0
    "@malept/cross-spawn-promise": ^2.0.0
    debug: ^4.3.1
    dir-compare: ^4.2.0
    minimatch: ^9.0.3
    plist: ^3.1.0
  checksum: 5561119d71cf06b9348c81f132d43eccc750931cece9d4339da72619f9433eba967da2b4a6a3bd62bc367c7e8a5da6b794a2b7f715d62816c9313cd8f60cc291
  languageName: node
  linkType: hard

"@emnapi/runtime@npm:^1.4.3":
  version: 1.4.4
  resolution: "@emnapi/runtime@npm:1.4.4"
  dependencies:
    tslib: ^2.4.0
  checksum: 49490b2630d258401af9d2fc6cfc4d302fe92e1557761380a9ce495a2d78aea67fb4dcb3f523eee396b24896548bce2b9d9e4cea01b62730cf527a47a52189da
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/aix-ppc64@npm:0.25.6"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/android-arm64@npm:0.25.6"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/android-arm@npm:0.25.6"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/android-x64@npm:0.25.6"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/darwin-arm64@npm:0.25.6"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/darwin-x64@npm:0.25.6"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/freebsd-arm64@npm:0.25.6"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/freebsd-x64@npm:0.25.6"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/linux-arm64@npm:0.25.6"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/linux-arm@npm:0.25.6"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/linux-ia32@npm:0.25.6"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/linux-loong64@npm:0.25.6"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/linux-mips64el@npm:0.25.6"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/linux-ppc64@npm:0.25.6"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/linux-riscv64@npm:0.25.6"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/linux-s390x@npm:0.25.6"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/linux-x64@npm:0.25.6"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/netbsd-arm64@npm:0.25.6"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/netbsd-x64@npm:0.25.6"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/openbsd-arm64@npm:0.25.6"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/openbsd-x64@npm:0.25.6"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openharmony-arm64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/openharmony-arm64@npm:0.25.6"
  conditions: os=openharmony & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/sunos-x64@npm:0.25.6"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/win32-arm64@npm:0.25.6"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/win32-ia32@npm:0.25.6"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.25.6":
  version: 0.25.6
  resolution: "@esbuild/win32-x64@npm:0.25.6"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.7.0":
  version: 4.7.0
  resolution: "@eslint-community/eslint-utils@npm:4.7.0"
  dependencies:
    eslint-visitor-keys: ^3.4.3
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: b177e3b75c0b8d0e5d71f1c532edb7e40b31313db61f0c879f9bf19c3abb2783c6c372b5deb2396dab4432f2946b9972122ac682e77010376c029dfd0149c681
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 0d628680e204bc316d545b4993d3658427ca404ae646ce541fcc65306b8c712c340e5e573e30fb9f85f4855c0c5f6dca9868931f2fcced06417fbe1a0c6cd2d6
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.21.0":
  version: 0.21.0
  resolution: "@eslint/config-array@npm:0.21.0"
  dependencies:
    "@eslint/object-schema": ^2.1.6
    debug: ^4.3.1
    minimatch: ^3.1.2
  checksum: 84d3ae7cb755af94dc158a74389f4c560757b13f2bb908f598f927b87b70a38e8152015ea2e9557c1b4afc5130ee1356f6cad682050d67aae0468bbef98bc3a8
  languageName: node
  linkType: hard

"@eslint/config-helpers@npm:^0.3.0":
  version: 0.3.0
  resolution: "@eslint/config-helpers@npm:0.3.0"
  checksum: d4fe8242ef580806ddaa88309f4bb2d3e6be5524cc6d6197675106c6d048f766a3f9cdc2e8e33bbc97a123065792cac8314fc85ac2b3cf72610e8df59301d63a
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.14.0":
  version: 0.14.0
  resolution: "@eslint/core@npm:0.14.0"
  dependencies:
    "@types/json-schema": ^7.0.15
  checksum: d68b8282b6f38c5145234f812f18f491d12d716240875591bd54bf5ac32858d979bdf6d38e521997a6e01f2c4223a3e66049714151da7278d0a95ff15b5d46c8
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.15.1":
  version: 0.15.1
  resolution: "@eslint/core@npm:0.15.1"
  dependencies:
    "@types/json-schema": ^7.0.15
  checksum: 9215f00466d60764453466604443a491b0ea8263c148836fef723354d6ef1d550991e931d3df2780c99cee2cab14c4f41f97d5341ab12a8443236c961bb6f664
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.3.1":
  version: 3.3.1
  resolution: "@eslint/eslintrc@npm:3.3.1"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.3.2
    espree: ^10.0.1
    globals: ^14.0.0
    ignore: ^5.2.0
    import-fresh: ^3.2.1
    js-yaml: ^4.1.0
    minimatch: ^3.1.2
    strip-json-comments: ^3.1.1
  checksum: 8241f998f0857abf5a615072273b90b1244d75c1c45d217c6a8eb444c6e12bbb5506b4879c14fb262eb72b7d8e3d2f0542da2db1a7f414a12496ebb790fb4d62
  languageName: node
  linkType: hard

"@eslint/js@npm:9.30.1, @eslint/js@npm:^9.25.0":
  version: 9.30.1
  resolution: "@eslint/js@npm:9.30.1"
  checksum: 596adcd4336f098121b4f3f336169dabe86ca8d34b9fb4e30c9c44ccbb10def931bdbbd92cd910776c4030a05ae614fbc89fc8d09f69f5bad2795cd7157678e8
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.6":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: e32e565319f6544d36d3fa69a3e163120722d12d666d1a4525c9a6f02e9b54c29d9b1f03139e25d7e759e08dda8da433590bc23c09db8d511162157ef1b86a4c
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.3.1":
  version: 0.3.3
  resolution: "@eslint/plugin-kit@npm:0.3.3"
  dependencies:
    "@eslint/core": ^0.15.1
    levn: ^0.4.1
  checksum: c9dc7b83ed011dce35ccc66dc53aaaa87e9fb2bd7c8a11231f7624334d82c9a53552e4b1a1cb60b74073fcc49a2661be874e503aae14cf2f6ac6b1c7faeb7080
  languageName: node
  linkType: hard

"@floating-ui/core@npm:^1.7.2":
  version: 1.7.2
  resolution: "@floating-ui/core@npm:1.7.2"
  dependencies:
    "@floating-ui/utils": ^0.2.10
  checksum: aea540ea0101daf83e5beb2769af81f0532dcb8514dbee9d4c0a06576377d56dbfd4e5c3b031359594a26649734d1145bbd5524e8a573a745a5fcadc6b307906
  languageName: node
  linkType: hard

"@floating-ui/dom@npm:^1.7.2":
  version: 1.7.2
  resolution: "@floating-ui/dom@npm:1.7.2"
  dependencies:
    "@floating-ui/core": ^1.7.2
    "@floating-ui/utils": ^0.2.10
  checksum: 232d6668693cfecec038f3fb1f5398eace340427a9108701b895136c18d303d8bdd6237dce224626abf56a5a4592db776fbd0d187aa0f72c729bfca14a474b65
  languageName: node
  linkType: hard

"@floating-ui/react-dom@npm:^2.0.0":
  version: 2.1.4
  resolution: "@floating-ui/react-dom@npm:2.1.4"
  dependencies:
    "@floating-ui/dom": ^1.7.2
  peerDependencies:
    react: ">=16.8.0"
    react-dom: ">=16.8.0"
  checksum: be2cc094b0c5cd7f6a06c6c58944e4f070e231bdc8d9f84fc8246eedf91e1545a8a9e6d8560664054de126aae6520da57a30b7d81433cb2625641a08eea8f029
  languageName: node
  linkType: hard

"@floating-ui/utils@npm:^0.2.10":
  version: 0.2.10
  resolution: "@floating-ui/utils@npm:0.2.10"
  checksum: ffc4c24a46a665cfd0337e9aaf7de8415b572f8a0f323af39175e4b575582aed13d172e7f049eedeece9eaf022bad019c140a2d192580451984ae529bdf1285c
  languageName: node
  linkType: hard

"@gar/promisify@npm:^1.0.1, @gar/promisify@npm:^1.1.3":
  version: 1.1.3
  resolution: "@gar/promisify@npm:1.1.3"
  checksum: 4059f790e2d07bf3c3ff3e0fec0daa8144fe35c1f6e0111c9921bd32106adaa97a4ab096ad7dab1e28ee6a9060083c4d1a4ada42a7f5f3f7a96b8812e2b757c1
  languageName: node
  linkType: hard

"@hookform/resolvers@npm:^3.9.0":
  version: 3.10.0
  resolution: "@hookform/resolvers@npm:3.10.0"
  peerDependencies:
    react-hook-form: ^7.0.0
  checksum: e062f10bebff696a669fe37388f64f6b7c322eb524e99008c0e157e7adf68d2cee4d86fce1a7304a80d7f6fdd6d92facc37320a539cb195235a31a680415760c
  languageName: node
  linkType: hard

"@huggingface/jinja@npm:^0.2.2":
  version: 0.2.2
  resolution: "@huggingface/jinja@npm:0.2.2"
  checksum: 8a6e3e287863d437920990afa2ca25d83c51997bd5ba0325ea90633e52469c2d901178cbd758cc362b45ad1c9521fccf372884fd59e58d2916d6b2e5bb15f776
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 611e0545146f55ddfdd5c20239cfb7911f9d0e28258787c4fc1a1f6214250830c9367aaaeace0096ed90b6739bee1e9c52ad5ba8adaf74ab8b449119303babfe
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": ^0.19.1
    "@humanwhocodes/retry": ^0.3.0
  checksum: f9cb52bb235f8b9c6fcff43a7e500669a38f8d6ce26593404a9b56365a1644e0ed60c720dc65ff6a696b1f85f3563ab055bb554ec8674f2559085ba840e47710
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 0fd22007db8034a2cdf2c764b140d37d9020bbfce8a49d3ec5c05290e77d4b0263b1b972b752df8c89e5eaa94073408f2b7d977aed131faf6cf396ebb5d7fb61
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 7e5517bb51dbea3e02ab6cacef59a8f4b0ca023fc4b0b8cbc40de0ad29f46edd50b897c6e7fba79366a0217e3f48e2da8975056f6c35cfe19d9cc48f1d03c1dd
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.2":
  version: 0.4.3
  resolution: "@humanwhocodes/retry@npm:0.4.3"
  checksum: d423455b9d53cf01f778603404512a4246fb19b83e74fe3e28c70d9a80e9d4ae147d2411628907ca983e91a855a52535859a8bb218050bc3f6dbd7a553b7b442
  languageName: node
  linkType: hard

"@img/sharp-darwin-arm64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-darwin-arm64@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-darwin-arm64": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-darwin-arm64":
      optional: true
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-darwin-x64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-darwin-x64@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-darwin-x64": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-darwin-x64":
      optional: true
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-arm64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-darwin-arm64@npm:1.1.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-x64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-darwin-x64@npm:1.1.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-arm64@npm:1.1.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-arm@npm:1.1.0"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-ppc64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-ppc64@npm:1.1.0"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-s390x@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-s390x@npm:1.1.0"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-x64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-x64@npm:1.1.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-arm64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linuxmusl-arm64@npm:1.1.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-x64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linuxmusl-x64@npm:1.1.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linux-arm64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-linux-arm64@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-linux-arm64": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-arm@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-linux-arm@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-linux-arm": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm":
      optional: true
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-s390x@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-linux-s390x@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-linux-s390x": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-linux-s390x":
      optional: true
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-x64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-linux-x64@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-linux-x64": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-linux-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-arm64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-linuxmusl-arm64@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-linuxmusl-arm64": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-x64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-linuxmusl-x64@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-linuxmusl-x64": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-wasm32@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-wasm32@npm:0.34.2"
  dependencies:
    "@emnapi/runtime": ^1.4.3
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@img/sharp-win32-arm64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-win32-arm64@npm:0.34.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-win32-ia32@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-win32-ia32@npm:0.34.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@img/sharp-win32-x64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-win32-x64@npm:0.34.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@isaacs/balanced-match@npm:^4.0.1":
  version: 4.0.1
  resolution: "@isaacs/balanced-match@npm:4.0.1"
  checksum: 102fbc6d2c0d5edf8f6dbf2b3feb21695a21bc850f11bc47c4f06aa83bd8884fde3fe9d6d797d619901d96865fdcb4569ac2a54c937992c48885c5e3d9967fe8
  languageName: node
  linkType: hard

"@isaacs/brace-expansion@npm:^5.0.0":
  version: 5.0.0
  resolution: "@isaacs/brace-expansion@npm:5.0.0"
  dependencies:
    "@isaacs/balanced-match": ^4.0.1
  checksum: d7a3b8b0ddbf0ccd8eeb1300e29dd0a0c02147e823d8138f248375a365682360620895c66d113e05ee02389318c654379b0e538b996345b83c914941786705b1
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: ^7.0.4
  checksum: 5d36d289960e886484362d9eb6a51d1ea28baed5f5d0140bbe62b99bac52eaf06cc01c2bc0d3575977962f84f6b2c4387b043ee632216643d4787b0999465bf2
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.12, @jridgewell/gen-mapping@npm:^0.3.2, @jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.12
  resolution: "@jridgewell/gen-mapping@npm:0.3.12"
  dependencies:
    "@jridgewell/sourcemap-codec": ^1.5.0
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: 56ee1631945084897f274e65348afbaca7970ce92e3c23b3a23b2fe5d0d2f0c67614f0df0f2bb070e585e944bbaaf0c11cee3a36318ab8a36af46f2fd566bc40
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 83b85f72c59d1c080b4cbec0fef84528963a1b5db34e4370fa4bd1e3ff64a0d80e0cee7369d11d73c704e0286fb2865b530acac7a871088fbe92b5edf1000870
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.4
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.4"
  checksum: 959093724bfbc7c1c9aadc08066154f5c1f2acc647b45bd59beec46922cbfc6a9eda4a2114656de5bc00bb3600e420ea9a4cb05e68dcf388619f573b77bd9f0c
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.28":
  version: 0.3.29
  resolution: "@jridgewell/trace-mapping@npm:0.3.29"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 5e92eeafa5131a4f6b7122063833d657f885cb581c812da54f705d7a599ff36a75a4a093a83b0f6c7e95642f5772dd94753f696915e8afea082237abf7423ca3
  languageName: node
  linkType: hard

"@lancedb/lancedb-darwin-arm64@npm:0.20.0":
  version: 0.20.0
  resolution: "@lancedb/lancedb-darwin-arm64@npm:0.20.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@lancedb/lancedb-darwin-x64@npm:0.20.0":
  version: 0.20.0
  resolution: "@lancedb/lancedb-darwin-x64@npm:0.20.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@lancedb/lancedb-linux-arm64-gnu@npm:0.20.0":
  version: 0.20.0
  resolution: "@lancedb/lancedb-linux-arm64-gnu@npm:0.20.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@lancedb/lancedb-linux-arm64-musl@npm:0.20.0":
  version: 0.20.0
  resolution: "@lancedb/lancedb-linux-arm64-musl@npm:0.20.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@lancedb/lancedb-linux-x64-gnu@npm:0.20.0":
  version: 0.20.0
  resolution: "@lancedb/lancedb-linux-x64-gnu@npm:0.20.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@lancedb/lancedb-linux-x64-musl@npm:0.20.0":
  version: 0.20.0
  resolution: "@lancedb/lancedb-linux-x64-musl@npm:0.20.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@lancedb/lancedb-win32-arm64-msvc@npm:0.20.0":
  version: 0.20.0
  resolution: "@lancedb/lancedb-win32-arm64-msvc@npm:0.20.0"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@lancedb/lancedb-win32-x64-msvc@npm:0.20.0":
  version: 0.20.0
  resolution: "@lancedb/lancedb-win32-x64-msvc@npm:0.20.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@lancedb/lancedb@npm:^0.20.0":
  version: 0.20.0
  resolution: "@lancedb/lancedb@npm:0.20.0"
  dependencies:
    "@lancedb/lancedb-darwin-arm64": 0.20.0
    "@lancedb/lancedb-darwin-x64": 0.20.0
    "@lancedb/lancedb-linux-arm64-gnu": 0.20.0
    "@lancedb/lancedb-linux-arm64-musl": 0.20.0
    "@lancedb/lancedb-linux-x64-gnu": 0.20.0
    "@lancedb/lancedb-linux-x64-musl": 0.20.0
    "@lancedb/lancedb-win32-arm64-msvc": 0.20.0
    "@lancedb/lancedb-win32-x64-msvc": 0.20.0
    reflect-metadata: ^0.2.2
  peerDependencies:
    apache-arrow: ">=15.0.0 <=18.1.0"
  dependenciesMeta:
    "@lancedb/lancedb-darwin-arm64":
      optional: true
    "@lancedb/lancedb-darwin-x64":
      optional: true
    "@lancedb/lancedb-linux-arm64-gnu":
      optional: true
    "@lancedb/lancedb-linux-arm64-musl":
      optional: true
    "@lancedb/lancedb-linux-x64-gnu":
      optional: true
    "@lancedb/lancedb-linux-x64-musl":
      optional: true
    "@lancedb/lancedb-win32-arm64-msvc":
      optional: true
    "@lancedb/lancedb-win32-x64-msvc":
      optional: true
  conditions: (os=darwin | os=linux | os=win32) & (cpu=x64 | cpu=arm64)
  languageName: node
  linkType: hard

"@malept/cross-spawn-promise@npm:^2.0.0":
  version: 2.0.0
  resolution: "@malept/cross-spawn-promise@npm:2.0.0"
  dependencies:
    cross-spawn: ^7.0.1
  checksum: 9016a6674842c161b6949d7876e655874ca2d7f6a4fd88a73147d2abde0dcb3981c5dd9714e721e40f92e953ba16e18d7ee3fc94e8b1aae9b5922c582cd320da
  languageName: node
  linkType: hard

"@malept/flatpak-bundler@npm:^0.4.0":
  version: 0.4.0
  resolution: "@malept/flatpak-bundler@npm:0.4.0"
  dependencies:
    debug: ^4.1.1
    fs-extra: ^9.0.0
    lodash: ^4.17.15
    tmp-promise: ^3.0.2
  checksum: 12527e42c2865504eb2a91cc419e52dd7a68c1eda1138c0713a1520a5413ef9dabfa9d21b7908d211998b75c60035d1d5ae87c00fe8ff5be8fa8449525235dd5
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: ^7.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.1
    lru-cache: ^10.0.1
    socks-proxy-agent: ^8.0.3
  checksum: e8fc25d536250ed3e669813b36e8c6d805628b472353c57afd8c4fde0fcfcf3dda4ffe22f7af8c9070812ec2e7a03fb41d7151547cef3508efe661a5a3add20f
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^1.0.0":
  version: 1.1.1
  resolution: "@npmcli/fs@npm:1.1.1"
  dependencies:
    "@gar/promisify": ^1.0.1
    semver: ^7.3.5
  checksum: f5ad92f157ed222e4e31c352333d0901df02c7c04311e42a81d8eb555d4ec4276ea9c635011757de20cc476755af33e91622838de573b17e52e2e7703f0a9965
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^2.1.0":
  version: 2.1.2
  resolution: "@npmcli/fs@npm:2.1.2"
  dependencies:
    "@gar/promisify": ^1.1.3
    semver: ^7.3.5
  checksum: 405074965e72d4c9d728931b64d2d38e6ea12066d4fad651ac253d175e413c06fe4350970c783db0d749181da8fe49c42d3880bd1cbc12cd68e3a7964d820225
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: ^7.3.5
  checksum: 68951c589e9a4328698a35fd82fe71909a257d6f2ede0434d236fa55634f0fbcad9bb8755553ce5849bd25ee6f019f4d435921ac715c853582c4a7f5983c8d4a
  languageName: node
  linkType: hard

"@npmcli/move-file@npm:^1.0.1":
  version: 1.1.2
  resolution: "@npmcli/move-file@npm:1.1.2"
  dependencies:
    mkdirp: ^1.0.4
    rimraf: ^3.0.2
  checksum: c96381d4a37448ea280951e46233f7e541058cf57a57d4094dd4bdcaae43fa5872b5f2eb6bfb004591a68e29c5877abe3cdc210cb3588cbf20ab2877f31a7de7
  languageName: node
  linkType: hard

"@npmcli/move-file@npm:^2.0.0":
  version: 2.0.1
  resolution: "@npmcli/move-file@npm:2.0.1"
  dependencies:
    mkdirp: ^1.0.4
    rimraf: ^3.0.2
  checksum: 52dc02259d98da517fae4cb3a0a3850227bdae4939dda1980b788a7670636ca2b4a01b58df03dd5f65c1e3cb70c50fa8ce5762b582b3f499ec30ee5ce1fd9380
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@protobufjs/aspromise@npm:^1.1.1, @protobufjs/aspromise@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/aspromise@npm:1.1.2"
  checksum: 011fe7ef0826b0fd1a95935a033a3c0fd08483903e1aa8f8b4e0704e3233406abb9ee25350ec0c20bbecb2aad8da0dcea58b392bbd77d6690736f02c143865d2
  languageName: node
  linkType: hard

"@protobufjs/base64@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/base64@npm:1.1.2"
  checksum: 67173ac34de1e242c55da52c2f5bdc65505d82453893f9b51dc74af9fe4c065cf4a657a4538e91b0d4a1a1e0a0642215e31894c31650ff6e3831471061e1ee9e
  languageName: node
  linkType: hard

"@protobufjs/codegen@npm:^2.0.4":
  version: 2.0.4
  resolution: "@protobufjs/codegen@npm:2.0.4"
  checksum: 59240c850b1d3d0b56d8f8098dd04787dcaec5c5bd8de186fa548de86b86076e1c50e80144b90335e705a044edf5bc8b0998548474c2a10a98c7e004a1547e4b
  languageName: node
  linkType: hard

"@protobufjs/eventemitter@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/eventemitter@npm:1.1.0"
  checksum: 0369163a3d226851682f855f81413cbf166cd98f131edb94a0f67f79e75342d86e89df9d7a1df08ac28be2bc77e0a7f0200526bb6c2a407abbfee1f0262d5fd7
  languageName: node
  linkType: hard

"@protobufjs/fetch@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/fetch@npm:1.1.0"
  dependencies:
    "@protobufjs/aspromise": ^1.1.1
    "@protobufjs/inquire": ^1.1.0
  checksum: 3fce7e09eb3f1171dd55a192066450f65324fd5f7cc01a431df01bb00d0a895e6bfb5b0c5561ce157ee1d886349c90703d10a4e11a1a256418ff591b969b3477
  languageName: node
  linkType: hard

"@protobufjs/float@npm:^1.0.2":
  version: 1.0.2
  resolution: "@protobufjs/float@npm:1.0.2"
  checksum: 5781e1241270b8bd1591d324ca9e3a3128d2f768077a446187a049e36505e91bc4156ed5ac3159c3ce3d2ba3743dbc757b051b2d723eea9cd367bfd54ab29b2f
  languageName: node
  linkType: hard

"@protobufjs/inquire@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/inquire@npm:1.1.0"
  checksum: ca06f02eaf65ca36fb7498fc3492b7fc087bfcc85c702bac5b86fad34b692bdce4990e0ef444c1e2aea8c034227bd1f0484be02810d5d7e931c55445555646f4
  languageName: node
  linkType: hard

"@protobufjs/path@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/path@npm:1.1.2"
  checksum: 856eeb532b16a7aac071cacde5c5620df800db4c80cee6dbc56380524736205aae21e5ae47739114bf669ab5e8ba0e767a282ad894f3b5e124197cb9224445ee
  languageName: node
  linkType: hard

"@protobufjs/pool@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/pool@npm:1.1.0"
  checksum: d6a34fbbd24f729e2a10ee915b74e1d77d52214de626b921b2d77288bd8f2386808da2315080f2905761527cceffe7ec34c7647bd21a5ae41a25e8212ff79451
  languageName: node
  linkType: hard

"@protobufjs/utf8@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/utf8@npm:1.1.0"
  checksum: f9bf3163d13aaa3b6f5e6fbf37a116e094ea021c0e1f2a7ccd0e12a29e2ce08dafba4e8b36e13f8ed7397e1591610ce880ed1289af4d66cf4ace8a36a9557278
  languageName: node
  linkType: hard

"@radix-ui/number@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/number@npm:1.1.1"
  checksum: 58717faf3f7aa180fdfcde7083cae0bc06677cbd08fd2bed5a3f8820deeb6f514f7d475f1fbb61e1f9a16cb2e7daf1000b2c614b0de3520fccfc04e3576e4566
  languageName: node
  linkType: hard

"@radix-ui/primitive@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/primitive@npm:1.1.2"
  checksum: 6cb2ac097faf77b7288bdfd87d92e983e357252d00ee0d2b51ad8e7897bf9f51ec53eafd7dd64c613671a2b02cb8166177bc3de444a6560ec60835c363321c18
  languageName: node
  linkType: hard

"@radix-ui/react-accordion@npm:^1.2.0":
  version: 1.2.11
  resolution: "@radix-ui/react-accordion@npm:1.2.11"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-collapsible": 1.1.11
    "@radix-ui/react-collection": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-controllable-state": 1.2.2
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 1fd2780d58055b2e475873207157736d8b090cefba956f5dec0e6a42e2f8f09367e40ec8696a1509bbf47a526b448a6bb14e53978e38abe9875c24a95493ac28
  languageName: node
  linkType: hard

"@radix-ui/react-alert-dialog@npm:^1.1.1":
  version: 1.1.14
  resolution: "@radix-ui/react-alert-dialog@npm:1.1.14"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-dialog": 1.1.14
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-slot": 1.2.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 5b3fec8fc11267c6c5bcf6afcbcb5ca7e8050401c692019097d08502a43d9fd555a18beb5b74806f301074150b6212dc7523722311e8dad9baa50a502d8b3828
  languageName: node
  linkType: hard

"@radix-ui/react-arrow@npm:1.1.7":
  version: 1.1.7
  resolution: "@radix-ui/react-arrow@npm:1.1.7"
  dependencies:
    "@radix-ui/react-primitive": 2.1.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 6cdf74f06090f8994cdf6d3935a44ea3ac309163a4f59c476482c4907e8e0775f224045030abf10fa4f9e1cb7743db034429249b9e59354988e247eeb0f4fdcf
  languageName: node
  linkType: hard

"@radix-ui/react-aspect-ratio@npm:^1.1.0":
  version: 1.1.7
  resolution: "@radix-ui/react-aspect-ratio@npm:1.1.7"
  dependencies:
    "@radix-ui/react-primitive": 2.1.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 50e84b77df4f777f03e5305ef6ca1b02824babc99bec5b7a2484d5c45c83a606375d1d3da63f7ac68d03b6abb27c3ddc0cbb02296a4315715116c221e7d61582
  languageName: node
  linkType: hard

"@radix-ui/react-avatar@npm:^1.1.0":
  version: 1.1.10
  resolution: "@radix-ui/react-avatar@npm:1.1.10"
  dependencies:
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-is-hydrated": 0.1.0
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 3d63c9b99549c574be0f3f24028ab3f339e51ca85fc0821887f83e30af1342a41b3a3f40bf0fc12cdb2814340342530b4aba6b758deda9e99f6846b41d2f987f
  languageName: node
  linkType: hard

"@radix-ui/react-checkbox@npm:^1.1.1":
  version: 1.3.2
  resolution: "@radix-ui/react-checkbox@npm:1.3.2"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-controllable-state": 1.2.2
    "@radix-ui/react-use-previous": 1.1.1
    "@radix-ui/react-use-size": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 4c895aa1d9fa469d429a1a7ce39c10c8c056aba7e55acd9b257d1169f3826721d815f5d3e1543014f563aed379885d60aabcb23629a75cbbf18d6257e860c20a
  languageName: node
  linkType: hard

"@radix-ui/react-collapsible@npm:1.1.11, @radix-ui/react-collapsible@npm:^1.1.0":
  version: 1.1.11
  resolution: "@radix-ui/react-collapsible@npm:1.1.11"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-controllable-state": 1.2.2
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: a7ef7992caa3c02952dd8210493c1807739a8ac2ffb6ecf9b07974de65ed49a93b743e325e2dcd23318ea658328a01f750ecd3a965db68059a84b14988892dc4
  languageName: node
  linkType: hard

"@radix-ui/react-collection@npm:1.1.7":
  version: 1.1.7
  resolution: "@radix-ui/react-collection@npm:1.1.7"
  dependencies:
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-slot": 1.2.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: dd9bb015ef86205b4246f55bc84e5ad54519bb89b4825dd83e646fe95205191fe376bb31a9e847f9d66b710d0ef7fc9353c0b0ded7e8997a5c1f5be6addf94ef
  languageName: node
  linkType: hard

"@radix-ui/react-compose-refs@npm:1.1.2, @radix-ui/react-compose-refs@npm:^1.1.1":
  version: 1.1.2
  resolution: "@radix-ui/react-compose-refs@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 9a91f0213014ffa40c5b8aae4debb993be5654217e504e35aa7422887eb2d114486d37e53c482d0fffb00cd44f51b5269fcdf397b280c71666fa11b7f32f165d
  languageName: node
  linkType: hard

"@radix-ui/react-context-menu@npm:^2.2.1":
  version: 2.2.15
  resolution: "@radix-ui/react-context-menu@npm:2.2.15"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-menu": 2.1.15
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-controllable-state": 1.2.2
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 85c25fdb1f378606b8281d0a383c5fed454a9577b9c1c30d469cd548353e08df544d841cbd8d9cd0dc4dca6ce76344efe310de693d082695a21ac543017476d0
  languageName: node
  linkType: hard

"@radix-ui/react-context@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-context@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 6d08437f23df362672259e535ae463e70bf7a0069f09bfa06c983a5a90e15250bde19da1d63ef8e3da06df1e1b4f92afa9d28ca6aa0297bb1c8aaf6ca83d28c5
  languageName: node
  linkType: hard

"@radix-ui/react-dialog@npm:1.1.14, @radix-ui/react-dialog@npm:^1.1.1, @radix-ui/react-dialog@npm:^1.1.2, @radix-ui/react-dialog@npm:^1.1.6":
  version: 1.1.14
  resolution: "@radix-ui/react-dialog@npm:1.1.14"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-dismissable-layer": 1.1.10
    "@radix-ui/react-focus-guards": 1.1.2
    "@radix-ui/react-focus-scope": 1.1.7
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-portal": 1.1.9
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-slot": 1.2.3
    "@radix-ui/react-use-controllable-state": 1.2.2
    aria-hidden: ^1.2.4
    react-remove-scroll: ^2.6.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 4928c0bf84b3a054eb3b4659b8e87192d8c120333d8437fcbd9d9311502d5eea9e9c87173929d4bfbc0db61b1134fcd98015756011d67ddcd2aed1b4a0134d7c
  languageName: node
  linkType: hard

"@radix-ui/react-direction@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-direction@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 8cc330285f1d06829568042ca9aabd3295be4690ae93683033fc8632b5c4dfc60f5c1312f6e2cae27c196189c719de3cfbcf792ff74800f9ccae0ab4abc1bc92
  languageName: node
  linkType: hard

"@radix-ui/react-dismissable-layer@npm:1.1.10":
  version: 1.1.10
  resolution: "@radix-ui/react-dismissable-layer@npm:1.1.10"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-escape-keydown": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: c4f31e8e93ae979a1bcd60726f8ebe7b79f23baafcd1d1e65f62cff6b322b2c6ff6132d82f2e63737f9955a8f04407849036f5b64b478e9a5678747d835957d8
  languageName: node
  linkType: hard

"@radix-ui/react-dropdown-menu@npm:^2.1.1":
  version: 2.1.15
  resolution: "@radix-ui/react-dropdown-menu@npm:2.1.15"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-menu": 2.1.15
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-controllable-state": 1.2.2
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: d95104c27eb3ddd0c03cd0736cd056f0cd1171ef49c4a9b331f2c3b4e67272bf0cf2ef2a03a5966a1ac79524a004cf27919c47b42788db63a42bb7585e0b306c
  languageName: node
  linkType: hard

"@radix-ui/react-focus-guards@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-focus-guards@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 618658e2b98575198b94ccfdd27f41beb37f83721c9a04617e848afbc47461124ae008d703d713b9644771d96d4852e49de322cf4be3b5f10a4f94d200db5248
  languageName: node
  linkType: hard

"@radix-ui/react-focus-scope@npm:1.1.7":
  version: 1.1.7
  resolution: "@radix-ui/react-focus-scope@npm:1.1.7"
  dependencies:
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: bb642d192d3da8431f8b39f64959b493a7ba743af8501b76699ef93357c96507c11fb76d468824b52b0e024eaee130a641f3a213268ac7c9af34883b45610c9b
  languageName: node
  linkType: hard

"@radix-ui/react-hover-card@npm:^1.1.1":
  version: 1.1.14
  resolution: "@radix-ui/react-hover-card@npm:1.1.14"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-dismissable-layer": 1.1.10
    "@radix-ui/react-popper": 1.2.7
    "@radix-ui/react-portal": 1.1.9
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-controllable-state": 1.2.2
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: cf61db6f4971ec58b3c076e385f332c1f792a6befb1bc011feab6361217682d53fadada0ae8d8dee42f75030fb51a7b8a3a005296e3fcce45a6f9e4f2797362e
  languageName: node
  linkType: hard

"@radix-ui/react-id@npm:1.1.1, @radix-ui/react-id@npm:^1.1.0":
  version: 1.1.1
  resolution: "@radix-ui/react-id@npm:1.1.1"
  dependencies:
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 8d68e200778eb3038906870fc869b3d881f4a46715fb20cddd9c76cba42fdaaa4810a3365b6ec2daf0f185b9201fc99d009167f59c7921bc3a139722c2e976db
  languageName: node
  linkType: hard

"@radix-ui/react-label@npm:^2.1.0":
  version: 2.1.7
  resolution: "@radix-ui/react-label@npm:2.1.7"
  dependencies:
    "@radix-ui/react-primitive": 2.1.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 6fe47ff695ac127a87a3ee77489fb345a89515edc8df4b2b290c801a9ae14ad934cc64ddad7638cddb71b064ff898bd1c2ac88c16dd1b8236a0939d7fea95e3b
  languageName: node
  linkType: hard

"@radix-ui/react-menu@npm:2.1.15":
  version: 2.1.15
  resolution: "@radix-ui/react-menu@npm:2.1.15"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-collection": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-dismissable-layer": 1.1.10
    "@radix-ui/react-focus-guards": 1.1.2
    "@radix-ui/react-focus-scope": 1.1.7
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-popper": 1.2.7
    "@radix-ui/react-portal": 1.1.9
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-roving-focus": 1.1.10
    "@radix-ui/react-slot": 1.2.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    aria-hidden: ^1.2.4
    react-remove-scroll: ^2.6.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 2e454089f8384bc4448645f4228656cd9b34301c783ede567b3902b4cb7f65e6516c2248785e24e6398430bbbfe88b8fb1c36224d70dfc30a961d5738a33355a
  languageName: node
  linkType: hard

"@radix-ui/react-menubar@npm:^1.1.1":
  version: 1.1.15
  resolution: "@radix-ui/react-menubar@npm:1.1.15"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-collection": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-menu": 2.1.15
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-roving-focus": 1.1.10
    "@radix-ui/react-use-controllable-state": 1.2.2
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 1a03a4757d157c1a7699a6ff90a15a75ed9c7e747f9169474de5f856f5a4358a080ff16dde4065e2f8d06def6170e7b4f85f28bf8c9f320958f0cec44c6e2967
  languageName: node
  linkType: hard

"@radix-ui/react-navigation-menu@npm:^1.2.0":
  version: 1.2.13
  resolution: "@radix-ui/react-navigation-menu@npm:1.2.13"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-collection": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-dismissable-layer": 1.1.10
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-controllable-state": 1.2.2
    "@radix-ui/react-use-layout-effect": 1.1.1
    "@radix-ui/react-use-previous": 1.1.1
    "@radix-ui/react-visually-hidden": 1.2.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 665fe4ea3b0cf1dd0891a361898618cc47a196ec2650f943c66a30ac7780c19f2bc5817fafe2e455af4c86d64735aa390bd10e4b6af26922c61aafe06300754f
  languageName: node
  linkType: hard

"@radix-ui/react-popover@npm:^1.1.1":
  version: 1.1.14
  resolution: "@radix-ui/react-popover@npm:1.1.14"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-dismissable-layer": 1.1.10
    "@radix-ui/react-focus-guards": 1.1.2
    "@radix-ui/react-focus-scope": 1.1.7
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-popper": 1.2.7
    "@radix-ui/react-portal": 1.1.9
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-slot": 1.2.3
    "@radix-ui/react-use-controllable-state": 1.2.2
    aria-hidden: ^1.2.4
    react-remove-scroll: ^2.6.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 50f146117ebf675944181ef2df4fbc2d7ca017c71a2ab78eaa67159eb8a0101c682fa02bafa2b132ea7744592b7f103d02935ace2c1f430ab9040a0ece9246c8
  languageName: node
  linkType: hard

"@radix-ui/react-popper@npm:1.2.7":
  version: 1.2.7
  resolution: "@radix-ui/react-popper@npm:1.2.7"
  dependencies:
    "@floating-ui/react-dom": ^2.0.0
    "@radix-ui/react-arrow": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-layout-effect": 1.1.1
    "@radix-ui/react-use-rect": 1.1.1
    "@radix-ui/react-use-size": 1.1.1
    "@radix-ui/rect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 1d672b8b635846501212eb0cd15273c8acdd31e76e78d2b9ba29ce29730d5a2d3a61a8ed49bb689c94f67f45d1dffe0d49449e0810f08c4e112d8aef8430e76d
  languageName: node
  linkType: hard

"@radix-ui/react-portal@npm:1.1.9":
  version: 1.1.9
  resolution: "@radix-ui/react-portal@npm:1.1.9"
  dependencies:
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: bd6be39bf021d5c917e2474ecba411e2625171f7ef96862b9af04bbd68833bb3662a7f1fbdeb5a7a237111b10e811e76d2cd03e957dadd6e668ef16541bfbd68
  languageName: node
  linkType: hard

"@radix-ui/react-presence@npm:1.1.4":
  version: 1.1.4
  resolution: "@radix-ui/react-presence@npm:1.1.4"
  dependencies:
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: d3b0976368fccdfa07100c1f07ca434d0092d4132d1ed4a5c213802f7318d77fc1fd61d1b7038b87e82912688fafa97d8af000a6cca4027b09d92c5477f79dd0
  languageName: node
  linkType: hard

"@radix-ui/react-primitive@npm:2.1.3, @radix-ui/react-primitive@npm:^2.0.2":
  version: 2.1.3
  resolution: "@radix-ui/react-primitive@npm:2.1.3"
  dependencies:
    "@radix-ui/react-slot": 1.2.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 01f82e4bad76b57767198762c905e5bcea04f4f52129749791e31adfcb1b36f6fdc89c73c40017d812b6e25e4ac925d837214bb280cfeaa5dc383457ce6940b0
  languageName: node
  linkType: hard

"@radix-ui/react-progress@npm:^1.1.0":
  version: 1.1.7
  resolution: "@radix-ui/react-progress@npm:1.1.7"
  dependencies:
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-primitive": 2.1.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 9161fe9b64819b2ec6597a600e8868eb5002757fe9fb7b2f4987f52369823fed498f27dcb6856b5a8bbd5d5f9cd5e8d296ba8be80f15b7c00fe8130425e10200
  languageName: node
  linkType: hard

"@radix-ui/react-radio-group@npm:^1.2.0":
  version: 1.3.7
  resolution: "@radix-ui/react-radio-group@npm:1.3.7"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-roving-focus": 1.1.10
    "@radix-ui/react-use-controllable-state": 1.2.2
    "@radix-ui/react-use-previous": 1.1.1
    "@radix-ui/react-use-size": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: e50be3e3a52039c583ea93b7182558ed7c896f5db3118867fdf0c9e2521afbe87534124764ae88d8874897728cf668a291ebc086d47cb0002e27108672414770
  languageName: node
  linkType: hard

"@radix-ui/react-roving-focus@npm:1.1.10":
  version: 1.1.10
  resolution: "@radix-ui/react-roving-focus@npm:1.1.10"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-collection": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-controllable-state": 1.2.2
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 9f6afabc82e3a018cbb48f4fc94ce046f776f894df2d66c96f6b78bf864d59d71f749406e30c4600f6415a6f703c66fdbb87b2841cfa919f10d1f6602df2a5b6
  languageName: node
  linkType: hard

"@radix-ui/react-scroll-area@npm:^1.1.0":
  version: 1.2.9
  resolution: "@radix-ui/react-scroll-area@npm:1.2.9"
  dependencies:
    "@radix-ui/number": 1.1.1
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: dd5e097b58bf39c51d748043e7524414da860fc116ef9265daaed7d08d5faf5f0a7b3c2acd00463f9d72d7da0eb28ce00c0b886402b87535c0c0983aad2e69b7
  languageName: node
  linkType: hard

"@radix-ui/react-select@npm:^2.1.1":
  version: 2.2.5
  resolution: "@radix-ui/react-select@npm:2.2.5"
  dependencies:
    "@radix-ui/number": 1.1.1
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-collection": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-dismissable-layer": 1.1.10
    "@radix-ui/react-focus-guards": 1.1.2
    "@radix-ui/react-focus-scope": 1.1.7
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-popper": 1.2.7
    "@radix-ui/react-portal": 1.1.9
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-slot": 1.2.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-controllable-state": 1.2.2
    "@radix-ui/react-use-layout-effect": 1.1.1
    "@radix-ui/react-use-previous": 1.1.1
    "@radix-ui/react-visually-hidden": 1.2.3
    aria-hidden: ^1.2.4
    react-remove-scroll: ^2.6.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 9f5f024744833c2f8730ea1dc7b4d3a5647336fab7809966f29bdc62a8ea970aafc451cf96c7c5a9d125225724ec29697c0b31b395012fef0c1f85a5425ca451
  languageName: node
  linkType: hard

"@radix-ui/react-separator@npm:^1.1.0":
  version: 1.1.7
  resolution: "@radix-ui/react-separator@npm:1.1.7"
  dependencies:
    "@radix-ui/react-primitive": 2.1.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: c5c19b7991bf5395eb2b849145deeb9aa307d9fcf220380497992ff2a301753ab477d0329af51b6d500cd3c1d777edbd2504b544d9254542fb5f829ac5ddbcf3
  languageName: node
  linkType: hard

"@radix-ui/react-slider@npm:^1.2.0":
  version: 1.3.5
  resolution: "@radix-ui/react-slider@npm:1.3.5"
  dependencies:
    "@radix-ui/number": 1.1.1
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-collection": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-controllable-state": 1.2.2
    "@radix-ui/react-use-layout-effect": 1.1.1
    "@radix-ui/react-use-previous": 1.1.1
    "@radix-ui/react-use-size": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: c09071d637309ae0a4438849cca98049d9b2220f10afe53f85c8c33fe41ba8ca813854a659ecef868e48e89ac389fb821726a766fc0c6c10aa6d7d408f4485f5
  languageName: node
  linkType: hard

"@radix-ui/react-slot@npm:1.2.3, @radix-ui/react-slot@npm:^1.1.0":
  version: 1.2.3
  resolution: "@radix-ui/react-slot@npm:1.2.3"
  dependencies:
    "@radix-ui/react-compose-refs": 1.1.2
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 2731089e15477dd5eef98a5757c36113dd932d0c52ff05123cd89f05f0412e95e5b205229185d1cd705cda4a674a838479cce2b3b46ed903f82f5d23d9e3f3c2
  languageName: node
  linkType: hard

"@radix-ui/react-switch@npm:^1.1.0":
  version: 1.2.5
  resolution: "@radix-ui/react-switch@npm:1.2.5"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-controllable-state": 1.2.2
    "@radix-ui/react-use-previous": 1.1.1
    "@radix-ui/react-use-size": 1.1.1
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: fdabb3600fc1b1669af5d9b232199e98053a32a0f457385d07ed012ae94112e12809ceb62572c87d703fde88288a6346dc1fb2a8eef4d22553ff7e8160c393d1
  languageName: node
  linkType: hard

"@radix-ui/react-tabs@npm:^1.1.0":
  version: 1.1.12
  resolution: "@radix-ui/react-tabs@npm:1.1.12"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-roving-focus": 1.1.10
    "@radix-ui/react-use-controllable-state": 1.2.2
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: eb5eecb4813b784c6f324a947b1f391b1b890bc3f5bd0eb2d08e73f84a10b25bbf482a8dd9785de8a6fe49ff99860c07cf0a6953c9f9631202e5d9c8ebc06758
  languageName: node
  linkType: hard

"@radix-ui/react-toast@npm:^1.2.1":
  version: 1.2.14
  resolution: "@radix-ui/react-toast@npm:1.2.14"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-collection": 1.1.7
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-dismissable-layer": 1.1.10
    "@radix-ui/react-portal": 1.1.9
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-callback-ref": 1.1.1
    "@radix-ui/react-use-controllable-state": 1.2.2
    "@radix-ui/react-use-layout-effect": 1.1.1
    "@radix-ui/react-visually-hidden": 1.2.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 1f17a3b0dbb03b4c5d09aa2edc5af102fd59f6f788597c4c2978b500747ab0daca1604bac263a90831359e40bccb2075fe7d82fadee7b48a9b2d4af515accbda
  languageName: node
  linkType: hard

"@radix-ui/react-toggle-group@npm:^1.1.0":
  version: 1.1.10
  resolution: "@radix-ui/react-toggle-group@npm:1.1.10"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-direction": 1.1.1
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-roving-focus": 1.1.10
    "@radix-ui/react-toggle": 1.1.9
    "@radix-ui/react-use-controllable-state": 1.2.2
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 4be91c95e6c91b9e5d1d92c9fa1c04f50277823c0230880d221446d5e2ce0c84f22e2c0c80c9df3d8b73abfb71e77366e40b89a47792016482253a2df524c63b
  languageName: node
  linkType: hard

"@radix-ui/react-toggle@npm:1.1.9, @radix-ui/react-toggle@npm:^1.1.0":
  version: 1.1.9
  resolution: "@radix-ui/react-toggle@npm:1.1.9"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-use-controllable-state": 1.2.2
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 844e44da16dea79c005629f96eba40423d6b76c23f4ccfc1a45ccb1afdf681d7434c103d217c0b80e00b809bbbc7b457cdcc1255c3424db3f3d818992995f17f
  languageName: node
  linkType: hard

"@radix-ui/react-tooltip@npm:^1.1.4":
  version: 1.2.7
  resolution: "@radix-ui/react-tooltip@npm:1.2.7"
  dependencies:
    "@radix-ui/primitive": 1.1.2
    "@radix-ui/react-compose-refs": 1.1.2
    "@radix-ui/react-context": 1.1.2
    "@radix-ui/react-dismissable-layer": 1.1.10
    "@radix-ui/react-id": 1.1.1
    "@radix-ui/react-popper": 1.2.7
    "@radix-ui/react-portal": 1.1.9
    "@radix-ui/react-presence": 1.1.4
    "@radix-ui/react-primitive": 2.1.3
    "@radix-ui/react-slot": 1.2.3
    "@radix-ui/react-use-controllable-state": 1.2.2
    "@radix-ui/react-visually-hidden": 1.2.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: aebb5c124c73dc236e9362899cb81bb0b1d4103d29205122f55dd64a9b9bdf4be7cc335964e1885098be3c570416a35899a317e0e6f373b6f9d39334699e4694
  languageName: node
  linkType: hard

"@radix-ui/react-use-callback-ref@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-callback-ref@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: cde8c40f1d4e79e6e71470218163a746858304bad03758ac84dc1f94247a046478e8e397518350c8d6609c84b7e78565441d7505bb3ed573afce82cfdcd19faf
  languageName: node
  linkType: hard

"@radix-ui/react-use-controllable-state@npm:1.2.2":
  version: 1.2.2
  resolution: "@radix-ui/react-use-controllable-state@npm:1.2.2"
  dependencies:
    "@radix-ui/react-use-effect-event": 0.0.2
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: b438ee199d0630bf95eaafe8bf4bce219e73b371cfc8465f47548bfa4ee231f1134b5c6696b242890a01a0fd25fa34a7b172346bbfc5ee25cfb28b3881b1dc92
  languageName: node
  linkType: hard

"@radix-ui/react-use-effect-event@npm:0.0.2":
  version: 0.0.2
  resolution: "@radix-ui/react-use-effect-event@npm:0.0.2"
  dependencies:
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 5a1950a30a399ea7e4b98154da9f536737a610de80189b7aacd4f064a89a3cd0d2a48571d527435227252e72e872bdb544ff6ffcfbdd02de2efd011be4aaa902
  languageName: node
  linkType: hard

"@radix-ui/react-use-escape-keydown@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-escape-keydown@npm:1.1.1"
  dependencies:
    "@radix-ui/react-use-callback-ref": 1.1.1
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 0eb0756c2c55ddcde9ff01446ab01c085ab2bf799173e97db7ef5f85126f9e8600225570801a1f64740e6d14c39ffe8eed7c14d29737345a5797f4622ac96f6f
  languageName: node
  linkType: hard

"@radix-ui/react-use-is-hydrated@npm:0.1.0":
  version: 0.1.0
  resolution: "@radix-ui/react-use-is-hydrated@npm:0.1.0"
  dependencies:
    use-sync-external-store: ^1.5.0
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 72e68a85a7a4a6dafd255a0cc87b6410bf0356c5e296e2eb82c265559408a735204cd150408b9c0d598057dafad3d51086e0362633bd728e95655b3bfd70ae26
  languageName: node
  linkType: hard

"@radix-ui/react-use-layout-effect@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-layout-effect@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: bad2ba4f206e6255263582bedfb7868773c400836f9a1b423c0b464ffe4a17e13d3f306d1ce19cf7a19a492e9d0e49747464f2656451bb7c6a99f5a57bd34de2
  languageName: node
  linkType: hard

"@radix-ui/react-use-previous@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-previous@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: ea6ea13523a0561dda9b14b9d44e299484816a6762d7fb50b91b27b6aec89f78c85245b69d5a904750d43919dbb7ef6ce6d3823639346675aa3a5cb9de32d984
  languageName: node
  linkType: hard

"@radix-ui/react-use-rect@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-rect@npm:1.1.1"
  dependencies:
    "@radix-ui/rect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 116461bebc49472f7497e66a9bd413541181b3d00c5e0aaeef45d790dc1fbd7c8dcea80b169ea273306228b9a3c2b70067e902d1fd5004b3057e3bbe35b9d55d
  languageName: node
  linkType: hard

"@radix-ui/react-use-size@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-size@npm:1.1.1"
  dependencies:
    "@radix-ui/react-use-layout-effect": 1.1.1
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 64e61f65feb67ffc80e1fc4a8d5e32480fb6d68475e2640377e021178dead101568cba5f936c9c33e6c142c7cf2fb5d76ad7b23ef80e556ba142d56cf306147b
  languageName: node
  linkType: hard

"@radix-ui/react-visually-hidden@npm:1.2.3":
  version: 1.2.3
  resolution: "@radix-ui/react-visually-hidden@npm:1.2.3"
  dependencies:
    "@radix-ui/react-primitive": 2.1.3
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 42296bde1ddf4af4e7445e914c35d6bc8406d6ede49f0a959a553e75b3ed21da09fda80a81c48d8ec058ed8129ce7137499d02ee26f90f0d3eaa2417922d6509
  languageName: node
  linkType: hard

"@radix-ui/rect@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/rect@npm:1.1.1"
  checksum: c1c111edeab70b14a735bca43601de6468c792482864b766ac8940b43321492e5c0ae62f92b156cecdc9265ec3c680c32b3fa0c8a90b5e796923a9af13c5dc20
  languageName: node
  linkType: hard

"@remix-run/router@npm:1.23.0":
  version: 1.23.0
  resolution: "@remix-run/router@npm:1.23.0"
  checksum: 6a403b7bc740f15185f3b68f90f98d4976fe231e819b44a0f0628783c4f31ca1072e3370c24b98488be3e4f68ecf51b20cb9463f20a5a6cf4c21929fc7721964
  languageName: node
  linkType: hard

"@rolldown/pluginutils@npm:1.0.0-beta.11":
  version: 1.0.0-beta.11
  resolution: "@rolldown/pluginutils@npm:1.0.0-beta.11"
  checksum: bcb963b0b4c51e02089be46a6bb77e79b38f4383e3a754853a44c4c6ea72a51c000a89b0e5d8e0529c7b0bb4aca48ebdca01eb0ddad61376c33fa7bd868c26f2
  languageName: node
  linkType: hard

"@rolldown/pluginutils@npm:1.0.0-beta.19":
  version: 1.0.0-beta.19
  resolution: "@rolldown/pluginutils@npm:1.0.0-beta.19"
  checksum: a82592c47ca7d7b56477e4d7065d3f06286ed3fd909c05bc645fb021361a02c66ed6c08f13692a893858ba3cb331e16ddc66ab793d3febfa0837ffa4269157c5
  languageName: node
  linkType: hard

"@sindresorhus/is@npm:^4.0.0":
  version: 4.6.0
  resolution: "@sindresorhus/is@npm:4.6.0"
  checksum: 83839f13da2c29d55c97abc3bc2c55b250d33a0447554997a85c539e058e57b8da092da396e252b11ec24a0279a0bed1f537fa26302209327060643e327f81d2
  languageName: node
  linkType: hard

"@smithy/abort-controller@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/abort-controller@npm:4.0.4"
  dependencies:
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 50e646633160f16d4d131c4a5612a352bca8ee652acfefc811389307756b791d0e0cee1459eeba4662e09b57e9f78681bb6c24d180d76e605126281fa52c20fb
  languageName: node
  linkType: hard

"@smithy/config-resolver@npm:^4.1.4":
  version: 4.1.4
  resolution: "@smithy/config-resolver@npm:4.1.4"
  dependencies:
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/types": ^4.3.1
    "@smithy/util-config-provider": ^4.0.0
    "@smithy/util-middleware": ^4.0.4
    tslib: ^2.6.2
  checksum: d3c3b7017377ae30839d3bc684fca7ff58c41c2ca71dd067931aff61f0c570b09cf35e47fde660488c7e1ecc8e1abf720bd41f380b9a91ea302fbd7c7f1b85fb
  languageName: node
  linkType: hard

"@smithy/core@npm:^3.7.0":
  version: 3.7.0
  resolution: "@smithy/core@npm:3.7.0"
  dependencies:
    "@smithy/middleware-serde": ^4.0.8
    "@smithy/protocol-http": ^5.1.2
    "@smithy/types": ^4.3.1
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-body-length-browser": ^4.0.0
    "@smithy/util-middleware": ^4.0.4
    "@smithy/util-stream": ^4.2.3
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: ca9c7f956e054bdb97ed8c4e4758846d6dcde36761955927abd12089371f795925bd9e67e4977f0a31750388f446e4d6eeeb406a4aebfb342d26546e72485ab1
  languageName: node
  linkType: hard

"@smithy/credential-provider-imds@npm:^4.0.6":
  version: 4.0.6
  resolution: "@smithy/credential-provider-imds@npm:4.0.6"
  dependencies:
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/property-provider": ^4.0.4
    "@smithy/types": ^4.3.1
    "@smithy/url-parser": ^4.0.4
    tslib: ^2.6.2
  checksum: 380ada77c7cc7f6e11ee4246a335799cd855b43df07469164ca7ccaeecd1eb8e037adf0b870e57578de7f82bb1f77e5d534c55ed3aa44491fcef55809b5d1d5c
  languageName: node
  linkType: hard

"@smithy/eventstream-codec@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/eventstream-codec@npm:4.0.4"
  dependencies:
    "@aws-crypto/crc32": 5.2.0
    "@smithy/types": ^4.3.1
    "@smithy/util-hex-encoding": ^4.0.0
    tslib: ^2.6.2
  checksum: b5ff1a2c9f8ea48406f181c0104daf56e1f52bf251cfc531f497abce86f02a148d381ee1648bcd34a4c2293b8e0e052c02043755ffeb864421957fa320c74435
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-browser@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/eventstream-serde-browser@npm:4.0.4"
  dependencies:
    "@smithy/eventstream-serde-universal": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 1d39b5da8fe1fd060d47f41f25b70ea4291c8ae2e4f5ea79bf1849a72af88ab0d819618e5b02606b1758084ce43964a92ffc36688817409dcabacd36a8a2266a
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-config-resolver@npm:^4.1.2":
  version: 4.1.2
  resolution: "@smithy/eventstream-serde-config-resolver@npm:4.1.2"
  dependencies:
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 42b81e6c92029966f373be7eb589e1292a201e191965b62f1ff71a4e6b5dabf874850a8b65441fa9cec735aad018365a3e841f95af7443288130603be00a7996
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-node@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/eventstream-serde-node@npm:4.0.4"
  dependencies:
    "@smithy/eventstream-serde-universal": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 179dd707b0f730c36ab68b1cfdbd0c6f29012a25bf3fec564076217a929627e119ccae8e5df34c267c1df8512c6294c10aa74e0511ab82b767f4a7ef39209519
  languageName: node
  linkType: hard

"@smithy/eventstream-serde-universal@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/eventstream-serde-universal@npm:4.0.4"
  dependencies:
    "@smithy/eventstream-codec": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 53cf083f742f2fa381a0d0457e3fbfe3b36b65efec216f52d21f840382466b59c64e685a65412925b60a3def11e24e4bd6bcd3bb5afad336f3fafab2bb2cbd48
  languageName: node
  linkType: hard

"@smithy/fetch-http-handler@npm:^5.1.0":
  version: 5.1.0
  resolution: "@smithy/fetch-http-handler@npm:5.1.0"
  dependencies:
    "@smithy/protocol-http": ^5.1.2
    "@smithy/querystring-builder": ^4.0.4
    "@smithy/types": ^4.3.1
    "@smithy/util-base64": ^4.0.0
    tslib: ^2.6.2
  checksum: f88242d6b4f1341e7d45b1defdc6b930f1600d840da57ce015583a81fd24a320e12b9fda12e3c51ecf9ce49ede37fe1f77d21d5e4bb94f094e801b6464dfee8c
  languageName: node
  linkType: hard

"@smithy/hash-node@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/hash-node@npm:4.0.4"
  dependencies:
    "@smithy/types": ^4.3.1
    "@smithy/util-buffer-from": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: 2fd8a1036b9d6d2948249ad41a97b5801b918948ab1f8041b2b2482848570e8b417eeea7810f959376325e9ab33890775025b34a58305355b84ca8bff1417481
  languageName: node
  linkType: hard

"@smithy/invalid-dependency@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/invalid-dependency@npm:4.0.4"
  dependencies:
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 6d6f53558cb252e2070e4830a18c0c72ad486308378d6eab2a185d63f5a0492ffbdff27dbea59f79e2d48477af2295e80d6314becf9ea3215827be8bb6690b07
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/is-array-buffer@npm:2.2.0"
  dependencies:
    tslib: ^2.6.2
  checksum: cd12c2e27884fec89ca8966d33c9dc34d3234efe89b33a9b309c61ebcde463e6f15f6a02d31d4fddbfd6e5904743524ca5b95021b517b98fe10957c2da0cd5fc
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/is-array-buffer@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 8226fc1eca7aacd7f887f3a5ec2f15a3cafa72aa1c42d3fc759c66600481381d18ec7285a8195f24b9c4fe0ce9a565c133b2021d86a8077aebce3f86b3716802
  languageName: node
  linkType: hard

"@smithy/middleware-content-length@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/middleware-content-length@npm:4.0.4"
  dependencies:
    "@smithy/protocol-http": ^5.1.2
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 251e47fbb7df19a8c39719f96dfd9e00252fc33733d2585898b7e5a37e85056052de1559d3c62b9daf493e2293b574ac2e92e17806777cadc9b733f1aab42294
  languageName: node
  linkType: hard

"@smithy/middleware-endpoint@npm:^4.1.14":
  version: 4.1.14
  resolution: "@smithy/middleware-endpoint@npm:4.1.14"
  dependencies:
    "@smithy/core": ^3.7.0
    "@smithy/middleware-serde": ^4.0.8
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/shared-ini-file-loader": ^4.0.4
    "@smithy/types": ^4.3.1
    "@smithy/url-parser": ^4.0.4
    "@smithy/util-middleware": ^4.0.4
    tslib: ^2.6.2
  checksum: 8cc4dbfada407590eb2ffae00b655ab76b5915b2380041df9bf71c157e067da7cad9565852ccad9d6def40325de5d91e508f9451664511dc1bf5c111f405c6db
  languageName: node
  linkType: hard

"@smithy/middleware-retry@npm:^4.1.15":
  version: 4.1.15
  resolution: "@smithy/middleware-retry@npm:4.1.15"
  dependencies:
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/protocol-http": ^5.1.2
    "@smithy/service-error-classification": ^4.0.6
    "@smithy/smithy-client": ^4.4.6
    "@smithy/types": ^4.3.1
    "@smithy/util-middleware": ^4.0.4
    "@smithy/util-retry": ^4.0.6
    tslib: ^2.6.2
    uuid: ^9.0.1
  checksum: 95fa796973ee58420992e97d88f2ac3e8a794d51a845f3c09e6aab7fac239d7f4e36439b46f9706b295c97d8e9c5ae184b5007947905f5e9bc21a3a265fefa55
  languageName: node
  linkType: hard

"@smithy/middleware-serde@npm:^4.0.8":
  version: 4.0.8
  resolution: "@smithy/middleware-serde@npm:4.0.8"
  dependencies:
    "@smithy/protocol-http": ^5.1.2
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 1c78cf584bf82c2ed80d55694945d63b5d3bdaf0c4dea1a35ff33b201d939e9ee5afbfb01c6725c9cbc0d9efb3ee50703970d177a9d20dba545e7e7ba3c0a3f5
  languageName: node
  linkType: hard

"@smithy/middleware-stack@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/middleware-stack@npm:4.0.4"
  dependencies:
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: c0b4e057d438fbc900435a4bcae68308bc17361968ebe828d43b4f78d826711e5d196ea2fc3ef86525169508d885979e459db0d46918ae00a2bb5dc8a5bd6796
  languageName: node
  linkType: hard

"@smithy/node-config-provider@npm:^4.1.3":
  version: 4.1.3
  resolution: "@smithy/node-config-provider@npm:4.1.3"
  dependencies:
    "@smithy/property-provider": ^4.0.4
    "@smithy/shared-ini-file-loader": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: c1260719f567b64e979e54698356ffd49f26d82e5eaafc60741588257df6016bbf7d2e26cba902ff900058e5e47e985287fdcb7ab1acdf6534b7cd50252e3856
  languageName: node
  linkType: hard

"@smithy/node-http-handler@npm:^4.1.0":
  version: 4.1.0
  resolution: "@smithy/node-http-handler@npm:4.1.0"
  dependencies:
    "@smithy/abort-controller": ^4.0.4
    "@smithy/protocol-http": ^5.1.2
    "@smithy/querystring-builder": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 4ea660acadb0f30255066b068451cd8521d130f5702060c19af5e488f681cc7f76834612e566d80a933d92e897b4fca94973ed942f0a32f1703f6140bdd66b81
  languageName: node
  linkType: hard

"@smithy/property-provider@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/property-provider@npm:4.0.4"
  dependencies:
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 1cd552792897e43c1d4cf91edac0956a8a8d6a7ba588e46532644ae5aca535ec0fb33e3aa71c73f325632b72cd1b8f26732525a6723f74c54238026432b0118e
  languageName: node
  linkType: hard

"@smithy/protocol-http@npm:^5.1.2":
  version: 5.1.2
  resolution: "@smithy/protocol-http@npm:5.1.2"
  dependencies:
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 48dbb715956f7089f3422c6c875fd5c6c901bb55363091905f749bba4bac03c40bf11e63dcc92c9b5de058305c60513e987e1fd7550e585c9e2a98e7355676c8
  languageName: node
  linkType: hard

"@smithy/querystring-builder@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/querystring-builder@npm:4.0.4"
  dependencies:
    "@smithy/types": ^4.3.1
    "@smithy/util-uri-escape": ^4.0.0
    tslib: ^2.6.2
  checksum: e521cd60294aebabb11386f4db7925095ca7dcdd1eda1904ad3443aa65c992a74e7d57b24018c3e141320bcc8b8928a24b8a14c4328bc7176bdb1eac15f6655b
  languageName: node
  linkType: hard

"@smithy/querystring-parser@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/querystring-parser@npm:4.0.4"
  dependencies:
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: ebe874dfec44ec3d6ff63f9570cac7c18f5b1b2fb3d6a72722adb9d24bb891970fbbabb18d15b8ce46902cc5f21f1751218794f3ff2e110865804d822f4b83a0
  languageName: node
  linkType: hard

"@smithy/service-error-classification@npm:^4.0.6":
  version: 4.0.6
  resolution: "@smithy/service-error-classification@npm:4.0.6"
  dependencies:
    "@smithy/types": ^4.3.1
  checksum: c851c882358af75cac41508ffdd2cfdc59e0cd298cb25cf6a4a97dd6cbc92f4890ce04590305726ebb1bbb6b6c527dde8d80ec84095c76bcdb6a3a1cc2107a90
  languageName: node
  linkType: hard

"@smithy/shared-ini-file-loader@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/shared-ini-file-loader@npm:4.0.4"
  dependencies:
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: b9405d3fea03cb7d1b031a41d7a91581eaaf47a5e6322c7bf2c57e27bcf8af403eea68c46a9c878a31af8a1f08fa803fce3d253c55b3318548fc93d61b0e56e8
  languageName: node
  linkType: hard

"@smithy/signature-v4@npm:^5.1.2":
  version: 5.1.2
  resolution: "@smithy/signature-v4@npm:5.1.2"
  dependencies:
    "@smithy/is-array-buffer": ^4.0.0
    "@smithy/protocol-http": ^5.1.2
    "@smithy/types": ^4.3.1
    "@smithy/util-hex-encoding": ^4.0.0
    "@smithy/util-middleware": ^4.0.4
    "@smithy/util-uri-escape": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: b8acbdd600279be860650b8c99ea443b653f0a980a9aceb7cdf8bf0f017d0376a4aac9bef738c24aa0c2f12b3ae1984bf1ed5d99235f64a9ff41a7fcd851b531
  languageName: node
  linkType: hard

"@smithy/smithy-client@npm:^4.4.6":
  version: 4.4.6
  resolution: "@smithy/smithy-client@npm:4.4.6"
  dependencies:
    "@smithy/core": ^3.7.0
    "@smithy/middleware-endpoint": ^4.1.14
    "@smithy/middleware-stack": ^4.0.4
    "@smithy/protocol-http": ^5.1.2
    "@smithy/types": ^4.3.1
    "@smithy/util-stream": ^4.2.3
    tslib: ^2.6.2
  checksum: 6579149131e16f0dbcb87ff9d6fc4d9f8d71056066d73e4838e8233a0a9f2bf9abd07a8b890db7dded9d539d37a6a66fa6044ae91e0e015d0b73453c5344eeae
  languageName: node
  linkType: hard

"@smithy/types@npm:^4.3.1":
  version: 4.3.1
  resolution: "@smithy/types@npm:4.3.1"
  dependencies:
    tslib: ^2.6.2
  checksum: 45f2e15cec06eefb6a2470346c65ec927e56ab1757eee5ab1c431f703a9b350b331679e1f60105a1529ecb9cdb953104883942e655701fb4710bbaf566ec0bc6
  languageName: node
  linkType: hard

"@smithy/url-parser@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/url-parser@npm:4.0.4"
  dependencies:
    "@smithy/querystring-parser": ^4.0.4
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 1d3df1c58809f424af00396f987607ec9ebb0840625e4353af6dcd6baf480db8dd080b2f01ed41598ff18681ab2fcecab37f18f4c253fcbdd71eab2fab049400
  languageName: node
  linkType: hard

"@smithy/util-base64@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-base64@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: 7fb3430d6e1cbb4bcc61458587bb0746458f0ec8e8cd008224ca984ff65c3c3307b3a528d040cef4c1fc7d1bd4111f6de8f4f1595845422f14ac7d100b3871b1
  languageName: node
  linkType: hard

"@smithy/util-body-length-browser@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-browser@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 72381e12de7cccbb722c60e3f3ae0f8bce7fc9a9e8064c7968ac733698a5a30bea098a3c365095c519491fe64e2e949c22f74d4f1e0d910090d6389b41c416eb
  languageName: node
  linkType: hard

"@smithy/util-body-length-node@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-node@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 12d8de9c526647f51f56804044f5847f0c7c7afee30fa368d2b7bd4b4de8fe2438a925aab51965fe8a4b2f08f68e8630cc3c54a449beae6646d99cae900ed106
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/util-buffer-from@npm:2.2.0"
  dependencies:
    "@smithy/is-array-buffer": ^2.2.0
    tslib: ^2.6.2
  checksum: 424c5b7368ae5880a8f2732e298d17879a19ca925f24ca45e1c6c005f717bb15b76eb28174d308d81631ad457ea0088aab0fd3255dd42f45a535c81944ad64d3
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-buffer-from@npm:4.0.0"
  dependencies:
    "@smithy/is-array-buffer": ^4.0.0
    tslib: ^2.6.2
  checksum: 8124e28d3e34b5335c08398a9081cc56a232d23e08172d488669f91a167d0871d36aba9dd3e4b70175a52f1bd70e2bf708d4c989a19512a4374d2cf67650a15e
  languageName: node
  linkType: hard

"@smithy/util-config-provider@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-config-provider@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 91bd9e0bec4c4a37c3fc286e72f3387be9272b090111edaee992d9e9619370f3f2ad88ce771ef42dbfe40a44500163b633914486e662526591f5f737d5e4ff5a
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-browser@npm:^4.0.22":
  version: 4.0.22
  resolution: "@smithy/util-defaults-mode-browser@npm:4.0.22"
  dependencies:
    "@smithy/property-provider": ^4.0.4
    "@smithy/smithy-client": ^4.4.6
    "@smithy/types": ^4.3.1
    bowser: ^2.11.0
    tslib: ^2.6.2
  checksum: 7ba4f23367a66cb73db19f9e5ae2fb2a6337127ed6f65278b909a37a152b3efb09c5fbcd933e4c4531a257f691cf4f5b3b1f4816024a3112a4c94c4afc55c04d
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-node@npm:^4.0.22":
  version: 4.0.22
  resolution: "@smithy/util-defaults-mode-node@npm:4.0.22"
  dependencies:
    "@smithy/config-resolver": ^4.1.4
    "@smithy/credential-provider-imds": ^4.0.6
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/property-provider": ^4.0.4
    "@smithy/smithy-client": ^4.4.6
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 1a10953d1b6b0e0ae484cfa6a1df3a0782414c2d6b95e2c43d3320c2e1260a80b582a68bbfb48eb373c1e5effb337286e85794878c4da6a9dc797011ff809d7a
  languageName: node
  linkType: hard

"@smithy/util-endpoints@npm:^3.0.6":
  version: 3.0.6
  resolution: "@smithy/util-endpoints@npm:3.0.6"
  dependencies:
    "@smithy/node-config-provider": ^4.1.3
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 185c096db895f5bfabc05f1500d3428761fc4d450e998d6bf269879f7fc3f6fd770c1ed5a2de395a6b5300197bd40748a5b06c74b91ff01c9c499a25f2ba827e
  languageName: node
  linkType: hard

"@smithy/util-hex-encoding@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-hex-encoding@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: b932fa0e5cd2ba2598ad55ce46722bbbd15109809badaa3e4402fe4dd6f31f62b9fb49d2616e38d660363dc92a5898391f9c8f3b18507c36109e908400785e2a
  languageName: node
  linkType: hard

"@smithy/util-middleware@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/util-middleware@npm:4.0.4"
  dependencies:
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 6cfdec16f03cc963e78d888a0ef349c0d80645775e9933a88c4615fbd5a683a8230997f89372e2597bd956bc05df5adc41de6524fa8c0cc93fb7150d6530a03b
  languageName: node
  linkType: hard

"@smithy/util-retry@npm:^4.0.6":
  version: 4.0.6
  resolution: "@smithy/util-retry@npm:4.0.6"
  dependencies:
    "@smithy/service-error-classification": ^4.0.6
    "@smithy/types": ^4.3.1
    tslib: ^2.6.2
  checksum: 0faef3d90da51024a5abd90de6bf1a846b6cd0f61c78791a2fecc7e49b0e8a705ca5619ae538cad4bab8995456d8219fe1c2769dacd156195cb73befcb02ca03
  languageName: node
  linkType: hard

"@smithy/util-stream@npm:^4.2.3":
  version: 4.2.3
  resolution: "@smithy/util-stream@npm:4.2.3"
  dependencies:
    "@smithy/fetch-http-handler": ^5.1.0
    "@smithy/node-http-handler": ^4.1.0
    "@smithy/types": ^4.3.1
    "@smithy/util-base64": ^4.0.0
    "@smithy/util-buffer-from": ^4.0.0
    "@smithy/util-hex-encoding": ^4.0.0
    "@smithy/util-utf8": ^4.0.0
    tslib: ^2.6.2
  checksum: 3384df45323f9af1ecc3bad506e8dc0100af44397d623e4b456654b997c87458b9c550b6f540f31e1d498f93e914b868f4bda6cf7eb36b34e26f86426c5299fd
  languageName: node
  linkType: hard

"@smithy/util-uri-escape@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-uri-escape@npm:4.0.0"
  dependencies:
    tslib: ^2.6.2
  checksum: 7ea350545971f8a009d56e085c34c949c9045862cfab233ee7adc16e111a076a814bb5d9279b2b85ee382e0ed204a1c673ac32e3e28f1073b62a2c53a5dd6d19
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^2.0.0":
  version: 2.3.0
  resolution: "@smithy/util-utf8@npm:2.3.0"
  dependencies:
    "@smithy/util-buffer-from": ^2.2.0
    tslib: ^2.6.2
  checksum: 00e55d4b4e37d48be0eef3599082402b933c52a1407fed7e8e8ad76d94d81a0b30b8bfaf2047c59d9c3af31e5f20e7a8c959cb7ae270f894255e05a2229964f0
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-utf8@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": ^4.0.0
    tslib: ^2.6.2
  checksum: 08811c5a18c341782b3b65acc4640a9f559aeba61c889dbdc56e5153a3b7f395e613bfb1ade25cf15311d6237f291e1fce8af197c6313065e0cb084fd2148c64
  languageName: node
  linkType: hard

"@swc/core-darwin-arm64@npm:1.12.11":
  version: 1.12.11
  resolution: "@swc/core-darwin-arm64@npm:1.12.11"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@swc/core-darwin-x64@npm:1.12.11":
  version: 1.12.11
  resolution: "@swc/core-darwin-x64@npm:1.12.11"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@swc/core-linux-arm-gnueabihf@npm:1.12.11":
  version: 1.12.11
  resolution: "@swc/core-linux-arm-gnueabihf@npm:1.12.11"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@swc/core-linux-arm64-gnu@npm:1.12.11":
  version: 1.12.11
  resolution: "@swc/core-linux-arm64-gnu@npm:1.12.11"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@swc/core-linux-arm64-musl@npm:1.12.11":
  version: 1.12.11
  resolution: "@swc/core-linux-arm64-musl@npm:1.12.11"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@swc/core-linux-x64-gnu@npm:1.12.11":
  version: 1.12.11
  resolution: "@swc/core-linux-x64-gnu@npm:1.12.11"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@swc/core-linux-x64-musl@npm:1.12.11":
  version: 1.12.11
  resolution: "@swc/core-linux-x64-musl@npm:1.12.11"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@swc/core-win32-arm64-msvc@npm:1.12.11":
  version: 1.12.11
  resolution: "@swc/core-win32-arm64-msvc@npm:1.12.11"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@swc/core-win32-ia32-msvc@npm:1.12.11":
  version: 1.12.11
  resolution: "@swc/core-win32-ia32-msvc@npm:1.12.11"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@swc/core-win32-x64-msvc@npm:1.12.11":
  version: 1.12.11
  resolution: "@swc/core-win32-x64-msvc@npm:1.12.11"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@swc/core@npm:^1.11.31":
  version: 1.12.11
  resolution: "@swc/core@npm:1.12.11"
  dependencies:
    "@swc/core-darwin-arm64": 1.12.11
    "@swc/core-darwin-x64": 1.12.11
    "@swc/core-linux-arm-gnueabihf": 1.12.11
    "@swc/core-linux-arm64-gnu": 1.12.11
    "@swc/core-linux-arm64-musl": 1.12.11
    "@swc/core-linux-x64-gnu": 1.12.11
    "@swc/core-linux-x64-musl": 1.12.11
    "@swc/core-win32-arm64-msvc": 1.12.11
    "@swc/core-win32-ia32-msvc": 1.12.11
    "@swc/core-win32-x64-msvc": 1.12.11
    "@swc/counter": ^0.1.3
    "@swc/types": ^0.1.23
  peerDependencies:
    "@swc/helpers": ">=0.5.17"
  dependenciesMeta:
    "@swc/core-darwin-arm64":
      optional: true
    "@swc/core-darwin-x64":
      optional: true
    "@swc/core-linux-arm-gnueabihf":
      optional: true
    "@swc/core-linux-arm64-gnu":
      optional: true
    "@swc/core-linux-arm64-musl":
      optional: true
    "@swc/core-linux-x64-gnu":
      optional: true
    "@swc/core-linux-x64-musl":
      optional: true
    "@swc/core-win32-arm64-msvc":
      optional: true
    "@swc/core-win32-ia32-msvc":
      optional: true
    "@swc/core-win32-x64-msvc":
      optional: true
  peerDependenciesMeta:
    "@swc/helpers":
      optional: true
  checksum: 6ca6578efb64d81a7c7e656f130e8be66ff3ada1dd3035ca9242973146e864bfcc2d8fffc30bdd23233a831ce566f38f4c2597e45f35dc8312175b2a2ded4bcc
  languageName: node
  linkType: hard

"@swc/counter@npm:^0.1.3":
  version: 0.1.3
  resolution: "@swc/counter@npm:0.1.3"
  checksum: df8f9cfba9904d3d60f511664c70d23bb323b3a0803ec9890f60133954173047ba9bdeabce28cd70ba89ccd3fd6c71c7b0bd58be85f611e1ffbe5d5c18616598
  languageName: node
  linkType: hard

"@swc/helpers@npm:^0.5.11":
  version: 0.5.17
  resolution: "@swc/helpers@npm:0.5.17"
  dependencies:
    tslib: ^2.8.0
  checksum: 085e13b536323945dfc3a270debf270bda6dfc80a1c68fd2ed08f7cbdfcbdaeead402650b5b10722e54e4a24193afc8a3c6f63d3d6d719974e7470557fb415bd
  languageName: node
  linkType: hard

"@swc/types@npm:^0.1.23":
  version: 0.1.23
  resolution: "@swc/types@npm:0.1.23"
  dependencies:
    "@swc/counter": ^0.1.3
  checksum: 3069c6966754046d3adf3b0c223a6012fd7dafa76aed8db02eb7d5ee7cfe7d2b8561c31130baf78e7d5afd5d32226076f3188fdd7c78e878aa64d7eed14684bd
  languageName: node
  linkType: hard

"@szmarczak/http-timer@npm:^4.0.5":
  version: 4.0.6
  resolution: "@szmarczak/http-timer@npm:4.0.6"
  dependencies:
    defer-to-connect: ^2.0.0
  checksum: c29df3bcec6fc3bdec2b17981d89d9c9fc9bd7d0c9bcfe92821dc533f4440bc890ccde79971838b4ceed1921d456973c4180d7175ee1d0023ad0562240a58d95
  languageName: node
  linkType: hard

"@tailwindcss/typography@npm:^0.5.15":
  version: 0.5.16
  resolution: "@tailwindcss/typography@npm:0.5.16"
  dependencies:
    lodash.castarray: ^4.4.0
    lodash.isplainobject: ^4.0.6
    lodash.merge: ^4.6.2
    postcss-selector-parser: 6.0.10
  peerDependencies:
    tailwindcss: "*"
  checksum: 700b74aa4bda40db9f9c6bce5dbfbd38c48999c9999972260b8392c1f7b67a542bbf31f33d6a81497b4d34dfaebd6dde1077c0e1e4a39c8263c759c2c148b961
  languageName: node
  linkType: hard

"@tanstack/query-core@npm:5.82.0":
  version: 5.82.0
  resolution: "@tanstack/query-core@npm:5.82.0"
  checksum: 293a1e299f1de7ab3a659fa240bed2e2ce5f6fc44b83f145b7ef6bdcf9b286169ae0a38e6c76e67ae62a2915663adfaa57ac41c1c362b8304aaa726207d6ed88
  languageName: node
  linkType: hard

"@tanstack/react-query@npm:^5.56.2":
  version: 5.82.0
  resolution: "@tanstack/react-query@npm:5.82.0"
  dependencies:
    "@tanstack/query-core": 5.82.0
  peerDependencies:
    react: ^18 || ^19
  checksum: e68370c211730b94b924b4616a1c032c231f2e683847663ca3370e535b4c82cef147ca4e4a3374d68ffddf6726420107c65a04b21a5111d8e25163a2127adbc0
  languageName: node
  linkType: hard

"@tootallnate/once@npm:1":
  version: 1.1.2
  resolution: "@tootallnate/once@npm:1.1.2"
  checksum: e1fb1bbbc12089a0cb9433dc290f97bddd062deadb6178ce9bcb93bb7c1aecde5e60184bc7065aec42fe1663622a213493c48bbd4972d931aae48315f18e1be9
  languageName: node
  linkType: hard

"@tootallnate/once@npm:2":
  version: 2.0.0
  resolution: "@tootallnate/once@npm:2.0.0"
  checksum: ad87447820dd3f24825d2d947ebc03072b20a42bfc96cbafec16bff8bbda6c1a81fcb0be56d5b21968560c5359a0af4038a68ba150c3e1694fe4c109a063bed8
  languageName: node
  linkType: hard

"@types/async@npm:^3.2.24":
  version: 3.2.24
  resolution: "@types/async@npm:3.2.24"
  checksum: e52ee5e9b6e4354aba709551f1777080b16c07d91632ef2d3d6542ea838c84fae5a2811015327dd908ce7dc810daacc564ab99b9a5bc22e4f3f86819461e0970
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.20.5":
  version: 7.20.5
  resolution: "@types/babel__core@npm:7.20.5"
  dependencies:
    "@babel/parser": ^7.20.7
    "@babel/types": ^7.20.7
    "@types/babel__generator": "*"
    "@types/babel__template": "*"
    "@types/babel__traverse": "*"
  checksum: a3226f7930b635ee7a5e72c8d51a357e799d19cbf9d445710fa39ab13804f79ab1a54b72ea7d8e504659c7dfc50675db974b526142c754398d7413aa4bc30845
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.27.0
  resolution: "@types/babel__generator@npm:7.27.0"
  dependencies:
    "@babel/types": ^7.0.0
  checksum: e6739cacfa276c1ad38e1d8a6b4b1f816c2c11564e27f558b68151728489aaf0f4366992107ee4ed7615dfa303f6976dedcdce93df2b247116d1bcd1607ee260
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.4
  resolution: "@types/babel__template@npm:7.4.4"
  dependencies:
    "@babel/parser": ^7.1.0
    "@babel/types": ^7.0.0
  checksum: d7a02d2a9b67e822694d8e6a7ddb8f2b71a1d6962dfd266554d2513eefbb205b33ca71a0d163b1caea3981ccf849211f9964d8bd0727124d18ace45aa6c9ae29
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*":
  version: 7.20.7
  resolution: "@types/babel__traverse@npm:7.20.7"
  dependencies:
    "@babel/types": ^7.20.7
  checksum: 2a2e5ad29c34a8b776162b0fe81c9ccb6459b2b46bf230f756ba0276a0258fcae1cbcfdccbb93a1e8b1df44f4939784ee8a1a269f95afe0c78b24b9cb6d50dd1
  languageName: node
  linkType: hard

"@types/better-sqlite3@npm:^7.6.13":
  version: 7.6.13
  resolution: "@types/better-sqlite3@npm:7.6.13"
  dependencies:
    "@types/node": "*"
  checksum: 0b5bcca8d5441e0b234b2a120f6b6c8b8136fbe2a777a287ba72cf3c3c260abf5108e8b5cc3daae3fedd7f9f2f5a0abbb4cb72e45a4b82ebd6e1dd98fada3f7c
  languageName: node
  linkType: hard

"@types/body-parser@npm:*":
  version: 1.19.6
  resolution: "@types/body-parser@npm:1.19.6"
  dependencies:
    "@types/connect": "*"
    "@types/node": "*"
  checksum: 33041e88eae00af2cfa0827e951e5f1751eafab2a8b6fce06cd89ef368a988907996436b1325180edaeddd1c0c7d0d0d4c20a6c9ff294a91e0039a9db9e9b658
  languageName: node
  linkType: hard

"@types/cacheable-request@npm:^6.0.1":
  version: 6.0.3
  resolution: "@types/cacheable-request@npm:6.0.3"
  dependencies:
    "@types/http-cache-semantics": "*"
    "@types/keyv": ^3.1.4
    "@types/node": "*"
    "@types/responselike": ^1.0.0
  checksum: d9b26403fe65ce6b0cb3720b7030104c352bcb37e4fac2a7089a25a97de59c355fa08940658751f2f347a8512aa9d18fdb66ab3ade835975b2f454f2d5befbd9
  languageName: node
  linkType: hard

"@types/command-line-args@npm:^5.2.3":
  version: 5.2.3
  resolution: "@types/command-line-args@npm:5.2.3"
  checksum: 3d90db5b4bbaabd049654a0d12fa378989ab0d76a0f98d4c606761b5a08ce76458df0f9bb175219e187b4cd57e285e6f836d23e86b2c3d997820854cc3ed9121
  languageName: node
  linkType: hard

"@types/command-line-usage@npm:^5.0.4":
  version: 5.0.4
  resolution: "@types/command-line-usage@npm:5.0.4"
  checksum: 7173c356ca8c9507feeeda8e660c52498929556e90be0cf2d09d35270c597481121cd0f006a74167c5577feebfbc75b648c0f8f01b8f06ce30bde9fe30d5ba40
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.38
  resolution: "@types/connect@npm:3.4.38"
  dependencies:
    "@types/node": "*"
  checksum: 7eb1bc5342a9604facd57598a6c62621e244822442976c443efb84ff745246b10d06e8b309b6e80130026a396f19bf6793b7cecd7380169f369dac3bfc46fb99
  languageName: node
  linkType: hard

"@types/cors@npm:^2.8.18":
  version: 2.8.19
  resolution: "@types/cors@npm:2.8.19"
  dependencies:
    "@types/node": "*"
  checksum: 9545cc532c9218754443f48a0c98c1a9ba4af1fe54a3425c95de75ff3158147bb39e666cb7c6bf98cc56a9c6dc7b4ce5b2cbdae6b55d5942e50c81b76ed6b825
  languageName: node
  linkType: hard

"@types/crypto-js@npm:^4.2.2":
  version: 4.2.2
  resolution: "@types/crypto-js@npm:4.2.2"
  checksum: 727daa0d2db35f0abefbab865c23213b6ee6a270e27e177939bbe4b70d1e84c2202d9fac4ea84859c4b4d49a4ee50f948f601327a39b69ec013288018ba07ca5
  languageName: node
  linkType: hard

"@types/d3-array@npm:^3.0.3":
  version: 3.2.1
  resolution: "@types/d3-array@npm:3.2.1"
  checksum: 8a41cee0969e53bab3f56cc15c4e6c9d76868d6daecb2b7d8c9ce71e0ececccc5a8239697cc52dadf5c665f287426de5c8ef31a49e7ad0f36e8846889a383df4
  languageName: node
  linkType: hard

"@types/d3-color@npm:*":
  version: 3.1.3
  resolution: "@types/d3-color@npm:3.1.3"
  checksum: 8a0e79a709929502ec4effcee2c786465b9aec51b653ba0b5d05dbfec3e84f418270dd603002d94021885061ff592f614979193bd7a02ad76317f5608560e357
  languageName: node
  linkType: hard

"@types/d3-ease@npm:^3.0.0":
  version: 3.0.2
  resolution: "@types/d3-ease@npm:3.0.2"
  checksum: 0885219966294bfc99548f37297e1c75e75da812a5f3ec941977ebb57dcab0a25acec5b2bbd82d09a49d387daafca08521ca269b7e4c27ddca7768189e987b54
  languageName: node
  linkType: hard

"@types/d3-interpolate@npm:^3.0.1":
  version: 3.0.4
  resolution: "@types/d3-interpolate@npm:3.0.4"
  dependencies:
    "@types/d3-color": "*"
  checksum: efd2770e174e84fc7316fdafe03cf3688451f767dde1fa6211610137f495be7f3923db7e1723a6961a0e0e9ae0ed969f4f47c038189fa0beb1d556b447922622
  languageName: node
  linkType: hard

"@types/d3-path@npm:*":
  version: 3.1.1
  resolution: "@types/d3-path@npm:3.1.1"
  checksum: fee8f6b0d3b28a3611c7d7fda3bf2f79392ded266f54b03a220f205c42117644bdcd33dcbf4853da3cca02229f1c669d2a60d5d297a24ce459ba8271ccb26c03
  languageName: node
  linkType: hard

"@types/d3-scale@npm:^4.0.2":
  version: 4.0.9
  resolution: "@types/d3-scale@npm:4.0.9"
  dependencies:
    "@types/d3-time": "*"
  checksum: c44265a38e538983686b1b8d159abfb4e81c09b33316f3a68f0f372d38400fa950ad531644d25230cc7b48ea5adb50270fc54823f088979ade62dcd0225f7aa3
  languageName: node
  linkType: hard

"@types/d3-shape@npm:^3.1.0":
  version: 3.1.7
  resolution: "@types/d3-shape@npm:3.1.7"
  dependencies:
    "@types/d3-path": "*"
  checksum: 776b982e2c4fc04763782af5100993c02bca338632ff2c76d2423ace398300ba7c48cd745f95b5f51edefabbfd026c45829a146c411f8facde09ef92580b20ce
  languageName: node
  linkType: hard

"@types/d3-time@npm:*, @types/d3-time@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/d3-time@npm:3.0.4"
  checksum: 0c296884571ce70c4bbd4ea9cd1c93c0c8aee602c6c806b056187dd4ee49daf70c2f41da94b25ba0d796edf8ca83cbb87fe6d1cdda7ca669ab800170ece1c12b
  languageName: node
  linkType: hard

"@types/d3-timer@npm:^3.0.0":
  version: 3.0.2
  resolution: "@types/d3-timer@npm:3.0.2"
  checksum: 1643eebfa5f4ae3eb00b556bbc509444d88078208ec2589ddd8e4a24f230dd4cf2301e9365947e70b1bee33f63aaefab84cd907822aae812b9bc4871b98ab0e1
  languageName: node
  linkType: hard

"@types/debug@npm:^4.1.6":
  version: 4.1.12
  resolution: "@types/debug@npm:4.1.12"
  dependencies:
    "@types/ms": "*"
  checksum: 47876a852de8240bfdaf7481357af2b88cb660d30c72e73789abf00c499d6bc7cd5e52f41c915d1b9cd8ec9fef5b05688d7b7aef17f7f272c2d04679508d1053
  languageName: node
  linkType: hard

"@types/estree@npm:1.0.8, @types/estree@npm:^1.0.0, @types/estree@npm:^1.0.6":
  version: 1.0.8
  resolution: "@types/estree@npm:1.0.8"
  checksum: bd93e2e415b6f182ec4da1074e1f36c480f1d26add3e696d54fb30c09bc470897e41361c8fd957bf0985024f8fbf1e6e2aff977d79352ef7eb93a5c6dcff6c11
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:^5.0.0":
  version: 5.0.7
  resolution: "@types/express-serve-static-core@npm:5.0.7"
  dependencies:
    "@types/node": "*"
    "@types/qs": "*"
    "@types/range-parser": "*"
    "@types/send": "*"
  checksum: 3539f5866720c081053daeb97d6786614791b382ec2f30b46f05c09076c99ae14c87755b00d8367f7d456535191594d67336dbaa764e8fbbba9ca3dfb15dae00
  languageName: node
  linkType: hard

"@types/express@npm:^5.0.1":
  version: 5.0.3
  resolution: "@types/express@npm:5.0.3"
  dependencies:
    "@types/body-parser": "*"
    "@types/express-serve-static-core": ^5.0.0
    "@types/serve-static": "*"
  checksum: bb6f10c14c8e3cce07f79ee172688aa9592852abd7577b663cd0c2054307f172c2b2b36468c918fed0d4ac359b99695807b384b3da6157dfa79acbac2226b59b
  languageName: node
  linkType: hard

"@types/fs-extra@npm:9.0.13, @types/fs-extra@npm:^9.0.11":
  version: 9.0.13
  resolution: "@types/fs-extra@npm:9.0.13"
  dependencies:
    "@types/node": "*"
  checksum: add79e212acd5ac76b97b9045834e03a7996aef60a814185e0459088fd290519a3c1620865d588fa36c4498bf614210d2a703af5cf80aa1dbc125db78f6edac3
  languageName: node
  linkType: hard

"@types/fs-extra@npm:^11.0.4":
  version: 11.0.4
  resolution: "@types/fs-extra@npm:11.0.4"
  dependencies:
    "@types/jsonfile": "*"
    "@types/node": "*"
  checksum: 242cb84157631f057f76495c8220707541882c00a00195b603d937fb55e471afecebcb089bab50233ed3a59c69fd68bf65c1f69dd7fafe2347e139cc15b9b0e5
  languageName: node
  linkType: hard

"@types/http-cache-semantics@npm:*":
  version: 4.0.4
  resolution: "@types/http-cache-semantics@npm:4.0.4"
  checksum: 7f4dd832e618bc1e271be49717d7b4066d77c2d4eed5b81198eb987e532bb3e1c7e02f45d77918185bad936f884b700c10cebe06305f50400f382ab75055f9e8
  languageName: node
  linkType: hard

"@types/http-errors@npm:*":
  version: 2.0.5
  resolution: "@types/http-errors@npm:2.0.5"
  checksum: a88da669366bc483e8f3b3eb3d34ada5f8d13eeeef851b1204d77e2ba6fc42aba4566d877cca5c095204a3f4349b87fe397e3e21288837bdd945dd514120755b
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 97ed0cb44d4070aecea772b7b2e2ed971e10c81ec87dd4ecc160322ffa55ff330dace1793489540e3e318d90942064bb697cc0f8989391797792d919737b3b98
  languageName: node
  linkType: hard

"@types/jsonfile@npm:*":
  version: 6.1.4
  resolution: "@types/jsonfile@npm:6.1.4"
  dependencies:
    "@types/node": "*"
  checksum: 309fda20eb5f1cf68f2df28931afdf189c5e7e6bec64ac783ce737bb98908d57f6f58757ad5da9be37b815645a6f914e2d4f3ac66c574b8fe1ba6616284d0e97
  languageName: node
  linkType: hard

"@types/keyv@npm:^3.1.4":
  version: 3.1.4
  resolution: "@types/keyv@npm:3.1.4"
  dependencies:
    "@types/node": "*"
  checksum: e009a2bfb50e90ca9b7c6e8f648f8464067271fd99116f881073fa6fa76dc8d0133181dd65e6614d5fb1220d671d67b0124aef7d97dc02d7e342ab143a47779d
  languageName: node
  linkType: hard

"@types/long@npm:^4.0.1":
  version: 4.0.2
  resolution: "@types/long@npm:4.0.2"
  checksum: d16cde7240d834cf44ba1eaec49e78ae3180e724cd667052b194a372f350d024cba8dd3f37b0864931683dab09ca935d52f0c4c1687178af5ada9fc85b0635f4
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.5
  resolution: "@types/mime@npm:1.3.5"
  checksum: e29a5f9c4776f5229d84e525b7cd7dd960b51c30a0fb9a028c0821790b82fca9f672dab56561e2acd9e8eed51d431bde52eafdfef30f643586c4162f1aecfc78
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 2.1.0
  resolution: "@types/ms@npm:2.1.0"
  checksum: 532d2ebb91937ccc4a89389715e5b47d4c66e708d15942fe6cc25add6dc37b2be058230a327dd50f43f89b8b6d5d52b74685a9e8f70516edfc9bdd6be910eff4
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:>=13.7.0, @types/node@npm:^24.0.10":
  version: 24.0.12
  resolution: "@types/node@npm:24.0.12"
  dependencies:
    undici-types: ~7.8.0
  checksum: fad1d824df644492f85fc1d29ae500672703145a1599ab59b48897a6fb868ed5065eb74c48027ac9ec68b2ad9557fe214a959884abc6e4cd82dbd85a7eb33b68
  languageName: node
  linkType: hard

"@types/node@npm:^20.13.0":
  version: 20.19.6
  resolution: "@types/node@npm:20.19.6"
  dependencies:
    undici-types: ~6.21.0
  checksum: 497c9b72c0a0ec7cbc2f3a2a4025822827ea13c7ed99d54c26a3e87371bafa2618cbe5dc1ca0938b57282379ddc115b10b790012fb09f28c4e9f994b05dd03c2
  languageName: node
  linkType: hard

"@types/node@npm:^22.7.7":
  version: 22.16.2
  resolution: "@types/node@npm:22.16.2"
  dependencies:
    undici-types: ~6.21.0
  checksum: 3ec83f9276c7915039953c6d96d33c7505d8a72eaad56fb400275174630d57301d6c9bdb134320c7dbdfe7662764d0ed2ec5e9d4420a2a79ea775b01a29aaf00
  languageName: node
  linkType: hard

"@types/os-utils@npm:^0.0.4":
  version: 0.0.4
  resolution: "@types/os-utils@npm:0.0.4"
  checksum: 4fb61d7054bac0d988fbc10586c2b7f1843ad9e78507f916b6c144acff65777d9982e85daa61a7bff6ca75c33d4ade25c5497bc3fdfa343cb8a3980dd495f621
  languageName: node
  linkType: hard

"@types/plist@npm:^3.0.1":
  version: 3.0.5
  resolution: "@types/plist@npm:3.0.5"
  dependencies:
    "@types/node": "*"
    xmlbuilder: ">=11.0.1"
  checksum: 71417189c9bc0d0cb4595106cea7c7a8a7274f64d2e9c4dd558efd7993bcfdada58be6917189e3be7c455fe4e5557004658fd13bd12254eafed8c56e0868b59e
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.14.0
  resolution: "@types/qs@npm:6.14.0"
  checksum: 1909205514d22b3cbc7c2314e2bd8056d5f05dfb21cf4377f0730ee5e338ea19957c41735d5e4806c746176563f50005bbab602d8358432e25d900bdf4970826
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.7
  resolution: "@types/range-parser@npm:1.2.7"
  checksum: 95640233b689dfbd85b8c6ee268812a732cf36d5affead89e806fe30da9a430767af8ef2cd661024fd97e19d61f3dec75af2df5e80ec3bea000019ab7028629a
  languageName: node
  linkType: hard

"@types/react-dom@npm:^19.1.2":
  version: 19.1.6
  resolution: "@types/react-dom@npm:19.1.6"
  peerDependencies:
    "@types/react": ^19.0.0
  checksum: b5b20b7f0797f34c5a11915b74dcf8b3b7a9da9fea90279975ce6f150ca5d31bb069dbb0838638a5e9e168098aa4bb4a6f61d078efa1bbb55d7f0bdfe47bb142
  languageName: node
  linkType: hard

"@types/react@npm:^19.1.2":
  version: 19.1.8
  resolution: "@types/react@npm:19.1.8"
  dependencies:
    csstype: ^3.0.2
  checksum: 17e0c74d9c01214938fa805aaa8b97925bf3c5514e88fdf94bec42c0a6d4abbc63d4e30255db176f46fd7f0aa89f8085b9b2b2fa5abaffbbf7e5009386ada892
  languageName: node
  linkType: hard

"@types/responselike@npm:^1.0.0":
  version: 1.0.3
  resolution: "@types/responselike@npm:1.0.3"
  dependencies:
    "@types/node": "*"
  checksum: 6ac4b35723429b11b117e813c7acc42c3af8b5554caaf1fc750404c1ae59f9b7376bc69b9e9e194a5a97357a597c2228b7173d317320f0360d617b6425212f58
  languageName: node
  linkType: hard

"@types/semver@npm:^7.7.0":
  version: 7.7.0
  resolution: "@types/semver@npm:7.7.0"
  checksum: d488eaeddb23879a0a8a759bed667e1a76cb0dd4d23e3255538e24c189db387357953ca9e7a3bda2bb7f95e84cac8fe0db4fbe6b3456e893043337732d1d23cc
  languageName: node
  linkType: hard

"@types/send@npm:*":
  version: 0.17.5
  resolution: "@types/send@npm:0.17.5"
  dependencies:
    "@types/mime": ^1
    "@types/node": "*"
  checksum: bff5add75eb178c3b80bebc422db483c76eeb2cb5016508c952e4fc67d968794f9c709b978d086bf60e4d6fbfe8c0b77e99a7603a615c671c1f97f808458d4a8
  languageName: node
  linkType: hard

"@types/serve-static@npm:*":
  version: 1.15.8
  resolution: "@types/serve-static@npm:1.15.8"
  dependencies:
    "@types/http-errors": "*"
    "@types/node": "*"
    "@types/send": "*"
  checksum: 41e0fb40bfdf3b5c2ac997c5dd5d58af9229e6a325dab1cf5f73b488b09635d933c1aa6f0e3265d6df8b45be0d09af36a9ffe90175088726f1db6bf104bf9ecf
  languageName: node
  linkType: hard

"@types/uuid@npm:^10.0.0":
  version: 10.0.0
  resolution: "@types/uuid@npm:10.0.0"
  checksum: e3958f8b0fe551c86c14431f5940c3470127293280830684154b91dc7eb3514aeb79fe3216968833cf79d4d1c67f580f054b5be2cd562bebf4f728913e73e944
  languageName: node
  linkType: hard

"@types/uuid@npm:^9.0.1":
  version: 9.0.8
  resolution: "@types/uuid@npm:9.0.8"
  checksum: b8c60b7ba8250356b5088302583d1704a4e1a13558d143c549c408bf8920535602ffc12394ede77f8a8083511b023704bc66d1345792714002bfa261b17c5275
  languageName: node
  linkType: hard

"@types/verror@npm:^1.10.3":
  version: 1.10.11
  resolution: "@types/verror@npm:1.10.11"
  checksum: 647a8c43f1510a7ed113426bc428e4d6914da5912946d77b1f6e37937493bc288f49656e1114794f0e5841c14cc1582887cf605952e4e4e0e77e3cd825790fad
  languageName: node
  linkType: hard

"@types/which@npm:^3.0.4":
  version: 3.0.4
  resolution: "@types/which@npm:3.0.4"
  checksum: 2eed998c2471862d95c150e2a8bc806e395215ea963db6e3e5df389884decccb6d448b6a5a352de5263866179835651fd145110b3b79f5e568a90bc8662acd50
  languageName: node
  linkType: hard

"@types/yauzl@npm:^2.9.1":
  version: 2.10.3
  resolution: "@types/yauzl@npm:2.10.3"
  dependencies:
    "@types/node": "*"
  checksum: 5ee966ea7bd6b2802f31ad4281c92c4c0b6dfa593c378a2582c58541fa113bec3d70eb0696b34ad95e8e6861a884cba6c3e351285816693ed176222f840a8c08
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:8.36.0":
  version: 8.36.0
  resolution: "@typescript-eslint/eslint-plugin@npm:8.36.0"
  dependencies:
    "@eslint-community/regexpp": ^4.10.0
    "@typescript-eslint/scope-manager": 8.36.0
    "@typescript-eslint/type-utils": 8.36.0
    "@typescript-eslint/utils": 8.36.0
    "@typescript-eslint/visitor-keys": 8.36.0
    graphemer: ^1.4.0
    ignore: ^7.0.0
    natural-compare: ^1.4.0
    ts-api-utils: ^2.1.0
  peerDependencies:
    "@typescript-eslint/parser": ^8.36.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: bd0368e2480cfe693e50525160db1d97ae8c3266bc0b0f35e58a1c554a9fba134ff186d29289e8352d52ceaee023e4bf9ec8eb56c0d1b54fbded9d4356c41adc
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:8.36.0":
  version: 8.36.0
  resolution: "@typescript-eslint/parser@npm:8.36.0"
  dependencies:
    "@typescript-eslint/scope-manager": 8.36.0
    "@typescript-eslint/types": 8.36.0
    "@typescript-eslint/typescript-estree": 8.36.0
    "@typescript-eslint/visitor-keys": 8.36.0
    debug: ^4.3.4
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 12560c00377e66cfbd9c2e6073e10a80c75490f7af20dbfc6fa0685b7a44bd60a84c0ae1fdcba34cca23b7209f3157c046e20815f7c4af0f59a7fefa5c73e67c
  languageName: node
  linkType: hard

"@typescript-eslint/project-service@npm:8.36.0":
  version: 8.36.0
  resolution: "@typescript-eslint/project-service@npm:8.36.0"
  dependencies:
    "@typescript-eslint/tsconfig-utils": ^8.36.0
    "@typescript-eslint/types": ^8.36.0
    debug: ^4.3.4
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 60a23703689d2882f1d4026c1ca7a7601adb6ed04ccc7596cbfef07bb2f03dd473f414b1ed8251dceba709002e68a18c5c113ad6b6067f7455f7efcacc1cfdc4
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.36.0":
  version: 8.36.0
  resolution: "@typescript-eslint/scope-manager@npm:8.36.0"
  dependencies:
    "@typescript-eslint/types": 8.36.0
    "@typescript-eslint/visitor-keys": 8.36.0
  checksum: 6f7287f22594f73a33c79851a458b9d365dd8791da0589b029d864c7e1615806b006fac01592437de902a155023834323bd66002cbe27d8cbcaa3d5f75c63184
  languageName: node
  linkType: hard

"@typescript-eslint/tsconfig-utils@npm:8.36.0, @typescript-eslint/tsconfig-utils@npm:^8.36.0":
  version: 8.36.0
  resolution: "@typescript-eslint/tsconfig-utils@npm:8.36.0"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: c0561811b395e1ab4fb6a50746fbb369b46d31a8324d6d1742a41f6e1a3d4d543b2c0cacb56a435206ef725bd017d2d1ee35730219efd28b1e311a1620e08027
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.36.0":
  version: 8.36.0
  resolution: "@typescript-eslint/type-utils@npm:8.36.0"
  dependencies:
    "@typescript-eslint/typescript-estree": 8.36.0
    "@typescript-eslint/utils": 8.36.0
    debug: ^4.3.4
    ts-api-utils: ^2.1.0
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 16026d93aeb7e838ace10f732087876c96486707990ea4867370712b0ed8a9c1727fc9f68660c4ff9a48185f6d875d290a48efa7a76b773b8c8372df8a884287
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.36.0, @typescript-eslint/types@npm:^8.36.0":
  version: 8.36.0
  resolution: "@typescript-eslint/types@npm:8.36.0"
  checksum: cf1ef4ff0d9df9f37328d6a4c15a006d574ad162448a68e16fba6864a65150edd16ad3e0278771a5311570712a23642242ecc49f13b231287a9becf739dd32fc
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.36.0":
  version: 8.36.0
  resolution: "@typescript-eslint/typescript-estree@npm:8.36.0"
  dependencies:
    "@typescript-eslint/project-service": 8.36.0
    "@typescript-eslint/tsconfig-utils": 8.36.0
    "@typescript-eslint/types": 8.36.0
    "@typescript-eslint/visitor-keys": 8.36.0
    debug: ^4.3.4
    fast-glob: ^3.3.2
    is-glob: ^4.0.3
    minimatch: ^9.0.4
    semver: ^7.6.0
    ts-api-utils: ^2.1.0
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: e7f4442063a942b0293f95eb04ed7b5b39487b9cf463bd8b52fba90c64c1823c46453aaeba871bcfeda29198b7294ee0860f399b803f67a01972754b5e255b64
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.36.0":
  version: 8.36.0
  resolution: "@typescript-eslint/utils@npm:8.36.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.7.0
    "@typescript-eslint/scope-manager": 8.36.0
    "@typescript-eslint/types": 8.36.0
    "@typescript-eslint/typescript-estree": 8.36.0
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: b6bd1c5e45011b996de792041211a02d2334e7e6b1be5902b2ca0fd3c8fc2904837cbb0dc64cf244a994cd0698e46b498401bdbd285a75973e2e61cd33a3457c
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.36.0":
  version: 8.36.0
  resolution: "@typescript-eslint/visitor-keys@npm:8.36.0"
  dependencies:
    "@typescript-eslint/types": 8.36.0
    eslint-visitor-keys: ^4.2.1
  checksum: 6f5a2e61950d8f85c254b35e2ca26ef27629fdf3d9280fc6df06592bd73de08b31dfefa018f28418e311c2e64db5b599658bad54fbb4450cf2655d960569349b
  languageName: node
  linkType: hard

"@vitejs/plugin-react-swc@npm:^3.5.0":
  version: 3.10.2
  resolution: "@vitejs/plugin-react-swc@npm:3.10.2"
  dependencies:
    "@rolldown/pluginutils": 1.0.0-beta.11
    "@swc/core": ^1.11.31
  peerDependencies:
    vite: ^4 || ^5 || ^6 || ^7.0.0-beta.0
  checksum: aa4fde1893e675f6bcb1198c69ecfd22b6ba0f88ceb74180a128758cb023cef7403a02865545ffb9a6137c732dfd8e6ccaf01930962aa964f96d0e0f6658a823
  languageName: node
  linkType: hard

"@vitejs/plugin-react@npm:^4.4.1":
  version: 4.6.0
  resolution: "@vitejs/plugin-react@npm:4.6.0"
  dependencies:
    "@babel/core": ^7.27.4
    "@babel/plugin-transform-react-jsx-self": ^7.27.1
    "@babel/plugin-transform-react-jsx-source": ^7.27.1
    "@rolldown/pluginutils": 1.0.0-beta.19
    "@types/babel__core": ^7.20.5
    react-refresh: ^0.17.0
  peerDependencies:
    vite: ^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0
  checksum: 3d4c72388a7a8ce0da3ac760eb4ae249839dbe1f7b5f0e21a5d0eb55b0e54bb90d360ca975c852aaa2eb4b2423e5e9599b59d38abbe5231314aeee2619be2494
  languageName: node
  linkType: hard

"@xenova/transformers@npm:^2.17.2":
  version: 2.17.2
  resolution: "@xenova/transformers@npm:2.17.2"
  dependencies:
    "@huggingface/jinja": ^0.2.2
    onnxruntime-node: 1.14.0
    onnxruntime-web: 1.14.0
    sharp: ^0.32.0
  dependenciesMeta:
    onnxruntime-node:
      optional: true
  checksum: 5d49219995f401eedab6e0dcde6ad15ce5df0466388448703ca191e083bb0dc95692c1b539827d47399410d089cb078c47c862b0c550e34b54670fc435e83941
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:^0.8.8":
  version: 0.8.10
  resolution: "@xmldom/xmldom@npm:0.8.10"
  checksum: 4c136aec31fb3b49aaa53b6fcbfe524d02a1dc0d8e17ee35bd3bf35e9ce1344560481cd1efd086ad1a4821541482528672306d5e37cdbd187f33d7fadd3e2cf0
  languageName: node
  linkType: hard

"abbrev@npm:1, abbrev@npm:^1.0.0":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: a4a97ec07d7ea112c517036882b2ac22f3109b7b19077dc656316d07d308438aac28e4d9746dc4d84bf6b1e75b4a7b0a5f3cb30592419f128ca9a8cee3bcfa17
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: e70b209f5f408dd3a3bbd0eec4b10a2ffd64704a4a3821d0969d84928cc490a8eb60f85b78a95622c1841113edac10161c62e52f5e7d0027aa26786a8136e02e
  languageName: node
  linkType: hard

"accepts@npm:^2.0.0":
  version: 2.0.0
  resolution: "accepts@npm:2.0.0"
  dependencies:
    mime-types: ^3.0.0
    negotiator: ^1.0.0
  checksum: 49fe6c050cb6f6ff4e771b4d88324fca4d3127865f2473872e818dca127d809ba3aa8fdfc7acb51dd3c5bade7311ca6b8cfff7015ea6db2f7eb9c8444d223a4f
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn@npm:^8.15.0":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 309c6b49aedf1a2e34aaf266de06de04aab6eb097c02375c66fdeb0f64556a6a823540409914fb364d9a11bc30d79d485a2eba29af47992d3490e9886c4391c3
  languageName: node
  linkType: hard

"adm-zip@npm:^0.5.16":
  version: 0.5.16
  resolution: "adm-zip@npm:0.5.16"
  checksum: 1f4104f3462b99e1b34d78ccfbdcf47e533a9cc7f894cedec6cd67b06cc6ad0b3a45241d66df5471050c7abbdd67e5707e3959fc76d75176ed6101a5b2a580d5
  languageName: node
  linkType: hard

"agent-base@npm:6, agent-base@npm:^6.0.2":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: 4
  checksum: f52b6872cc96fd5f622071b71ef200e01c7c4c454ee68bc9accca90c98cfb39f2810e3e9aa330435835eedc8c23f4f8a15267f67c6e245d2b33757575bdac49d
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.4
  resolution: "agent-base@npm:7.1.4"
  checksum: 86a7f542af277cfbd77dd61e7df8422f90bac512953709003a1c530171a9d019d072e2400eab2b59f84b49ab9dd237be44315ca663ac73e82b3922d10ea5eafa
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.1.3, agentkeepalive@npm:^4.2.1":
  version: 4.6.0
  resolution: "agentkeepalive@npm:4.6.0"
  dependencies:
    humanize-ms: ^1.2.1
  checksum: b3cdd10efca04876defda3c7671163523fcbce20e8ef7a8f9f30919a242e32b846791c0f1a8a0269718a585805a2cdcd031779ff7b9927a1a8dd8586f8c2e8c5
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.4.1":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 7dc5e5931677a680589050f79dcbe1fefbb8fea38a955af03724229139175b433c63c68f7ae5f86cf8f65d55eb7c25f75a046723e2e58296707617ca690feae9
  languageName: node
  linkType: hard

"ajv@npm:^6.10.0, ajv@npm:^6.12.0, ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"alpine-intellect@workspace:.":
  version: 0.0.0-use.local
  resolution: "alpine-intellect@workspace:."
  dependencies:
    "@aws-sdk/client-bedrock-runtime": ^3.830.0
    "@aws-sdk/credential-providers": ^3.830.0
    "@electron/asar": latest
    "@electron/rebuild": ^4.0.1
    "@electron/universal": ^3.0.0
    "@eslint/js": ^9.25.0
    "@hookform/resolvers": ^3.9.0
    "@lancedb/lancedb": ^0.20.0
    "@radix-ui/react-accordion": ^1.2.0
    "@radix-ui/react-alert-dialog": ^1.1.1
    "@radix-ui/react-aspect-ratio": ^1.1.0
    "@radix-ui/react-avatar": ^1.1.0
    "@radix-ui/react-checkbox": ^1.1.1
    "@radix-ui/react-collapsible": ^1.1.0
    "@radix-ui/react-context-menu": ^2.2.1
    "@radix-ui/react-dialog": ^1.1.2
    "@radix-ui/react-dropdown-menu": ^2.1.1
    "@radix-ui/react-hover-card": ^1.1.1
    "@radix-ui/react-label": ^2.1.0
    "@radix-ui/react-menubar": ^1.1.1
    "@radix-ui/react-navigation-menu": ^1.2.0
    "@radix-ui/react-popover": ^1.1.1
    "@radix-ui/react-progress": ^1.1.0
    "@radix-ui/react-radio-group": ^1.2.0
    "@radix-ui/react-scroll-area": ^1.1.0
    "@radix-ui/react-select": ^2.1.1
    "@radix-ui/react-separator": ^1.1.0
    "@radix-ui/react-slider": ^1.2.0
    "@radix-ui/react-slot": ^1.1.0
    "@radix-ui/react-switch": ^1.1.0
    "@radix-ui/react-tabs": ^1.1.0
    "@radix-ui/react-toast": ^1.2.1
    "@radix-ui/react-toggle": ^1.1.0
    "@radix-ui/react-toggle-group": ^1.1.0
    "@radix-ui/react-tooltip": ^1.1.4
    "@tailwindcss/typography": ^0.5.15
    "@tanstack/react-query": ^5.56.2
    "@types/async": ^3.2.24
    "@types/better-sqlite3": ^7.6.13
    "@types/cors": ^2.8.18
    "@types/crypto-js": ^4.2.2
    "@types/express": ^5.0.1
    "@types/fs-extra": ^11.0.4
    "@types/node": ^24.0.10
    "@types/os-utils": ^0.0.4
    "@types/react": ^19.1.2
    "@types/react-dom": ^19.1.2
    "@types/semver": ^7.7.0
    "@types/uuid": ^10.0.0
    "@types/which": ^3.0.4
    "@vitejs/plugin-react": ^4.4.1
    "@vitejs/plugin-react-swc": ^3.5.0
    "@xenova/transformers": ^2.17.2
    apache-arrow: ^18.1.0
    async: ^3.2.6
    autoprefixer: ^10.4.20
    better-sqlite3: ^12.1.0
    class-variance-authority: ^0.7.1
    clsx: ^2.1.1
    cmdk: ^1.0.0
    color-name: ^2.0.0
    cors: ^2.8.5
    cross-env: ^7.0.3
    crypto-js: ^4.2.0
    date-fns: ^3.6.0
    dotenv: ^17.1.0
    electron: ^36.1.0
    electron-builder: latest
    electron-log: ^5.4.1
    embla-carousel-react: ^8.3.0
    eslint: ^9.25.0
    eslint-plugin-react-hooks: ^5.2.0
    eslint-plugin-react-refresh: ^0.4.19
    express: ^5.1.0
    fs-extra: ^11.3.0
    globals: ^16.0.0
    ignore: ^7.0.5
    input-otp: ^1.2.4
    js-tiktoken: ^1.0.20
    keytar: ^7.9.0
    lovable-tagger: ^1.1.7
    lucide-react: ^0.462.0
    next-themes: ^0.3.0
    npm-run-all: ^4.1.5
    onnxruntime-node: ^1.22.0-rev
    openai: ^5.5.1
    os-utils: ^0.0.14
    p-limit: ^6.1.0
    portfinder: ^1.0.37
    postcss: ^8.4.47
    react: ^19.1.0
    react-day-picker: ^8.10.1
    react-dom: ^19.1.0
    react-hook-form: ^7.53.0
    react-resizable-panels: ^2.1.3
    react-router-dom: ^6.26.2
    react-toastify: ^11.0.5
    recharts: ^2.15.3
    rollup: ^4.40.2
    semver: ^7.7.2
    sharp: ^0.34.2
    sonner: ^1.5.0
    sqlite3: ^5.1.7
    tailwind-merge: ^2.5.2
    tailwindcss: ^3.4.11
    tailwindcss-animate: ^1.0.7
    tiktoken: ^1.0.21
    tree-sitter: ^0.25.0
    tree-sitter-bash: ^0.25.0
    tree-sitter-c: ^0.24.1
    tree-sitter-c-sharp: ^0.23.1
    tree-sitter-cpp: ^0.23.4
    tree-sitter-css: ^0.23.2
    tree-sitter-dockerfile: ^0.0.1-security
    tree-sitter-go: ^0.23.4
    tree-sitter-html: ^0.23.2
    tree-sitter-java: ^0.23.5
    tree-sitter-javascript: ^0.23.1
    tree-sitter-json: ^0.24.8
    tree-sitter-php: ^0.23.12
    tree-sitter-python: ^0.23.6
    tree-sitter-ruby: ^0.23.1
    tree-sitter-rust: ^0.24.0
    tree-sitter-scss: ^1.0.0
    tree-sitter-typescript: ^0.23.2
    typescript: ~5.8.3
    typescript-eslint: ^8.30.1
    uri-js: ^4.4.1
    uuid: ^9.0.1
    vaul: ^0.9.3
    vite: ^6.3.5
    vite-plugin-electron: ^0.29.0
    vite-plugin-electron-renderer: ^0.14.6
    web-tree-sitter: ^0.22.6
    which: ^5.0.0
    wink-nlp-utils: ^2.1.0
    workerpool: ^9.3.2
    zod: ^3.23.8
  languageName: unknown
  linkType: soft

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 0ee8a9bdbe882c90464d75d1f55cf027f5458650c4bd1f0467e65aec38ccccda07ca5844969ee77ed46d04e7dded3eaceb027e8d32f385688523fe305fa7e1de
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"apache-arrow@npm:^18.1.0":
  version: 18.1.0
  resolution: "apache-arrow@npm:18.1.0"
  dependencies:
    "@swc/helpers": ^0.5.11
    "@types/command-line-args": ^5.2.3
    "@types/command-line-usage": ^5.0.4
    "@types/node": ^20.13.0
    command-line-args: ^5.2.1
    command-line-usage: ^7.0.1
    flatbuffers: ^24.3.25
    json-bignum: ^0.0.3
    tslib: ^2.6.2
  bin:
    arrow2csv: bin/arrow2csv.js
  checksum: 40acc777503bc5d9a6bd494c647e4192e0d687fb1f7f6546cb23dc62069c68fb2b92910fe769273397aea662f9e5b93e42f80104996febb41d4aa0a29277ba6f
  languageName: node
  linkType: hard

"app-builder-bin@npm:5.0.0-alpha.12":
  version: 5.0.0-alpha.12
  resolution: "app-builder-bin@npm:5.0.0-alpha.12"
  checksum: 9ff9f5c4670d3d376b1719af3a2a88abfdf1e58652aae55a0bcd52cc3cee93e36dda8e0d24536f24c99e5531ae4e3b10531abd33c4d7cc6be8d7ca08ff08b987
  languageName: node
  linkType: hard

"app-builder-lib@npm:26.0.12":
  version: 26.0.12
  resolution: "app-builder-lib@npm:26.0.12"
  dependencies:
    "@develar/schema-utils": ~2.6.5
    "@electron/asar": 3.2.18
    "@electron/fuses": ^1.8.0
    "@electron/notarize": 2.5.0
    "@electron/osx-sign": 1.3.1
    "@electron/rebuild": 3.7.0
    "@electron/universal": 2.0.1
    "@malept/flatpak-bundler": ^0.4.0
    "@types/fs-extra": 9.0.13
    async-exit-hook: ^2.0.1
    builder-util: 26.0.11
    builder-util-runtime: 9.3.1
    chromium-pickle-js: ^0.2.0
    config-file-ts: 0.2.8-rc1
    debug: ^4.3.4
    dotenv: ^16.4.5
    dotenv-expand: ^11.0.6
    ejs: ^3.1.8
    electron-publish: 26.0.11
    fs-extra: ^10.1.0
    hosted-git-info: ^4.1.0
    is-ci: ^3.0.0
    isbinaryfile: ^5.0.0
    js-yaml: ^4.1.0
    json5: ^2.2.3
    lazy-val: ^1.0.5
    minimatch: ^10.0.0
    plist: 3.1.0
    resedit: ^1.7.0
    semver: ^7.3.8
    tar: ^6.1.12
    temp-file: ^3.4.0
    tiny-async-pool: 1.3.0
  peerDependencies:
    dmg-builder: 26.0.12
    electron-builder-squirrel-windows: 26.0.12
  checksum: c434525c9a8883a25ab7a31ca57abd7e5c7751a518277d3597486d0b2947796712fa35ff714305450358199d22f7140979c1d947273808819bd5f44248004a04
  languageName: node
  linkType: hard

"aproba@npm:^1.0.3 || ^2.0.0":
  version: 2.0.0
  resolution: "aproba@npm:2.0.0"
  checksum: 5615cadcfb45289eea63f8afd064ab656006361020e1735112e346593856f87435e02d8dcc7ff0d11928bc7d425f27bc7c2a84f6c0b35ab0ff659c814c138a24
  languageName: node
  linkType: hard

"are-we-there-yet@npm:^3.0.0":
  version: 3.0.1
  resolution: "are-we-there-yet@npm:3.0.1"
  dependencies:
    delegates: ^1.0.0
    readable-stream: ^3.6.0
  checksum: 52590c24860fa7173bedeb69a4c05fb573473e860197f618b9a28432ee4379049336727ae3a1f9c4cb083114601c1140cee578376164d0e651217a9843f9fe83
  languageName: node
  linkType: hard

"arg@npm:^5.0.2":
  version: 5.0.2
  resolution: "arg@npm:5.0.2"
  checksum: 6c69ada1a9943d332d9e5382393e897c500908d91d5cb735a01120d5f71daf1b339b7b8980cbeaba8fd1afc68e658a739746179e4315a26e8a28951ff9930078
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"aria-hidden@npm:^1.2.4":
  version: 1.2.6
  resolution: "aria-hidden@npm:1.2.6"
  dependencies:
    tslib: ^2.0.0
  checksum: 56409c55c43ad917607f3f3aa67748dcf30a27e8bb5cb3c5d86b43e38babadd63cd77731a27bc8a8c4332c2291741ed92333bf7ca45f8b99ebc87b94a8070a6e
  languageName: node
  linkType: hard

"array-back@npm:^3.0.1, array-back@npm:^3.1.0":
  version: 3.1.0
  resolution: "array-back@npm:3.1.0"
  checksum: 7205004fcd0f9edd926db921af901b083094608d5b265738d0290092f9822f73accb468e677db74c7c94ef432d39e5ed75a7b1786701e182efb25bbba9734209
  languageName: node
  linkType: hard

"array-back@npm:^6.2.2":
  version: 6.2.2
  resolution: "array-back@npm:6.2.2"
  checksum: baae1e3a1687300a307d3bdf09715f6415e1099b5729d3d8e397309fb1e43d90b939d694602892172aaca7e0aeed38da89d04aa4951637d31c2a21350809e003
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    is-array-buffer: ^3.0.5
  checksum: 0ae3786195c3211b423e5be8dd93357870e6fb66357d81da968c2c39ef43583ef6eece1f9cb1caccdae4806739c65dea832b44b8593414313cd76a89795fca63
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: ^1.0.1
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
    is-array-buffer: ^3.0.4
  checksum: b1d1fd20be4e972a3779b1569226f6740170dca10f07aa4421d42cefeec61391e79c557cda8e771f5baefe47d878178cd4438f60916ce831813c08132bced765
  languageName: node
  linkType: hard

"assert-plus@npm:^1.0.0":
  version: 1.0.0
  resolution: "assert-plus@npm:1.0.0"
  checksum: 19b4340cb8f0e6a981c07225eacac0e9d52c2644c080198765d63398f0075f83bbc0c8e95474d54224e297555ad0d631c1dcd058adb1ddc2437b41a6b424ac64
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: 876231688c66400473ba505731df37ea436e574dd524520294cc3bbc54ea40334865e01fa0d074d74d036ee874ee7e62f486ea38bc421ee8e6a871c06f011766
  languageName: node
  linkType: hard

"async-exit-hook@npm:^2.0.1":
  version: 2.0.1
  resolution: "async-exit-hook@npm:2.0.1"
  checksum: b72cbdd19ea90fa33a3a57b0dbff83e4bf2f4e4acd70b2b3847a588f9f16a45d38590ee13f285375dd919c224f60fa58dc3d315a87678d3aa24ff686d1c0200a
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 9102e246d1ed9b37ac36f57f0a6ca55226876553251a31fc80677e71471f463a54c872dc78d5d7f80740c8ba624395cccbe8b60f7b690c4418f487d8e9fd1106
  languageName: node
  linkType: hard

"async@npm:^3.2.3, async@npm:^3.2.6":
  version: 3.2.6
  resolution: "async@npm:3.2.6"
  checksum: ee6eb8cd8a0ab1b58bd2a3ed6c415e93e773573a91d31df9d5ef559baafa9dab37d3b096fa7993e84585cac3697b2af6ddb9086f45d3ac8cae821bb2aab65682
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 7b78c451df768adba04e2d02e63e2d0bf3b07adcd6e42b4cf665cb7ce899bedd344c69a1dcbce355b5f972d597b25aaa1c1742b52cffd9caccb22f348114f6be
  languageName: node
  linkType: hard

"at-least-node@npm:^1.0.0":
  version: 1.0.0
  resolution: "at-least-node@npm:1.0.0"
  checksum: 463e2f8e43384f1afb54bc68485c436d7622acec08b6fad269b421cb1d29cebb5af751426793d0961ed243146fe4dc983402f6d5a51b720b277818dbf6f2e49e
  languageName: node
  linkType: hard

"autoprefixer@npm:^10.4.20":
  version: 10.4.21
  resolution: "autoprefixer@npm:10.4.21"
  dependencies:
    browserslist: ^4.24.4
    caniuse-lite: ^1.0.30001702
    fraction.js: ^4.3.7
    normalize-range: ^0.1.2
    picocolors: ^1.1.1
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.1.0
  bin:
    autoprefixer: bin/autoprefixer
  checksum: 11770ce635a0520e457eaf2ff89056cd57094796a9f5d6d9375513388a5a016cd947333dcfd213b822fdd8a0b43ce68ae4958e79c6f077c41d87444c8cca0235
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: ^1.0.0
  checksum: 1aa3ffbfe6578276996de660848b6e95669d9a95ad149e3dd0c0cda77db6ee1dbd9d1dd723b65b6d277b882dd0c4b91a654ae9d3cf9e1254b7e93e4908d78fd3
  languageName: node
  linkType: hard

"b4a@npm:^1.6.4":
  version: 1.6.7
  resolution: "b4a@npm:1.6.7"
  checksum: afe4e239b49c0ef62236fe0d788ac9bd9d7eac7e9855b0d1835593cd0efcc7be394f9cc28a747a2ed2cdcb0a48c3528a551a196f472eb625457c711169c9efa2
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"bare-events@npm:^2.2.0, bare-events@npm:^2.5.4":
  version: 2.6.0
  resolution: "bare-events@npm:2.6.0"
  checksum: d6e95797ea539b39e2c31391d8410d3e0f959b19aa7cc3b7ba1f8acf188c7bd4cb5c596d6d4e6ad3836ffdf5285c8647d82667ca88fb9a1f4ab5de3f86fac3b9
  languageName: node
  linkType: hard

"bare-fs@npm:^4.0.1":
  version: 4.1.6
  resolution: "bare-fs@npm:4.1.6"
  dependencies:
    bare-events: ^2.5.4
    bare-path: ^3.0.0
    bare-stream: ^2.6.4
  peerDependencies:
    bare-buffer: "*"
  peerDependenciesMeta:
    bare-buffer:
      optional: true
  checksum: f623749f4fb5ddba2b7a9a2e58a5b8a1d4a3aa0f37ff26d271e14e01fd8bfc6e60dfa4a9ccb0e7f4abdceddce89d56f395498465270063af6d5313293e0edeea
  languageName: node
  linkType: hard

"bare-os@npm:^3.0.1":
  version: 3.6.1
  resolution: "bare-os@npm:3.6.1"
  checksum: 2fcdbaa631e02e2b7a4a38ded4586ae8bef2d329c6933b9dca8c543b4af0ac3c257fdf0ff3339b83259e179e07873f300e61c75c0a1e6b796c0214b1fbae8696
  languageName: node
  linkType: hard

"bare-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "bare-path@npm:3.0.0"
  dependencies:
    bare-os: ^3.0.1
  checksum: 51d559515f332f62cf9c37c38f2640c1b84b5e8c9de454b70baf029f806058cf94c51d6a0dfec0025cc7760f2069dc3e16c82f0d24f4a9ddb18c829bf9c0206d
  languageName: node
  linkType: hard

"bare-stream@npm:^2.6.4":
  version: 2.6.5
  resolution: "bare-stream@npm:2.6.5"
  dependencies:
    streamx: ^2.21.0
  peerDependencies:
    bare-buffer: "*"
    bare-events: "*"
  peerDependenciesMeta:
    bare-buffer:
      optional: true
    bare-events:
      optional: true
  checksum: 6a3d4baf8ded0bdc465b7b0b65dfbb8e40f7520ee8899adcae5fd37949d5c520412164116659750ad841215b03ce761fe252a626cd4fe3ec9df0440c6fd07a96
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1, base64-js@npm:^1.5.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"better-sqlite3@npm:^12.1.0":
  version: 12.2.0
  resolution: "better-sqlite3@npm:12.2.0"
  dependencies:
    bindings: ^1.5.0
    node-gyp: latest
    prebuild-install: ^7.1.1
  checksum: b752119ea306c5a70d5bacdf5607fd69b39142b7c8c30d72f530047fedad0b651195b37e8a8067f106d3c56d6913dc0077f1d658916f07bcdad6841384d2bb1b
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"bindings@npm:^1.5.0":
  version: 1.5.0
  resolution: "bindings@npm:1.5.0"
  dependencies:
    file-uri-to-path: 1.0.0
  checksum: 65b6b48095717c2e6105a021a7da4ea435aa8d3d3cd085cb9e85bcb6e5773cf318c4745c3f7c504412855940b585bdf9b918236612a1c7a7942491de176f1ae7
  languageName: node
  linkType: hard

"bl@npm:^4.0.3, bl@npm:^4.1.0":
  version: 4.1.0
  resolution: "bl@npm:4.1.0"
  dependencies:
    buffer: ^5.5.0
    inherits: ^2.0.4
    readable-stream: ^3.4.0
  checksum: 9e8521fa7e83aa9427c6f8ccdcba6e8167ef30cc9a22df26effcc5ab682ef91d2cbc23a239f945d099289e4bbcfae7a192e9c28c84c6202e710a0dfec3722662
  languageName: node
  linkType: hard

"body-parser@npm:^2.2.0":
  version: 2.2.0
  resolution: "body-parser@npm:2.2.0"
  dependencies:
    bytes: ^3.1.2
    content-type: ^1.0.5
    debug: ^4.4.0
    http-errors: ^2.0.0
    iconv-lite: ^0.6.3
    on-finished: ^2.4.1
    qs: ^6.14.0
    raw-body: ^3.0.0
    type-is: ^2.0.0
  checksum: 7fe3a2d288f0b632528d6ccb90052d1a9492c5b79d5716d32c8de1f5fb8237b0d31ee5050e1d0b7ff143a492ff151804612c6e2686a222a1d4c9e2e6531b8fb2
  languageName: node
  linkType: hard

"boolean@npm:^3.0.1":
  version: 3.2.0
  resolution: "boolean@npm:3.2.0"
  checksum: fb29535b8bf710ef45279677a86d14f5185d604557204abd2ca5fa3fb2a5c80e04d695c8dbf13ab269991977a79bb6c04b048220a6b2a3849853faa94f4a7d77
  languageName: node
  linkType: hard

"bowser@npm:^2.11.0":
  version: 2.11.0
  resolution: "bowser@npm:2.11.0"
  checksum: 29c3f01f22e703fa6644fc3b684307442df4240b6e10f6cfe1b61c6ca5721073189ca97cdeedb376081148c8518e33b1d818a57f781d70b0b70e1f31fb48814f
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.12
  resolution: "brace-expansion@npm:1.1.12"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: 12cb6d6310629e3048cadb003e1aca4d8c9bb5c67c3c321bafdd7e7a50155de081f78ea3e0ed92ecc75a9015e784f301efc8132383132f4f7904ad1ac529c562
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: ^1.0.0
  checksum: 01dff195e3646bc4b0d27b63d9bab84d2ebc06121ff5013ad6e5356daa5a9d6b60fa26cf73c74797f2dc3fbec112af13578d51f75228c1112b26c790a87b0488
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0, browserslist@npm:^4.24.4":
  version: 4.25.1
  resolution: "browserslist@npm:4.25.1"
  dependencies:
    caniuse-lite: ^1.0.30001726
    electron-to-chromium: ^1.5.173
    node-releases: ^2.0.19
    update-browserslist-db: ^1.1.3
  bin:
    browserslist: cli.js
  checksum: 2a7e4317e809b09a436456221a1fcb8ccbd101bada187ed217f7a07a9e42ced822c7c86a0a4333d7d1b4e6e0c859d201732ffff1585d6bcacd8d226f6ddce7e3
  languageName: node
  linkType: hard

"buffer-crc32@npm:~0.2.3":
  version: 0.2.13
  resolution: "buffer-crc32@npm:0.2.13"
  checksum: 06252347ae6daca3453b94e4b2f1d3754a3b146a111d81c68924c22d91889a40623264e95e67955b1cb4a68cbedf317abeabb5140a9766ed248973096db5ce1c
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"buffer@npm:^5.1.0, buffer@npm:^5.5.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: ^1.3.1
    ieee754: ^1.1.13
  checksum: e2cf8429e1c4c7b8cbd30834ac09bd61da46ce35f5c22a78e6c2f04497d6d25541b16881e30a019c6fd3154150650ccee27a308eff3e26229d788bbdeb08ab84
  languageName: node
  linkType: hard

"builder-util-runtime@npm:9.3.1":
  version: 9.3.1
  resolution: "builder-util-runtime@npm:9.3.1"
  dependencies:
    debug: ^4.3.4
    sax: ^1.2.4
  checksum: 00a6cc472977d7e97ea18fafbb5861659b92207b6841bbc3b172dc310e59abcfe89302397e9c7989a1afa2669f6b253fcb3adf28096d5e14ff0acc2f4337e7b2
  languageName: node
  linkType: hard

"builder-util@npm:26.0.11":
  version: 26.0.11
  resolution: "builder-util@npm:26.0.11"
  dependencies:
    7zip-bin: ~5.2.0
    "@types/debug": ^4.1.6
    app-builder-bin: 5.0.0-alpha.12
    builder-util-runtime: 9.3.1
    chalk: ^4.1.2
    cross-spawn: ^7.0.6
    debug: ^4.3.4
    fs-extra: ^10.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.0
    is-ci: ^3.0.0
    js-yaml: ^4.1.0
    sanitize-filename: ^1.6.3
    source-map-support: ^0.5.19
    stat-mode: ^1.0.0
    temp-file: ^3.4.0
    tiny-async-pool: 1.3.0
  checksum: cf5f7ceb5cd22c78950a8218467c140d6c99da035c7c20c327bf51c4a6c8bd8f289a25da6ae108a6c88085f4391ca203090c15ce75f6438be6657cff3b466533
  languageName: node
  linkType: hard

"bytes@npm:3.1.2, bytes@npm:^3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: e4bcd3948d289c5127591fbedf10c0b639ccbf00243504e4e127374a15c3bc8eed0d28d4aaab08ff6f1cf2abc0cce6ba3085ed32f4f90e82a5683ce0014e1b6e
  languageName: node
  linkType: hard

"cacache@npm:^15.2.0":
  version: 15.3.0
  resolution: "cacache@npm:15.3.0"
  dependencies:
    "@npmcli/fs": ^1.0.0
    "@npmcli/move-file": ^1.0.1
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    glob: ^7.1.4
    infer-owner: ^1.0.4
    lru-cache: ^6.0.0
    minipass: ^3.1.1
    minipass-collect: ^1.0.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.2
    mkdirp: ^1.0.3
    p-map: ^4.0.0
    promise-inflight: ^1.0.1
    rimraf: ^3.0.2
    ssri: ^8.0.1
    tar: ^6.0.2
    unique-filename: ^1.1.1
  checksum: a07327c27a4152c04eb0a831c63c00390d90f94d51bb80624a66f4e14a6b6360bbf02a84421267bd4d00ca73ac9773287d8d7169e8d2eafe378d2ce140579db8
  languageName: node
  linkType: hard

"cacache@npm:^16.1.0":
  version: 16.1.3
  resolution: "cacache@npm:16.1.3"
  dependencies:
    "@npmcli/fs": ^2.1.0
    "@npmcli/move-file": ^2.0.0
    chownr: ^2.0.0
    fs-minipass: ^2.1.0
    glob: ^8.0.1
    infer-owner: ^1.0.4
    lru-cache: ^7.7.1
    minipass: ^3.1.6
    minipass-collect: ^1.0.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    mkdirp: ^1.0.4
    p-map: ^4.0.0
    promise-inflight: ^1.0.1
    rimraf: ^3.0.2
    ssri: ^9.0.0
    tar: ^6.1.11
    unique-filename: ^2.0.0
  checksum: d91409e6e57d7d9a3a25e5dcc589c84e75b178ae8ea7de05cbf6b783f77a5fae938f6e8fda6f5257ed70000be27a681e1e44829251bfffe4c10216002f8f14e6
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": ^4.0.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^10.0.1
    minipass: ^7.0.3
    minipass-collect: ^2.0.1
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^7.0.2
    ssri: ^12.0.0
    tar: ^7.4.3
    unique-filename: ^4.0.0
  checksum: e95684717de6881b4cdaa949fa7574e3171946421cd8291769dd3d2417dbf7abf4aa557d1f968cca83dcbc95bed2a281072b09abfc977c942413146ef7ed4525
  languageName: node
  linkType: hard

"cacheable-lookup@npm:^5.0.3":
  version: 5.0.4
  resolution: "cacheable-lookup@npm:5.0.4"
  checksum: 763e02cf9196bc9afccacd8c418d942fc2677f22261969a4c2c2e760fa44a2351a81557bd908291c3921fe9beb10b976ba8fa50c5ca837c5a0dd945f16468f2d
  languageName: node
  linkType: hard

"cacheable-request@npm:^7.0.2":
  version: 7.0.4
  resolution: "cacheable-request@npm:7.0.4"
  dependencies:
    clone-response: ^1.0.2
    get-stream: ^5.1.0
    http-cache-semantics: ^4.0.0
    keyv: ^4.0.0
    lowercase-keys: ^2.0.0
    normalize-url: ^6.0.1
    responselike: ^2.0.0
  checksum: 0de9df773fd4e7dd9bd118959878f8f2163867e2e1ab3575ffbecbe6e75e80513dd0c68ba30005e5e5a7b377cc6162bbc00ab1db019bb4e9cb3c2f3f7a6f1ee4
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: ^1.3.0
    function-bind: ^1.1.2
  checksum: b2863d74fcf2a6948221f65d95b91b4b2d90cfe8927650b506141e669f7d5de65cea191bf788838bc40d13846b7886c5bc5c84ab96c3adbcf88ad69a72fcdc6b
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: ^1.0.0
    es-define-property: ^1.0.0
    get-intrinsic: ^1.2.4
    set-function-length: ^1.2.2
  checksum: aa2899bce917a5392fd73bd32e71799c37c0b7ab454e0ed13af7f6727549091182aade8bbb7b55f304a5bc436d543241c14090fb8a3137e9875e23f444f4f5a9
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: ^1.0.2
    get-intrinsic: ^1.3.0
  checksum: 2f6399488d1c272f56306ca60ff696575e2b7f31daf23bc11574798c84d9f2759dceb0cb1f471a85b77f28962a7ac6411f51d283ea2e45319009a19b6ccab3b2
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase-css@npm:^2.0.1":
  version: 2.0.1
  resolution: "camelcase-css@npm:2.0.1"
  checksum: 1cec2b3b3dcb5026688a470b00299a8db7d904c4802845c353dbd12d9d248d3346949a814d83bfd988d4d2e5b9904c07efe76fecd195a1d4f05b543e7c0b56b1
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001702, caniuse-lite@npm:^1.0.30001726":
  version: 1.0.30001727
  resolution: "caniuse-lite@npm:1.0.30001727"
  checksum: 2bc6112f242701198a99c17713d4409be9b404d09005f34f351ec29a4ea46c054e7aa4982bc16f06b81b7a375cbc61c937e89650170cbce84db772a376ed3963
  languageName: node
  linkType: hard

"chalk-template@npm:^0.4.0":
  version: 0.4.0
  resolution: "chalk-template@npm:0.4.0"
  dependencies:
    chalk: ^4.1.2
  checksum: 6c706802a79a7963cbce18f022b046fe86e438a67843151868852f80ea7346e975a6a9749991601e7e5d3b6a6c4852a04c53dc966a9a3d04031bd0e0ed53c819
  languageName: node
  linkType: hard

"chalk@npm:^2.4.1":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.0.2, chalk@npm:^4.1.0, chalk@npm:^4.1.1, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"chokidar@npm:^3.6.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: d2f29f499705dcd4f6f3bbed79a9ce2388cf530460122eed3b9c48efeab7a4e28739c6551fd15bec9245c6b9eeca7a32baa64694d64d9b6faeb74ddb8c4a413d
  languageName: node
  linkType: hard

"chownr@npm:^1.1.1":
  version: 1.1.4
  resolution: "chownr@npm:1.1.4"
  checksum: 115648f8eb38bac5e41c3857f3e663f9c39ed6480d1349977c4d96c95a47266fcacc5a5aabf3cb6c481e22d72f41992827db47301851766c4fd77ac21a4f081d
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: fd73a4bab48b79e66903fe1cafbdc208956f41ea4f856df883d0c7277b7ab29fd33ee65f93b2ec9192fc0169238f2f8307b7735d27c155821d886b84aa97aa8d
  languageName: node
  linkType: hard

"chromium-pickle-js@npm:^0.2.0":
  version: 0.2.0
  resolution: "chromium-pickle-js@npm:0.2.0"
  checksum: 5ccacc538b0a1ecf3484c8fb3327eae129ceee858db0f64eb0a5ff87bda096a418d0d3e6f6e0967c6334d336a2c7463f7b683ec0e1cafbe736907fa2ee2f58ca
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0":
  version: 3.9.0
  resolution: "ci-info@npm:3.9.0"
  checksum: 6b19dc9b2966d1f8c2041a838217299718f15d6c4b63ae36e4674edd2bee48f780e94761286a56aa59eb305a85fbea4ddffb7630ec063e7ec7e7e5ad42549a87
  languageName: node
  linkType: hard

"class-variance-authority@npm:^0.7.1":
  version: 0.7.1
  resolution: "class-variance-authority@npm:0.7.1"
  dependencies:
    clsx: ^2.1.1
  checksum: e05ba26ef9ec38f7c675047ce366b067d60af6c954dba08f7802af19a9460a534ae752d8fe1294fff99d0fa94a669b16ccebd87e8a20f637c0736cf2751dd2c5
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: ^3.1.0
  checksum: 2692784c6cd2fd85cfdbd11f53aea73a463a6d64a77c3e098b2b4697a20443f430c220629e1ca3b195ea5ac4a97a74c2ee411f3807abf6df2b66211fec0c0a29
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.5.0":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 1bd588289b28432e4676cb5d40505cfe3e53f2e4e10fbe05c8a710a154d6fe0ce7836844b00d6858f740f2ffe67cdc36e0fce9c7b6a8430e80e6388d5aa4956c
  languageName: node
  linkType: hard

"cli-truncate@npm:^2.1.0":
  version: 2.1.0
  resolution: "cli-truncate@npm:2.1.0"
  dependencies:
    slice-ansi: ^3.0.0
    string-width: ^4.2.0
  checksum: bf1e4e6195392dc718bf9cd71f317b6300dc4a9191d052f31046b8773230ece4fa09458813bf0e3455a5e68c0690d2ea2c197d14a8b85a7b5e01c97f4b5feb5d
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.1
    wrap-ansi: ^7.0.0
  checksum: 79648b3b0045f2e285b76fb2e24e207c6db44323581e421c3acbd0e86454cba1b37aea976ab50195a49e7384b871e6dfb2247ad7dec53c02454ac6497394cb56
  languageName: node
  linkType: hard

"clone-response@npm:^1.0.2":
  version: 1.0.3
  resolution: "clone-response@npm:1.0.3"
  dependencies:
    mimic-response: ^1.0.0
  checksum: 4e671cac39b11c60aa8ba0a450657194a5d6504df51bca3fac5b3bd0145c4f8e8464898f87c8406b83232e3bc5cca555f51c1f9c8ac023969ebfbf7f6bdabb2e
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: d06418b7335897209e77bdd430d04f882189582e67bd1f75a04565f3f07f5b3f119a9d670c943b6697d0afb100f03b866b3b8a1f91d4d02d72c4ecf2bb64b5dd
  languageName: node
  linkType: hard

"clsx@npm:^2.0.0, clsx@npm:^2.1.1":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: acd3e1ab9d8a433ecb3cc2f6a05ab95fe50b4a3cfc5ba47abb6cbf3754585fcb87b84e90c822a1f256c4198e3b41c7f6c391577ffc8678ad587fc0976b24fd57
  languageName: node
  linkType: hard

"cmdk@npm:^1.0.0":
  version: 1.1.1
  resolution: "cmdk@npm:1.1.1"
  dependencies:
    "@radix-ui/react-compose-refs": ^1.1.1
    "@radix-ui/react-dialog": ^1.1.6
    "@radix-ui/react-id": ^1.1.0
    "@radix-ui/react-primitive": ^2.0.2
  peerDependencies:
    react: ^18 || ^19 || ^19.0.0-rc
    react-dom: ^18 || ^19 || ^19.0.0-rc
  checksum: 063c3c66eba917c1968c278673cce17a9925cf4ea2d2da72718a7e09e2a103a4f1cb08eac8a7257b6613c8c8e0d273173f22097045b3dfaeaca0e05d3a2e81a7
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-name@npm:^2.0.0":
  version: 2.0.0
  resolution: "color-name@npm:2.0.0"
  checksum: 10a1addae41de2987d6b90dbd3cfade266c2e6f680ce21749911df4493b4fae07654862c6b5358bdd13e155461acb4eedaa5e0ba172bf13542cdcca10866cf2b
  languageName: node
  linkType: hard

"color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: ^1.0.0
    simple-swizzle: ^0.2.2
  checksum: c13fe7cff7885f603f49105827d621ce87f4571d78ba28ef4a3f1a104304748f620615e6bf065ecd2145d0d9dad83a3553f52bb25ede7239d18e9f81622f1cc5
  languageName: node
  linkType: hard

"color-support@npm:^1.1.3":
  version: 1.1.3
  resolution: "color-support@npm:1.1.3"
  bin:
    color-support: bin.js
  checksum: 9b7356817670b9a13a26ca5af1c21615463b500783b739b7634a0c2047c16cef4b2865d7576875c31c3cddf9dd621fa19285e628f20198b233a5cfdda6d0793b
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: ^2.0.1
    color-string: ^1.9.0
  checksum: 0579629c02c631b426780038da929cca8e8d80a40158b09811a0112a107c62e10e4aad719843b791b1e658ab4e800558f2e87ca4522c8b32349d497ecb6adeb4
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: ~1.0.0
  checksum: 49fa4aeb4916567e33ea81d088f6584749fc90c7abec76fd516bf1c5aa5c79f3584b5ba3de6b86d26ddd64bae5329c4c7479343250cfe71c75bb366eae53bb7c
  languageName: node
  linkType: hard

"command-line-args@npm:^5.2.1":
  version: 5.2.1
  resolution: "command-line-args@npm:5.2.1"
  dependencies:
    array-back: ^3.1.0
    find-replace: ^3.0.0
    lodash.camelcase: ^4.3.0
    typical: ^4.0.0
  checksum: e759519087be3cf2e86af8b9a97d3058b4910cd11ee852495be881a067b72891f6a32718fb685ee6d41531ab76b2b7bfb6602f79f882cd4b7587ff1e827982c7
  languageName: node
  linkType: hard

"command-line-usage@npm:^7.0.1":
  version: 7.0.3
  resolution: "command-line-usage@npm:7.0.3"
  dependencies:
    array-back: ^6.2.2
    chalk-template: ^0.4.0
    table-layout: ^4.1.0
    typical: ^7.1.1
  checksum: cb65d94c71ac380d6133460fa16d15c3d6dde00746498d60dcd12989fffeb90d1373230135c97e0bd7019874edd913f9df8b87b0afc7180811117342ae950ff4
  languageName: node
  linkType: hard

"commander@npm:^13.1.0":
  version: 13.1.0
  resolution: "commander@npm:13.1.0"
  checksum: 8ca2fcb33caf2aa06fba3722d7a9440921331d54019dabf906f3603313e7bf334b009b862257b44083ff65d5a3ab19e83ad73af282bd5319f01dc228bdf87ef0
  languageName: node
  linkType: hard

"commander@npm:^4.0.0":
  version: 4.1.1
  resolution: "commander@npm:4.1.1"
  checksum: d7b9913ff92cae20cb577a4ac6fcc121bd6223319e54a40f51a14740a681ad5c574fd29a57da478a5f234a6fa6c52cbf0b7c641353e03c648b1ae85ba670b977
  languageName: node
  linkType: hard

"commander@npm:^5.0.0":
  version: 5.1.0
  resolution: "commander@npm:5.1.0"
  checksum: 0b7fec1712fbcc6230fcb161d8d73b4730fa91a21dc089515489402ad78810547683f058e2a9835929c212fead1d6a6ade70db28bbb03edbc2829a9ab7d69447
  languageName: node
  linkType: hard

"compare-version@npm:^0.1.2":
  version: 0.1.2
  resolution: "compare-version@npm:0.1.2"
  checksum: 0ceaf50b5f912c8eb8eeca19375e617209d200abebd771e9306510166462e6f91ad764f33f210a3058ee27c83f2f001a7a4ca32f509da2d207d0143a3438a020
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"config-file-ts@npm:0.2.8-rc1":
  version: 0.2.8-rc1
  resolution: "config-file-ts@npm:0.2.8-rc1"
  dependencies:
    glob: ^10.3.12
    typescript: ^5.4.3
  checksum: 820547f430e6b977b0be2ff25b37b890f7541edf523017afeba5351013bd7910aa3050679e5a58cde9b8414c59ef4d5b0a215f29fa5ab74e558fa56d31b6f65e
  languageName: node
  linkType: hard

"console-control-strings@npm:^1.1.0":
  version: 1.1.0
  resolution: "console-control-strings@npm:1.1.0"
  checksum: 8755d76787f94e6cf79ce4666f0c5519906d7f5b02d4b884cf41e11dcd759ed69c57da0670afd9236d229a46e0f9cf519db0cd829c6dca820bb5a5c3def584ed
  languageName: node
  linkType: hard

"content-disposition@npm:^1.0.0":
  version: 1.0.0
  resolution: "content-disposition@npm:1.0.0"
  dependencies:
    safe-buffer: 5.2.1
  checksum: b27e2579fefe0ecf78238bb652fbc750671efce8344f0c6f05235b12433e6a965adb40906df1ac1fdde23e8f9f0e58385e44640e633165420f3f47d830ae0398
  languageName: node
  linkType: hard

"content-type@npm:^1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 566271e0a251642254cde0f845f9dd4f9856e52d988f4eb0d0dcffbb7a1f8ec98de7a5215fc628f3bce30fe2fb6fd2bc064b562d721658c59b544e2d34ea2766
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 63ae9933be5a2b8d4509daca5124e20c14d023c820258e484e32dc324d34c2754e71297c94a05784064ad27615037ef677e3f0c00469fb55f409d2bb21261035
  languageName: node
  linkType: hard

"cookie-signature@npm:^1.2.1":
  version: 1.2.2
  resolution: "cookie-signature@npm:1.2.2"
  checksum: 1ad4f9b3907c9f3673a0f0a07c0a23da7909ac6c9204c5d80a0ec102fe50ccc45f27fdf496361840d6c132c5bb0037122c0a381f856d070183d1ebe3e5e041ff
  languageName: node
  linkType: hard

"cookie@npm:^0.7.1":
  version: 0.7.2
  resolution: "cookie@npm:0.7.2"
  checksum: 9bf8555e33530affd571ea37b615ccad9b9a34febbf2c950c86787088eb00a8973690833b0f8ebd6b69b753c62669ea60cec89178c1fb007bf0749abed74f93e
  languageName: node
  linkType: hard

"core-util-is@npm:1.0.2":
  version: 1.0.2
  resolution: "core-util-is@npm:1.0.2"
  checksum: 7a4c925b497a2c91421e25bf76d6d8190f0b2359a9200dbeed136e63b2931d6294d3b1893eda378883ed363cd950f44a12a401384c609839ea616befb7927dab
  languageName: node
  linkType: hard

"cors@npm:^2.8.5":
  version: 2.8.5
  resolution: "cors@npm:2.8.5"
  dependencies:
    object-assign: ^4
    vary: ^1
  checksum: ced838404ccd184f61ab4fdc5847035b681c90db7ac17e428f3d81d69e2989d2b680cc254da0e2554f5ed4f8a341820a1ce3d1c16b499f6e2f47a1b9b07b5006
  languageName: node
  linkType: hard

"crc@npm:^3.8.0":
  version: 3.8.0
  resolution: "crc@npm:3.8.0"
  dependencies:
    buffer: ^5.1.0
  checksum: dabbc4eba223b206068b92ca82bb471d583eb6be2384a87f5c3712730cfd6ba4b13a45e8ba3ef62174d5a781a2c5ac5c20bf36cf37bba73926899bd0aa19186f
  languageName: node
  linkType: hard

"cross-env@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-env@npm:7.0.3"
  dependencies:
    cross-spawn: ^7.0.1
  bin:
    cross-env: src/bin/cross-env.js
    cross-env-shell: src/bin/cross-env-shell.js
  checksum: 26f2f3ea2ab32617f57effb70d329c2070d2f5630adc800985d8b30b56e8bf7f5f439dd3a0358b79cee6f930afc23cf8e23515f17ccfb30092c6b62c6b630a79
  languageName: node
  linkType: hard

"cross-spawn@npm:^6.0.5":
  version: 6.0.6
  resolution: "cross-spawn@npm:6.0.6"
  dependencies:
    nice-try: ^1.0.4
    path-key: ^2.0.1
    semver: ^5.5.0
    shebang-command: ^1.2.0
    which: ^1.2.9
  checksum: a6e2e5b04a0e0f806c1df45f92cd079b65f95fbe5a7650ee1ab60318c33a6c156a8a2f8b6898f57764f7363ec599a0625e9855dfa78d52d2d73dbd32eb11c25e
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.1, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 8d306efacaf6f3f60e0224c287664093fa9185680b2d195852ba9a863f85d02dcc737094c6e512175f8ee0161f9b87c73c6826034c2422e39de7d6569cf4503b
  languageName: node
  linkType: hard

"crypto-js@npm:^4.2.0":
  version: 4.2.0
  resolution: "crypto-js@npm:4.2.0"
  checksum: f051666dbc077c8324777f44fbd3aaea2986f198fe85092535130d17026c7c2ccf2d23ee5b29b36f7a4a07312db2fae23c9094b644cc35f7858b1b4fcaf27774
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: f8c4ababffbc5e2ddf2fa9957dda1ee4af6048e22aeda1869d0d00843223c1b13ad3f5d88b51caa46c994225eacb636b764eb807a8883e2fb6f99b4f4e8c48b2
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 8db785cc92d259102725b3c694ec0c823f5619a84741b5c7991b8ad135dfaa66093038a1cc63e03361a6cd28d122be48f2106ae72334e067dd619a51f49eddf7
  languageName: node
  linkType: hard

"d3-array@npm:2 - 3, d3-array@npm:2.10.0 - 3, d3-array@npm:^3.1.6":
  version: 3.2.4
  resolution: "d3-array@npm:3.2.4"
  dependencies:
    internmap: 1 - 2
  checksum: a5976a6d6205f69208478bb44920dd7ce3e788c9dceb86b304dbe401a4bfb42ecc8b04c20facde486e9adcb488b5d1800d49393a3f81a23902b68158e12cddd0
  languageName: node
  linkType: hard

"d3-color@npm:1 - 3":
  version: 3.1.0
  resolution: "d3-color@npm:3.1.0"
  checksum: 4931fbfda5d7c4b5cfa283a13c91a954f86e3b69d75ce588d06cde6c3628cebfc3af2069ccf225e982e8987c612aa7948b3932163ce15eb3c11cd7c003f3ee3b
  languageName: node
  linkType: hard

"d3-ease@npm:^3.0.1":
  version: 3.0.1
  resolution: "d3-ease@npm:3.0.1"
  checksum: 06e2ee5326d1e3545eab4e2c0f84046a123dcd3b612e68858219aa034da1160333d9ce3da20a1d3486d98cb5c2a06f7d233eee1bc19ce42d1533458bd85dedcd
  languageName: node
  linkType: hard

"d3-format@npm:1 - 3":
  version: 3.1.0
  resolution: "d3-format@npm:3.1.0"
  checksum: f345ec3b8ad3cab19bff5dead395bd9f5590628eb97a389b1dd89f0b204c7c4fc1d9520f13231c2c7cf14b7c9a8cf10f8ef15bde2befbab41454a569bd706ca2
  languageName: node
  linkType: hard

"d3-interpolate@npm:1.2.0 - 3, d3-interpolate@npm:^3.0.1":
  version: 3.0.1
  resolution: "d3-interpolate@npm:3.0.1"
  dependencies:
    d3-color: 1 - 3
  checksum: a42ba314e295e95e5365eff0f604834e67e4a3b3c7102458781c477bd67e9b24b6bb9d8e41ff5521050a3f2c7c0c4bbbb6e187fd586daa3980943095b267e78b
  languageName: node
  linkType: hard

"d3-path@npm:^3.1.0":
  version: 3.1.0
  resolution: "d3-path@npm:3.1.0"
  checksum: 2306f1bd9191e1eac895ec13e3064f732a85f243d6e627d242a313f9777756838a2215ea11562f0c7630c7c3b16a19ec1fe0948b1c82f3317fac55882f6ee5d8
  languageName: node
  linkType: hard

"d3-scale@npm:^4.0.2":
  version: 4.0.2
  resolution: "d3-scale@npm:4.0.2"
  dependencies:
    d3-array: 2.10.0 - 3
    d3-format: 1 - 3
    d3-interpolate: 1.2.0 - 3
    d3-time: 2.1.1 - 3
    d3-time-format: 2 - 4
  checksum: a9c770d283162c3bd11477c3d9d485d07f8db2071665f1a4ad23eec3e515e2cefbd369059ec677c9ac849877d1a765494e90e92051d4f21111aa56791c98729e
  languageName: node
  linkType: hard

"d3-shape@npm:^3.1.0":
  version: 3.2.0
  resolution: "d3-shape@npm:3.2.0"
  dependencies:
    d3-path: ^3.1.0
  checksum: de2af5fc9a93036a7b68581ca0bfc4aca2d5a328aa7ba7064c11aedd44d24f310c20c40157cb654359d4c15c3ef369f95ee53d71221017276e34172c7b719cfa
  languageName: node
  linkType: hard

"d3-time-format@npm:2 - 4":
  version: 4.1.0
  resolution: "d3-time-format@npm:4.1.0"
  dependencies:
    d3-time: 1 - 3
  checksum: 7342bce28355378152bbd4db4e275405439cabba082d9cd01946d40581140481c8328456d91740b0fe513c51ec4a467f4471ffa390c7e0e30ea30e9ec98fcdf4
  languageName: node
  linkType: hard

"d3-time@npm:1 - 3, d3-time@npm:2.1.1 - 3, d3-time@npm:^3.0.0":
  version: 3.1.0
  resolution: "d3-time@npm:3.1.0"
  dependencies:
    d3-array: 2 - 3
  checksum: 613b435352a78d9f31b7f68540788186d8c331b63feca60ad21c88e9db1989fe888f97f242322ebd6365e45ec3fb206a4324cd4ca0dfffa1d9b5feb856ba00a7
  languageName: node
  linkType: hard

"d3-timer@npm:^3.0.1":
  version: 3.0.1
  resolution: "d3-timer@npm:3.0.1"
  checksum: 1cfddf86d7bca22f73f2c427f52dfa35c49f50d64e187eb788dcad6e927625c636aa18ae4edd44d084eb9d1f81d8ca4ec305dae7f733c15846a824575b789d73
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-data-view: ^1.0.2
  checksum: 1e1cd509c3037ac0f8ba320da3d1f8bf1a9f09b0be09394b5e40781b8cc15ff9834967ba7c9f843a425b34f9fe14ce44cf055af6662c44263424c1eb8d65659b
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-data-view: ^1.0.2
  checksum: 3600c91ced1cfa935f19ef2abae11029e01738de8d229354d3b2a172bf0d7e4ed08ff8f53294b715569fdf72dfeaa96aa7652f479c0f60570878d88e7e8bddf6
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: 8dd492cd51d19970876626b5b5169fbb67ca31ec1d1d3238ee6a71820ca8b80cafb141c485999db1ee1ef02f2cc3b99424c5eda8d59e852d9ebb79ab290eb5ee
  languageName: node
  linkType: hard

"date-fns@npm:^3.6.0":
  version: 3.6.0
  resolution: "date-fns@npm:3.6.0"
  checksum: 0daa1e9a436cf99f9f2ae9232b55e11f3dd46132bee10987164f3eebd29f245b2e066d7d7db40782627411ecf18551d8f4c9fcdf2226e48bb66545407d448ab7
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.3, debug@npm:^4.3.4, debug@npm:^4.3.5, debug@npm:^4.3.6, debug@npm:^4.4.0":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: a43826a01cda685ee4cec00fb2d3322eaa90ccadbef60d9287debc2a886be3e835d9199c80070ede75a409ee57828c4c6cd80e4b154f2843f0dc95a570dc0729
  languageName: node
  linkType: hard

"decimal.js-light@npm:^2.4.1":
  version: 2.5.1
  resolution: "decimal.js-light@npm:2.5.1"
  checksum: f5a2c7eac1c4541c8ab8a5c8abea64fc1761cefc7794bd5f8afd57a8a78d1b51785e0c4e4f85f4895a043eaa90ddca1edc3981d1263eb6ddce60f32bf5fe66c9
  languageName: node
  linkType: hard

"decompress-response@npm:^6.0.0":
  version: 6.0.0
  resolution: "decompress-response@npm:6.0.0"
  dependencies:
    mimic-response: ^3.1.0
  checksum: d377cf47e02d805e283866c3f50d3d21578b779731e8c5072d6ce8c13cc31493db1c2f6784da9d1d5250822120cefa44f1deab112d5981015f2e17444b763812
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 7be7e5a8d468d6b10e6a67c3de828f55001b6eb515d014f7aeb9066ce36bd5717161eb47d6a0f7bed8a9083935b465bc163ee2581c8b128d29bf61092fdf57a7
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: ^1.0.2
  checksum: 3a88b7a587fc076b84e60affad8b85245c01f60f38fc1d259e7ac1d89eb9ce6abb19e27215de46b98568dd5bc48471730b327637e6f20b0f1bc85cf00440c80a
  languageName: node
  linkType: hard

"defer-to-connect@npm:^2.0.0":
  version: 2.0.1
  resolution: "defer-to-connect@npm:2.0.1"
  checksum: 8a9b50d2f25446c0bfefb55a48e90afd58f85b21bcf78e9207cd7b804354f6409032a1705c2491686e202e64fc05f147aa5aa45f9aa82627563f045937f5791b
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    gopd: ^1.0.1
  checksum: 8068ee6cab694d409ac25936eb861eea704b7763f7f342adbdfe337fc27c78d7ae0eff2364b2917b58c508d723c7a074326d068eef2e45c4edcd85cf94d0313b
  languageName: node
  linkType: hard

"define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: ^1.0.1
    has-property-descriptors: ^1.0.0
    object-keys: ^1.1.1
  checksum: b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: a51744d9b53c164ba9c0492471a1a2ffa0b6727451bdc89e31627fdf4adda9d51277cfcbfb20f0a6f08ccb3c436f341df3e92631a3440226d93a8971724771fd
  languageName: node
  linkType: hard

"depd@npm:2.0.0, depd@npm:^2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: abbe19c768c97ee2eed6282d8ce3031126662252c58d711f646921c9623f9052e3e1906443066beec1095832f534e57c523b7333f8e7e0d93051ab6baef5ab3a
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.0, detect-libc@npm:^2.0.1, detect-libc@npm:^2.0.2, detect-libc@npm:^2.0.4":
  version: 2.0.4
  resolution: "detect-libc@npm:2.0.4"
  checksum: 3d186b7d4e16965e10e21db596c78a4e131f9eee69c0081d13b85e6a61d7448d3ba23fe7997648022bdfa3b0eb4cc3c289a44c8188df949445a20852689abef6
  languageName: node
  linkType: hard

"detect-node-es@npm:^1.1.0":
  version: 1.1.0
  resolution: "detect-node-es@npm:1.1.0"
  checksum: e46307d7264644975b71c104b9f028ed1d3d34b83a15b8a22373640ce5ea630e5640b1078b8ea15f202b54641da71e4aa7597093bd4b91f113db520a26a37449
  languageName: node
  linkType: hard

"detect-node@npm:^2.0.4":
  version: 2.1.0
  resolution: "detect-node@npm:2.1.0"
  checksum: 832184ec458353e41533ac9c622f16c19f7c02d8b10c303dfd3a756f56be93e903616c0bb2d4226183c9351c15fc0b3dba41a17a2308262afabcfa3776e6ae6e
  languageName: node
  linkType: hard

"didyoumean@npm:^1.2.2":
  version: 1.2.2
  resolution: "didyoumean@npm:1.2.2"
  checksum: d5d98719d58b3c2fa59663c4c42ba9716f1fd01245c31d5fce31915bd3aa26e6aac149788e007358f778ebbd68a2256eb5973e8ca6f221df221ba060115acf2e
  languageName: node
  linkType: hard

"dir-compare@npm:^4.2.0":
  version: 4.2.0
  resolution: "dir-compare@npm:4.2.0"
  dependencies:
    minimatch: ^3.0.5
    p-limit: "^3.1.0 "
  checksum: 138ee3c7716f45c1dc100efdf6b9517459428f1cb83fecda1f0dc633326d911a01f6456ff68333f916209649321c70fa004f448f137531664582ecddde4e2601
  languageName: node
  linkType: hard

"dlv@npm:^1.1.3":
  version: 1.1.3
  resolution: "dlv@npm:1.1.3"
  checksum: d7381bca22ed11933a1ccf376db7a94bee2c57aa61e490f680124fa2d1cd27e94eba641d9f45be57caab4f9a6579de0983466f620a2cd6230d7ec93312105ae7
  languageName: node
  linkType: hard

"dmg-builder@npm:26.0.12":
  version: 26.0.12
  resolution: "dmg-builder@npm:26.0.12"
  dependencies:
    app-builder-lib: 26.0.12
    builder-util: 26.0.11
    builder-util-runtime: 9.3.1
    dmg-license: ^1.0.11
    fs-extra: ^10.1.0
    iconv-lite: ^0.6.2
    js-yaml: ^4.1.0
  dependenciesMeta:
    dmg-license:
      optional: true
  checksum: 4a9d9c8d2cc1f154a999c2ad2314e46df298c7592a9af7143d26e1ee84d1a06cee6405eb1ac17d9f5bf9c57d9f72cc44567f87799a71a4f259030a72a3ad4620
  languageName: node
  linkType: hard

"dmg-license@npm:^1.0.11":
  version: 1.0.11
  resolution: "dmg-license@npm:1.0.11"
  dependencies:
    "@types/plist": ^3.0.1
    "@types/verror": ^1.10.3
    ajv: ^6.10.0
    crc: ^3.8.0
    iconv-corefoundation: ^1.1.7
    plist: ^3.0.4
    smart-buffer: ^4.0.2
    verror: ^1.10.0
  bin:
    dmg-license: bin/dmg-license.js
  conditions: os=darwin
  languageName: node
  linkType: hard

"dom-helpers@npm:^5.0.1":
  version: 5.2.1
  resolution: "dom-helpers@npm:5.2.1"
  dependencies:
    "@babel/runtime": ^7.8.7
    csstype: ^3.0.2
  checksum: 863ba9e086f7093df3376b43e74ce4422571d404fc9828bf2c56140963d5edf0e56160f9b2f3bb61b282c07f8fc8134f023c98fd684bddcb12daf7b0f14d951c
  languageName: node
  linkType: hard

"dotenv-expand@npm:^11.0.6":
  version: 11.0.7
  resolution: "dotenv-expand@npm:11.0.7"
  dependencies:
    dotenv: ^16.4.5
  checksum: 58455ad9ffedbf6180b49f8f35596da54f10b02efcaabcba5400363f432e1da057113eee39b42365535da41df1e794d54a4aa67b22b37c41686c3dce4e6a28c5
  languageName: node
  linkType: hard

"dotenv@npm:^16.4.5":
  version: 16.6.1
  resolution: "dotenv@npm:16.6.1"
  checksum: e8bd63c9a37f57934f7938a9cf35de698097fadf980cb6edb61d33b3e424ceccfe4d10f37130b904a973b9038627c2646a3365a904b4406514ea94d7f1816b69
  languageName: node
  linkType: hard

"dotenv@npm:^17.1.0":
  version: 17.2.0
  resolution: "dotenv@npm:17.2.0"
  checksum: 389d25dac7afeb1890bcd113c84d8d8175e95967559e90b8a6c8dc19bfc81ad3ede0036873c459cd8de1f90055a88232ddf74c550bcd2ea7a94b8d570f116470
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    es-errors: ^1.3.0
    gopd: ^1.2.0
  checksum: 149207e36f07bd4941921b0ca929e3a28f1da7bd6b6ff8ff7f4e2f2e460675af4576eeba359c635723dc189b64cdd4787e0255897d5b135ccc5d15cb8685fc90
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"ejs@npm:^3.1.8":
  version: 3.1.10
  resolution: "ejs@npm:3.1.10"
  dependencies:
    jake: ^10.8.5
  bin:
    ejs: bin/cli.js
  checksum: ce90637e9c7538663ae023b8a7a380b2ef7cc4096de70be85abf5a3b9641912dde65353211d05e24d56b1f242d71185c6d00e02cb8860701d571786d92c71f05
  languageName: node
  linkType: hard

"electron-builder@npm:latest":
  version: 26.0.12
  resolution: "electron-builder@npm:26.0.12"
  dependencies:
    app-builder-lib: 26.0.12
    builder-util: 26.0.11
    builder-util-runtime: 9.3.1
    chalk: ^4.1.2
    dmg-builder: 26.0.12
    fs-extra: ^10.1.0
    is-ci: ^3.0.0
    lazy-val: ^1.0.5
    simple-update-notifier: 2.0.0
    yargs: ^17.6.2
  bin:
    electron-builder: cli.js
    install-app-deps: install-app-deps.js
  checksum: 9376d0591d9d881be7ccbedaa6a0584744c2293ef7a2411a4c3709a310454a728377dc464298033166895ffc6d11ebb60e272ace91b60400ec067ea30930e1ec
  languageName: node
  linkType: hard

"electron-log@npm:^5.4.1":
  version: 5.4.1
  resolution: "electron-log@npm:5.4.1"
  checksum: 1229461d82ea7320e14daa44e77413b47e35510e8bcc97d09df0d4f09d9b7e4fdae0b5dc5249102b6b54f859a3e93e3b3cdc6d2d7cb1b5fd28126f8a89a12049
  languageName: node
  linkType: hard

"electron-publish@npm:26.0.11":
  version: 26.0.11
  resolution: "electron-publish@npm:26.0.11"
  dependencies:
    "@types/fs-extra": ^9.0.11
    builder-util: 26.0.11
    builder-util-runtime: 9.3.1
    chalk: ^4.1.2
    form-data: ^4.0.0
    fs-extra: ^10.1.0
    lazy-val: ^1.0.5
    mime: ^2.5.2
  checksum: 626ed5682d09ccad3474be58fb4d98079eeda30dafaa28c7c5f291d0e3d784e00f3a14988134193f622cca14ebc6057705b5cd7ab021944e039b5308e607e6fd
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.173":
  version: 1.5.181
  resolution: "electron-to-chromium@npm:1.5.181"
  checksum: 4606f8a873bce8b8191c9d83b91567ad25711d11496dca0cbe2bdd91d653315a462028e4461107420084c0092d0094fabab37450eb42fb66bfaf97cb50115c5a
  languageName: node
  linkType: hard

"electron@npm:^36.1.0":
  version: 36.7.0
  resolution: "electron@npm:36.7.0"
  dependencies:
    "@electron/get": ^2.0.0
    "@types/node": ^22.7.7
    extract-zip: ^2.0.1
  bin:
    electron: cli.js
  checksum: ef101db30c66c206e2d69c8745b7c8fe3a48be7c04fe0d9f1997bfce28cc3aa1045703be9599af9ec16e43fe13b7fcc52d22514adf24e4663631f53071fd8b0d
  languageName: node
  linkType: hard

"embla-carousel-react@npm:^8.3.0":
  version: 8.6.0
  resolution: "embla-carousel-react@npm:8.6.0"
  dependencies:
    embla-carousel: 8.6.0
    embla-carousel-reactive-utils: 8.6.0
  peerDependencies:
    react: ^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  checksum: 30622c3f6bc3de5b608ab3fd82f69ec2ad86e86657c4c9d780380cf159bf5dac0170adced74aecd117c521c06fa014fe71b5fa0ec7c293921fd1cb7dff460be3
  languageName: node
  linkType: hard

"embla-carousel-reactive-utils@npm:8.6.0":
  version: 8.6.0
  resolution: "embla-carousel-reactive-utils@npm:8.6.0"
  peerDependencies:
    embla-carousel: 8.6.0
  checksum: d3663addcb10a5cfecac976987f27fd58f2142ef944da9cd56568120752a71cb04d731e9c396f2e426f43be730f02145a87b88747c6902e24544a6fca9fc0141
  languageName: node
  linkType: hard

"embla-carousel@npm:8.6.0":
  version: 8.6.0
  resolution: "embla-carousel@npm:8.6.0"
  checksum: d943d225d069a8cf0b876440297dda42107e5e69006387c6bce1163b027ac5e5beba67160db818a763c7c46f2103238a097953ac3f383362f8104535cd278de1
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.0.0, emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"encodeurl@npm:^2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: abf5cd51b78082cf8af7be6785813c33b6df2068ce5191a40ca8b1afe6a86f9230af9a9ce694a5ce4665955e5c1120871826df9c128a642e09c58d592e2807fe
  languageName: node
  linkType: hard

"encoding@npm:^0.1.12, encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0, end-of-stream@npm:^1.4.1":
  version: 1.4.5
  resolution: "end-of-stream@npm:1.4.5"
  dependencies:
    once: ^1.4.0
  checksum: 1e0cfa6e7f49887544e03314f9dfc56a8cb6dde910cbb445983ecc2ff426fc05946df9d75d8a21a3a64f2cecfe1bf88f773952029f46756b2ed64a24e95b1fb8
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.9":
  version: 1.24.0
  resolution: "es-abstract@npm:1.24.0"
  dependencies:
    array-buffer-byte-length: ^1.0.2
    arraybuffer.prototype.slice: ^1.0.4
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    data-view-buffer: ^1.0.2
    data-view-byte-length: ^1.0.2
    data-view-byte-offset: ^1.0.1
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.1.1
    es-set-tostringtag: ^2.1.0
    es-to-primitive: ^1.3.0
    function.prototype.name: ^1.1.8
    get-intrinsic: ^1.3.0
    get-proto: ^1.0.1
    get-symbol-description: ^1.1.0
    globalthis: ^1.0.4
    gopd: ^1.2.0
    has-property-descriptors: ^1.0.2
    has-proto: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    internal-slot: ^1.1.0
    is-array-buffer: ^3.0.5
    is-callable: ^1.2.7
    is-data-view: ^1.0.2
    is-negative-zero: ^2.0.3
    is-regex: ^1.2.1
    is-set: ^2.0.3
    is-shared-array-buffer: ^1.0.4
    is-string: ^1.1.1
    is-typed-array: ^1.1.15
    is-weakref: ^1.1.1
    math-intrinsics: ^1.1.0
    object-inspect: ^1.13.4
    object-keys: ^1.1.1
    object.assign: ^4.1.7
    own-keys: ^1.0.1
    regexp.prototype.flags: ^1.5.4
    safe-array-concat: ^1.1.3
    safe-push-apply: ^1.0.0
    safe-regex-test: ^1.1.0
    set-proto: ^1.0.0
    stop-iteration-iterator: ^1.1.0
    string.prototype.trim: ^1.2.10
    string.prototype.trimend: ^1.0.9
    string.prototype.trimstart: ^1.0.8
    typed-array-buffer: ^1.0.3
    typed-array-byte-length: ^1.0.3
    typed-array-byte-offset: ^1.0.4
    typed-array-length: ^1.0.7
    unbox-primitive: ^1.1.0
    which-typed-array: ^1.1.19
  checksum: 06b3d605e56e3da9d16d4db2629a42dac1ca31f2961a41d15c860422a266115e865b43e82d6b9da81a0fabbbb65ebc12fb68b0b755bc9dbddacb6bf7450e96df
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 0512f4e5d564021c9e3a644437b0155af2679d10d80f21adaf868e64d30efdfbd321631956f20f42d655fedb2e3a027da479fad3fa6048f768eb453a80a5f80a
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: ec1414527a0ccacd7f15f4a3bc66e215f04f595ba23ca75cdae0927af099b5ec865f9f4d33e9d7e86f512f252876ac77d4281a7871531a50678132429b1271b5
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: ^1.3.0
  checksum: 214d3767287b12f36d3d7267ef342bbbe1e89f899cfd67040309fc65032372a8e60201410a99a1645f2f90c1912c8c49c8668066f6bdd954bcd614dda2e3da97
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: 789f35de4be3dc8d11fdcb91bc26af4ae3e6d602caa93299a8c45cf05d36cc5081454ae2a6d3afa09cceca214b76c046e4f8151e092e6fc7feeb5efb9e794fc6
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: ^1.2.7
    is-date-object: ^1.0.5
    is-symbol: ^1.0.4
  checksum: 966965880356486cd4d1fe9a523deda2084c81b3702d951212c098f5f2ee93605d1b7c1840062efb48a07d892641c7ed1bc194db563645c0dd2b919cb6d65b93
  languageName: node
  linkType: hard

"es6-error@npm:^4.1.1":
  version: 4.1.1
  resolution: "es6-error@npm:4.1.1"
  checksum: ae41332a51ec1323da6bbc5d75b7803ccdeddfae17c41b6166ebbafc8e8beb7a7b80b884b7fab1cc80df485860ac3c59d78605e860bb4f8cd816b3d6ade0d010
  languageName: node
  linkType: hard

"esbuild@npm:^0.25.0":
  version: 0.25.6
  resolution: "esbuild@npm:0.25.6"
  dependencies:
    "@esbuild/aix-ppc64": 0.25.6
    "@esbuild/android-arm": 0.25.6
    "@esbuild/android-arm64": 0.25.6
    "@esbuild/android-x64": 0.25.6
    "@esbuild/darwin-arm64": 0.25.6
    "@esbuild/darwin-x64": 0.25.6
    "@esbuild/freebsd-arm64": 0.25.6
    "@esbuild/freebsd-x64": 0.25.6
    "@esbuild/linux-arm": 0.25.6
    "@esbuild/linux-arm64": 0.25.6
    "@esbuild/linux-ia32": 0.25.6
    "@esbuild/linux-loong64": 0.25.6
    "@esbuild/linux-mips64el": 0.25.6
    "@esbuild/linux-ppc64": 0.25.6
    "@esbuild/linux-riscv64": 0.25.6
    "@esbuild/linux-s390x": 0.25.6
    "@esbuild/linux-x64": 0.25.6
    "@esbuild/netbsd-arm64": 0.25.6
    "@esbuild/netbsd-x64": 0.25.6
    "@esbuild/openbsd-arm64": 0.25.6
    "@esbuild/openbsd-x64": 0.25.6
    "@esbuild/openharmony-arm64": 0.25.6
    "@esbuild/sunos-x64": 0.25.6
    "@esbuild/win32-arm64": 0.25.6
    "@esbuild/win32-ia32": 0.25.6
    "@esbuild/win32-x64": 0.25.6
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/openharmony-arm64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: b6da1f32fc3300a4844562c9f5fae8b1421c994d6f8b8c64e73388b7e86d2788ecfc0ff16da5ffe9ff54f134aa2b0d454e644f558cfca510f9647058860148f4
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 47b029c83de01b0d17ad99ed766347b974b0d628e848de404018f3abee728e987da0d2d370ad4574aa3d5b5bfc368754fd085d69a30f8e75903486ec4b5b709e
  languageName: node
  linkType: hard

"escape-html@npm:^1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^5.2.0":
  version: 5.2.0
  resolution: "eslint-plugin-react-hooks@npm:5.2.0"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0
  checksum: 5920736a78c0075488e7e30e04fbe5dba5b6b5a6c8c4b5742fdae6f9b8adf4ee387bc45dc6e03b4012865e6fd39d134da7b83a40f57c90cc9eecf80692824e3a
  languageName: node
  linkType: hard

"eslint-plugin-react-refresh@npm:^0.4.19":
  version: 0.4.20
  resolution: "eslint-plugin-react-refresh@npm:0.4.20"
  peerDependencies:
    eslint: ">=8.40"
  checksum: b0e946be32f10f0c8239e7ec958b1e095fc633da3f2e8f6bd5bd09188f348bee1bf17d0e175d47e02a8e22fcad24f8246a9f1f065a84968f819e4102f0953256
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.4.0":
  version: 8.4.0
  resolution: "eslint-scope@npm:8.4.0"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: cf88f42cd5e81490d549dc6d350fe01e6fe420f9d9ea34f134bb359b030e3c4ef888d36667632e448937fe52449f7181501df48c08200e3d3b0fee250d05364e
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 36e9ef87fca698b6fd7ca5ca35d7b2b6eeaaf106572e2f7fd31c12d3bfdaccdb587bba6d3621067e5aece31c8c3a348b93922ab8f7b2cbc6aaab5e1d89040c60
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.1":
  version: 4.2.1
  resolution: "eslint-visitor-keys@npm:4.2.1"
  checksum: 3a77e3f99a49109f6fb2c5b7784bc78f9743b834d238cdba4d66c602c6b52f19ed7bcd0a5c5dbbeae3a8689fd785e76c001799f53d2228b278282cf9f699fff5
  languageName: node
  linkType: hard

"eslint@npm:^9.25.0":
  version: 9.30.1
  resolution: "eslint@npm:9.30.1"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.12.1
    "@eslint/config-array": ^0.21.0
    "@eslint/config-helpers": ^0.3.0
    "@eslint/core": ^0.14.0
    "@eslint/eslintrc": ^3.3.1
    "@eslint/js": 9.30.1
    "@eslint/plugin-kit": ^0.3.1
    "@humanfs/node": ^0.16.6
    "@humanwhocodes/module-importer": ^1.0.1
    "@humanwhocodes/retry": ^0.4.2
    "@types/estree": ^1.0.6
    "@types/json-schema": ^7.0.15
    ajv: ^6.12.4
    chalk: ^4.0.0
    cross-spawn: ^7.0.6
    debug: ^4.3.2
    escape-string-regexp: ^4.0.0
    eslint-scope: ^8.4.0
    eslint-visitor-keys: ^4.2.1
    espree: ^10.4.0
    esquery: ^1.5.0
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^8.0.0
    find-up: ^5.0.0
    glob-parent: ^6.0.2
    ignore: ^5.2.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    json-stable-stringify-without-jsonify: ^1.0.1
    lodash.merge: ^4.6.2
    minimatch: ^3.1.2
    natural-compare: ^1.4.0
    optionator: ^0.9.3
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: e6723b98ba19ff17cf0cacb29c3c0ea5c7c6b6fb648136b2d009e7e2da4980a2562c9523623b0faf449750e890f3b274b20bee11fa9c8f43362d235485ba2f91
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.4.0":
  version: 10.4.0
  resolution: "espree@npm:10.4.0"
  dependencies:
    acorn: ^8.15.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^4.2.1
  checksum: 5f9d0d7c81c1bca4bfd29a55270067ff9d575adb8c729a5d7f779c2c7b910bfc68ccf8ec19b29844b707440fc159a83868f22c8e87bbf7cbcb225ed067df6c85
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: 08ec4fe446d9ab27186da274d979558557fbdbbd10968fa9758552482720c54152a5640e08b9009e5a30706b66aba510692054d4129d32d0e12e05bbc0b96fb2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"estree-walker@npm:^3.0.3":
  version: 3.0.3
  resolution: "estree-walker@npm:3.0.3"
  dependencies:
    "@types/estree": ^1.0.0
  checksum: a65728d5727b71de172c5df323385755a16c0fdab8234dc756c3854cfee343261ddfbb72a809a5660fac8c75d960bb3e21aa898c2d7e9b19bb298482ca58a3af
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"etag@npm:^1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 571aeb3dbe0f2bbd4e4fadbdb44f325fc75335cd5f6f6b6a091e6a06a9f25ed5392f0863c5442acb0646787446e816f13cbfc6edce5b07658541dff573cab1ff
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.1":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 1875311c42fcfe9c707b2712c32664a245629b42bb0a5a84439762dd0fd637fc54d078155ea83c2af9e0323c9ac13687e03cfba79b03af9f40c89b4960099374
  languageName: node
  linkType: hard

"expand-template@npm:^2.0.3":
  version: 2.0.3
  resolution: "expand-template@npm:2.0.3"
  checksum: 588c19847216421ed92befb521767b7018dc88f88b0576df98cb242f20961425e96a92cbece525ef28cc5becceae5d544ae0f5b9b5e2aa05acb13716ca5b3099
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 7e191e3dd6edd8c56c88f2c8037c98fbb8034fe48778be53ed8cb30ccef371a061a4e999a469aab939b92f8f12698f3b426d52f4f76b7a20da5f9f98c3cbc862
  languageName: node
  linkType: hard

"express@npm:^5.1.0":
  version: 5.1.0
  resolution: "express@npm:5.1.0"
  dependencies:
    accepts: ^2.0.0
    body-parser: ^2.2.0
    content-disposition: ^1.0.0
    content-type: ^1.0.5
    cookie: ^0.7.1
    cookie-signature: ^1.2.1
    debug: ^4.4.0
    encodeurl: ^2.0.0
    escape-html: ^1.0.3
    etag: ^1.8.1
    finalhandler: ^2.1.0
    fresh: ^2.0.0
    http-errors: ^2.0.0
    merge-descriptors: ^2.0.0
    mime-types: ^3.0.0
    on-finished: ^2.4.1
    once: ^1.4.0
    parseurl: ^1.3.3
    proxy-addr: ^2.0.7
    qs: ^6.14.0
    range-parser: ^1.2.1
    router: ^2.2.0
    send: ^1.1.0
    serve-static: ^2.2.0
    statuses: ^2.0.1
    type-is: ^2.0.1
    vary: ^1.1.2
  checksum: 06e6141780c6c4780111f971ce062c83d4cf4862c40b43caf1d95afcbb58d7422c560503b8c9d04c7271511525d09cbdbe940bcaad63970fd4c1b9f6fd713bdb
  languageName: node
  linkType: hard

"extract-zip@npm:^2.0.1":
  version: 2.0.1
  resolution: "extract-zip@npm:2.0.1"
  dependencies:
    "@types/yauzl": ^2.9.1
    debug: ^4.1.1
    get-stream: ^5.1.0
    yauzl: ^2.10.0
  dependenciesMeta:
    "@types/yauzl":
      optional: true
  bin:
    extract-zip: cli.js
  checksum: 8cbda9debdd6d6980819cc69734d874ddd71051c9fe5bde1ef307ebcedfe949ba57b004894b585f758b7c9eeeea0e3d87f2dda89b7d25320459c2c9643ebb635
  languageName: node
  linkType: hard

"extsprintf@npm:^1.2.0":
  version: 1.4.1
  resolution: "extsprintf@npm:1.4.1"
  checksum: a2f29b241914a8d2bad64363de684821b6b1609d06ae68d5b539e4de6b28659715b5bea94a7265201603713b7027d35399d10b0548f09071c5513e65e8323d33
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-equals@npm:^5.0.1":
  version: 5.2.2
  resolution: "fast-equals@npm:5.2.2"
  checksum: 7156bcade0be5ee4dc335969d255a5815348d57080e1876fa1584451eafd0c92588de5f5840e55f81841b6d907ade2a49a46e4ec33e6f7a283a209c0fd8f8a59
  languageName: node
  linkType: hard

"fast-fifo@npm:^1.2.0, fast-fifo@npm:^1.3.2":
  version: 1.3.2
  resolution: "fast-fifo@npm:1.3.2"
  checksum: 6bfcba3e4df5af7be3332703b69a7898a8ed7020837ec4395bb341bd96cc3a6d86c3f6071dd98da289618cf2234c70d84b2a6f09a33dd6f988b1ff60d8e54275
  languageName: node
  linkType: hard

"fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.8
  checksum: 0704d7b85c0305fd2cef37777337dfa26230fdd072dce9fb5c82a4b03156f3ffb8ed3e636033e65d45d2a5805a4e475825369a27404c0307f2db0c8eb3366fbd
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fast-xml-parser@npm:5.2.5":
  version: 5.2.5
  resolution: "fast-xml-parser@npm:5.2.5"
  dependencies:
    strnum: ^2.1.0
  bin:
    fxparser: src/cli/cli.js
  checksum: b12daa933bc226bd7df1e1ecbd305e561c83fd6e4a234b5e2728901deca25a9b9522b9d3ebafde41b1f4d87ab814e3efe18c636638580795fdbe4670a556be88
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: ^1.0.4
  checksum: 7691d1794fb84ad0ec2a185f10e00f0e1713b894e2c9c4d42f0bc0ba5f8c00e6e655a202074ca0b91b9c3d977aab7c30c41a8dc069fb5368576ac0054870a0e6
  languageName: node
  linkType: hard

"fd-slicer@npm:~1.1.0":
  version: 1.1.0
  resolution: "fd-slicer@npm:1.1.0"
  dependencies:
    pend: ~1.2.0
  checksum: c8585fd5713f4476eb8261150900d2cb7f6ff2d87f8feb306ccc8a1122efd152f1783bdb2b8dc891395744583436bfd8081d8e63ece0ec8687eeefea394d4ff2
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.6
  resolution: "fdir@npm:6.4.6"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: fe9f3014901d023cf631831dcb9eae5447f4d7f69218001dd01ecf007eccc40f6c129a04411b5cc273a5f93c14e02e971e17270afc9022041c80be924091eb6f
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: ^4.0.0
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-uri-to-path@npm:1.0.0":
  version: 1.0.0
  resolution: "file-uri-to-path@npm:1.0.0"
  checksum: b648580bdd893a008c92c7ecc96c3ee57a5e7b6c4c18a9a09b44fb5d36d79146f8e442578bc0e173dc027adf3987e254ba1dfd6e3ec998b7c282873010502144
  languageName: node
  linkType: hard

"filelist@npm:^1.0.4":
  version: 1.0.4
  resolution: "filelist@npm:1.0.4"
  dependencies:
    minimatch: ^5.0.1
  checksum: a303573b0821e17f2d5e9783688ab6fbfce5d52aaac842790ae85e704a6f5e4e3538660a63183d6453834dedf1e0f19a9dadcebfa3e926c72397694ea11f5160
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"finalhandler@npm:^2.1.0":
  version: 2.1.0
  resolution: "finalhandler@npm:2.1.0"
  dependencies:
    debug: ^4.4.0
    encodeurl: ^2.0.0
    escape-html: ^1.0.3
    on-finished: ^2.4.1
    parseurl: ^1.3.3
    statuses: ^2.0.1
  checksum: 27ca9cc83b1384ba37959eb95bc7e62bc0bf4d6f6af63f6d38821cf7499b113e34b23f96a2a031616817f73986f94deea67c2f558de9daf406790c181a2501df
  languageName: node
  linkType: hard

"find-replace@npm:^3.0.0":
  version: 3.0.0
  resolution: "find-replace@npm:3.0.0"
  dependencies:
    array-back: ^3.0.1
  checksum: 6b04bcfd79027f5b84aa1dfe100e3295da989bdac4b4de6b277f4d063e78f5c9e92ebc8a1fec6dd3b448c924ba404ee051cc759e14a3ee3e825fa1361025df08
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: ^3.2.9
    keyv: ^4.5.4
  checksum: 899fc86bf6df093547d76e7bfaeb900824b869d7d457d02e9b8aae24836f0a99fbad79328cfd6415ee8908f180699bf259dc7614f793447cb14f707caf5996f6
  languageName: node
  linkType: hard

"flatbuffers@npm:^1.12.0":
  version: 1.12.0
  resolution: "flatbuffers@npm:1.12.0"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"flatbuffers@npm:^24.3.25":
  version: 24.12.23
  resolution: "flatbuffers@npm:24.12.23"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: ^1.2.7
  checksum: 3c986d7e11f4381237cc98baa0a2f87eabe74719eee65ed7bed275163082b940ede19268c61d04c6260e0215983b12f8d885e3c8f9aa8c2113bf07c37051745c
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0, foreground-child@npm:^3.3.1":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: ^7.0.6
    signal-exit: ^4.0.1
  checksum: b2c1a6fc0bf0233d645d9fefdfa999abf37db1b33e5dab172b3cbfb0662b88bfbd2c9e7ab853533d199050ec6b65c03fcf078fc212d26e4990220e98c6930eef
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.3
  resolution: "form-data@npm:4.0.3"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.8
    es-set-tostringtag: ^2.1.0
    hasown: ^2.0.2
    mime-types: ^2.1.12
  checksum: b8e2568c0853ce167b2b9c9c4b81fe563f9ade647178baf6b6381cf8a11e3c01dd2b78a63ba367e6f5eab59afab8284a9438bb5ae768133f9d9fce6567fbc26a
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: fd27e2394d8887ebd16a66ffc889dc983fbbd797d5d3f01087c020283c0f019a7d05ee85669383d8e0d216b116d720fc0cef2f6e9b7eb9f4c90c6e0bc7fd28e6
  languageName: node
  linkType: hard

"fraction.js@npm:^4.3.7":
  version: 4.3.7
  resolution: "fraction.js@npm:4.3.7"
  checksum: e1553ae3f08e3ba0e8c06e43a3ab20b319966dfb7ddb96fd9b5d0ee11a66571af7f993229c88ebbb0d4a816eb813a24ed48207b140d442a8f76f33763b8d1f3f
  languageName: node
  linkType: hard

"fresh@npm:^2.0.0":
  version: 2.0.0
  resolution: "fresh@npm:2.0.0"
  checksum: 38b9828352c6271e2a0dd8bdd985d0100dbbc4eb8b6a03286071dd6f7d96cfaacd06d7735701ad9a95870eb3f4555e67c08db1dcfe24c2e7bb87383c72fae1d2
  languageName: node
  linkType: hard

"fs-constants@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs-constants@npm:1.0.0"
  checksum: 18f5b718371816155849475ac36c7d0b24d39a11d91348cfcb308b4494824413e03572c403c86d3a260e049465518c4f0d5bd00f0371cdfcad6d4f30a85b350d
  languageName: node
  linkType: hard

"fs-extra@npm:^10.0.0, fs-extra@npm:^10.1.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: dc94ab37096f813cc3ca12f0f1b5ad6744dfed9ed21e953d72530d103cea193c2f81584a39e9dee1bea36de5ee66805678c0dddc048e8af1427ac19c00fffc50
  languageName: node
  linkType: hard

"fs-extra@npm:^11.1.1, fs-extra@npm:^11.3.0":
  version: 11.3.0
  resolution: "fs-extra@npm:11.3.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: f983c706e0c22b0c0747a8e9c76aed6f391ba2d76734cf2757cd84da13417b402ed68fe25bace65228856c61d36d3b41da198f1ffbf33d0b34283a2f7a62c6e9
  languageName: node
  linkType: hard

"fs-extra@npm:^8.1.0":
  version: 8.1.0
  resolution: "fs-extra@npm:8.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^4.0.0
    universalify: ^0.1.0
  checksum: bf44f0e6cea59d5ce071bba4c43ca76d216f89e402dc6285c128abc0902e9b8525135aa808adad72c9d5d218e9f4bcc63962815529ff2f684ad532172a284880
  languageName: node
  linkType: hard

"fs-extra@npm:^9.0.0, fs-extra@npm:^9.0.1":
  version: 9.1.0
  resolution: "fs-extra@npm:9.1.0"
  dependencies:
    at-least-node: ^1.0.0
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: ba71ba32e0faa74ab931b7a0031d1523c66a73e225de7426e275e238e312d07313d2da2d33e34a52aa406c8763ade5712eb3ec9ba4d9edce652bcacdc29e6b20
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0, fs-minipass@npm:^2.1.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: ^3.0.0
  checksum: 1b8d128dae2ac6cc94230cc5ead341ba3e0efaef82dab46a33d171c044caaa6ca001364178d42069b2809c35a1c3c35079a32107c770e9ffab3901b59af8c8b1
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: latest
  checksum: 11e6ea6fea15e42461fc55b4b0e4a0a3c654faa567f1877dbd353f39156f69def97a69936d1746619d656c4b93de2238bf731f6085a03a50cabf287c9d024317
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>, fsevents@patch:fsevents@~2.3.3#~builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#~builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    functions-have-names: ^1.2.3
    hasown: ^2.0.2
    is-callable: ^1.2.7
  checksum: 3a366535dc08b25f40a322efefa83b2da3cd0f6da41db7775f2339679120ef63b6c7e967266182609e655b8f0a8f65596ed21c7fd72ad8bd5621c2340edd4010
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: c3f1f5ba20f4e962efb71344ce0a40722163e85bee2101ce25f88214e78182d2d2476aa85ef37950c579eb6cf6ee811c17b3101bb84004bb75655f3e33f3fdb5
  languageName: node
  linkType: hard

"gauge@npm:^4.0.3":
  version: 4.0.4
  resolution: "gauge@npm:4.0.4"
  dependencies:
    aproba: ^1.0.3 || ^2.0.0
    color-support: ^1.1.3
    console-control-strings: ^1.1.0
    has-unicode: ^2.0.1
    signal-exit: ^3.0.7
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    wide-align: ^1.1.5
  checksum: 788b6bfe52f1dd8e263cda800c26ac0ca2ff6de0b6eee2fe0d9e3abf15e149b651bd27bf5226be10e6e3edb5c4e5d5985a5a1a98137e7a892f75eff76467ad2d
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: a7437e58c6be12aa6c90f7730eac7fa9833dc78872b4ad2963d2031b00a3367a93f98aec75f9aaac7220848e4026d67a8655e870b24f20a543d103c0d65952ec
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: ^1.0.2
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.1.1
    function-bind: ^1.1.2
    get-proto: ^1.0.1
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    math-intrinsics: ^1.1.0
  checksum: 301008e4482bb9a9cb49e132b88fee093bff373b4e6def8ba219b1e96b60158a6084f273ef5cafe832e42cd93462f4accb46a618d35fe59a2b507f2388c5b79d
  languageName: node
  linkType: hard

"get-nonce@npm:^1.0.0":
  version: 1.0.1
  resolution: "get-nonce@npm:1.0.1"
  checksum: e2614e43b4694c78277bb61b0f04583d45786881289285c73770b07ded246a98be7e1f78b940c80cbe6f2b07f55f0b724e6db6fd6f1bcbd1e8bdac16521074ed
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: ^1.0.1
    es-object-atoms: ^1.0.0
  checksum: 4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"get-stream@npm:^5.1.0":
  version: 5.2.0
  resolution: "get-stream@npm:5.2.0"
  dependencies:
    pump: ^3.0.0
  checksum: 8bc1a23174a06b2b4ce600df38d6c98d2ef6d84e020c1ddad632ad75bac4e092eeb40e4c09e0761c35fc2dbc5e7fff5dab5e763a383582c4a167dd69a905bd12
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
  checksum: 655ed04db48ee65ef2ddbe096540d4405e79ba0a7f54225775fef43a7e2afcb93a77d141c5f05fdef0afce2eb93bcbfb3597142189d562ac167ff183582683cd
  languageName: node
  linkType: hard

"github-from-package@npm:0.0.0":
  version: 0.0.0
  resolution: "github-from-package@npm:0.0.0"
  checksum: 14e448192a35c1e42efee94c9d01a10f42fe790375891a24b25261246ce9336ab9df5d274585aedd4568f7922246c2a78b8a8cd2571bfe99c693a9718e7dd0e3
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.12":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^3.1.2
    minimatch: ^9.0.4
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^1.11.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 0bc725de5e4862f9f387fd0f2b274baf16850dcd2714502ccf471ee401803997983e2c05590cb65f9675a3c6f2a58e7a53f9e365704108c6ad3cbf1d60934c4a
  languageName: node
  linkType: hard

"glob@npm:^11.0.1":
  version: 11.0.3
  resolution: "glob@npm:11.0.3"
  dependencies:
    foreground-child: ^3.3.1
    jackspeak: ^4.1.1
    minimatch: ^10.0.3
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^2.0.0
  bin:
    glob: dist/esm/bin.mjs
  checksum: 65ddc1e3c969e87999880580048763cc8b5bdd375930dd43b8100a5ba481d2e2563e4553de42875790800c602522a98aa8d3ed1c5bd4d27621609e6471eb371d
  languageName: node
  linkType: hard

"glob@npm:^7.1.3, glob@npm:^7.1.4, glob@npm:^7.1.6":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.1.1
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 29452e97b38fa704dabb1d1045350fb2467cf0277e155aa9ff7077e90ad81d1ea9d53d3ee63bd37c05b09a065e90f16aec4a65f5b8de401d1dac40bc5605d133
  languageName: node
  linkType: hard

"glob@npm:^8.0.1, glob@npm:^8.1.0":
  version: 8.1.0
  resolution: "glob@npm:8.1.0"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^5.0.1
    once: ^1.3.0
  checksum: 92fbea3221a7d12075f26f0227abac435de868dd0736a17170663783296d0dd8d3d532a5672b4488a439bf5d7fb85cdd07c11185d6cd39184f0385cbdfb86a47
  languageName: node
  linkType: hard

"global-agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "global-agent@npm:3.0.0"
  dependencies:
    boolean: ^3.0.1
    es6-error: ^4.1.1
    matcher: ^3.0.0
    roarr: ^2.15.3
    semver: ^7.3.2
    serialize-error: ^7.0.1
  checksum: 75074d80733b4bd5386c47f5df028e798018025beac0ab310e9908c72bf5639e408203e7bca0130d5ee01b5f4abc6d34385d96a9f950ea5fe1979bb431c808f7
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 534b8216736a5425737f59f6e6a5c7f386254560c9f41d24a9227d60ee3ad4a9e82c5b85def0e212e9d92162f83a92544be4c7fd4c902cb913736c10e08237ac
  languageName: node
  linkType: hard

"globals@npm:^16.0.0":
  version: 16.3.0
  resolution: "globals@npm:16.3.0"
  checksum: 2f3467f27bd84dca7778b0f7b528718b697274f3ed0d12721b9af0a14a9b6eb20240cb221817c264a27bfc5b9fac3ae28f6168b39808f27c74142942fb953c73
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.1, globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: ^1.2.1
    gopd: ^1.0.1
  checksum: 39ad667ad9f01476474633a1834a70842041f70a55571e8dcef5fb957980a92da5022db5430fca8aecc5d47704ae30618c0bc877a579c70710c904e9ef06108a
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: cc6d8e655e360955bdccaca51a12a474268f95bb793fc3e1f2bdadb075f28bfd1fd988dab872daf77a61d78cbaf13744bc8727a17cfb1d150d76047d805375f3
  languageName: node
  linkType: hard

"got@npm:^11.7.0, got@npm:^11.8.5":
  version: 11.8.6
  resolution: "got@npm:11.8.6"
  dependencies:
    "@sindresorhus/is": ^4.0.0
    "@szmarczak/http-timer": ^4.0.5
    "@types/cacheable-request": ^6.0.1
    "@types/responselike": ^1.0.0
    cacheable-lookup: ^5.0.3
    cacheable-request: ^7.0.2
    decompress-response: ^6.0.0
    http2-wrapper: ^1.0.0-beta.5.2
    lowercase-keys: ^2.0.0
    p-cancelable: ^2.0.0
    responselike: ^2.0.0
  checksum: bbc783578a8d5030c8164ef7f57ce41b5ad7db2ed13371e1944bef157eeca5a7475530e07c0aaa71610d7085474d0d96222c9f4268d41db333a17e39b463f45d
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.11, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: bab8f0be9b568857c7bec9fda95a89f87b783546d02951c40c33f84d05bb7da3fd10f863a9beb901463669b6583173a8c8cc6d6b306ea2b9b9d5d3d943c3a673
  languageName: node
  linkType: hard

"guid-typescript@npm:^1.0.9":
  version: 1.0.9
  resolution: "guid-typescript@npm:1.0.9"
  checksum: 829dd87866800a5138aafa0873994028bbc446eb20ff4cae6452d471a2a3d26f7025bed3eb980692c0f022fd22f95ea7396122b46b45a4b5084958505a4fc50c
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 79730518ae02c77e4af6a1d1a0b6a2c3e1509785532771f9baf0241e83e36329542c3d7a0e723df8cbc85f74eff4f177828a2265a01ba576adbdc2d40d86538b
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: ^1.0.0
  checksum: fcbb246ea2838058be39887935231c6d5788babed499d0e9d0cc5737494c48aba4fe17ba1449e0d0fbbb1e36175442faa37f9c427ae357d6ccb1d895fbcd3de3
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: ^1.0.0
  checksum: f55010cb94caa56308041d77967c72a02ffd71386b23f9afa8447e58bc92d49d15c19bf75173713468e92fe3fb1680b03b115da39c21c32c74886d1d50d3e7ff
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: b2316c7302a0e8ba3aaba215f834e96c22c86f192e7310bdf689dd0e6999510c89b00fbc5742571507cebf25764d68c988b3a0da217369a73596191ac0ce694b
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: ^1.0.3
  checksum: 999d60bb753ad714356b2c6c87b7fb74f32463b8426e159397da4bde5bca7e598ab1073f4d8d4deafac297f2eb311484cd177af242776bf05f0d11565680468d
  languageName: node
  linkType: hard

"has-unicode@npm:^2.0.1":
  version: 2.0.1
  resolution: "has-unicode@npm:2.0.1"
  checksum: 1eab07a7436512db0be40a710b29b5dc21fa04880b7f63c9980b706683127e3c1b57cb80ea96d47991bdae2dfe479604f6a1ba410106ee1046a41d1bd0814400
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"hosted-git-info@npm:^2.1.4":
  version: 2.8.9
  resolution: "hosted-git-info@npm:2.8.9"
  checksum: c955394bdab888a1e9bb10eb33029e0f7ce5a2ac7b3f158099dc8c486c99e73809dca609f5694b223920ca2174db33d32b12f9a2a47141dc59607c29da5a62dd
  languageName: node
  linkType: hard

"hosted-git-info@npm:^4.1.0":
  version: 4.1.0
  resolution: "hosted-git-info@npm:4.1.0"
  dependencies:
    lru-cache: ^6.0.0
  checksum: c3f87b3c2f7eb8c2748c8f49c0c2517c9a95f35d26f4bf54b2a8cba05d2e668f3753548b6ea366b18ec8dadb4e12066e19fa382a01496b0ffa0497eb23cbe461
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.0.0, http-cache-semantics@npm:^4.1.0, http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 7a7246ddfce629f96832791176fd643589d954e6f3b49548dadb4290451961237fab8fcea41cd2008fe819d95b41c1e8b97f47d088afc0a1c81705287b4ddbcc
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0, http-errors@npm:^2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: 2.0.0
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: 2.0.1
    toidentifier: 1.0.1
  checksum: 9b0a3782665c52ce9dc658a0d1560bcb0214ba5699e4ea15aefb2a496e2ca83db03ebc42e1cce4ac1f413e4e0d2d736a3fd755772c556a9a06853ba2a0b7d920
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^4.0.1":
  version: 4.0.1
  resolution: "http-proxy-agent@npm:4.0.1"
  dependencies:
    "@tootallnate/once": 1
    agent-base: 6
    debug: 4
  checksum: c6a5da5a1929416b6bbdf77b1aca13888013fe7eb9d59fc292e25d18e041bb154a8dfada58e223fc7b76b9b2d155a87e92e608235201f77d34aa258707963a82
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "http-proxy-agent@npm:5.0.0"
  dependencies:
    "@tootallnate/once": 2
    agent-base: 6
    debug: 4
  checksum: e2ee1ff1656a131953839b2a19cd1f3a52d97c25ba87bd2559af6ae87114abf60971e498021f9b73f9fd78aea8876d1fb0d4656aac8a03c6caa9fc175f22b786
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: ^7.1.0
    debug: ^4.3.4
  checksum: 670858c8f8f3146db5889e1fa117630910101db601fff7d5a8aa637da0abedf68c899f03d3451cac2f83bcc4c3d2dabf339b3aa00ff8080571cceb02c3ce02f3
  languageName: node
  linkType: hard

"http2-wrapper@npm:^1.0.0-beta.5.2":
  version: 1.0.3
  resolution: "http2-wrapper@npm:1.0.3"
  dependencies:
    quick-lru: ^5.1.1
    resolve-alpn: ^1.0.0
  checksum: 74160b862ec699e3f859739101ff592d52ce1cb207b7950295bf7962e4aa1597ef709b4292c673bece9c9b300efad0559fc86c71b1409c7a1e02b7229456003e
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: 6
    debug: 4
  checksum: 571fccdf38184f05943e12d37d6ce38197becdd69e58d03f43637f7fa1269cf303a7d228aa27e5b27bbd3af8f09fd938e1c91dcfefff2df7ba77c20ed8dfc765
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.0, https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: ^7.1.2
    debug: 4
  checksum: b882377a120aa0544846172e5db021fa8afbf83fea2a897d397bd2ddd8095ab268c24bc462f40a15f2a8c600bf4aa05ce52927f70038d4014e68aefecfa94e8d
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: ^2.0.0
  checksum: 9c7a74a2827f9294c009266c82031030eae811ca87b0da3dceb8d6071b9bde22c9f3daef0469c3c533cc67a97d8a167cd9fc0389350e5f415f61a79b171ded16
  languageName: node
  linkType: hard

"iconv-corefoundation@npm:^1.1.7":
  version: 1.1.7
  resolution: "iconv-corefoundation@npm:1.1.7"
  dependencies:
    cli-truncate: ^2.1.0
    node-addon-api: ^1.6.3
  conditions: os=darwin
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6.3, iconv-lite@npm:^0.6.2, iconv-lite@npm:^0.6.3":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 5144c0c9815e54ada181d80a0b810221a253562422e7c6c3a60b1901154184f49326ec239d618c416c1c5945a2e197107aee8d986a3dd836b53dffefd99b5e7e
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 2acfd32a573260ea522ea0bfeff880af426d68f6831f973129e2ba7363f422923cf53aab62f8369cbf4667c7b25b6f8a3761b34ecdb284ea18e87a5262a865be
  languageName: node
  linkType: hard

"ignore@npm:^7.0.0, ignore@npm:^7.0.5":
  version: 7.0.5
  resolution: "ignore@npm:7.0.5"
  checksum: d0862bf64d3d58bf34d5fb0a9f725bec9ca5ce8cd1aecc8f28034269e8f69b8009ffd79ca3eda96962a6a444687781cd5efdb8c7c8ddc0a6996e36d31c217f14
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: a06b19461b4879cc654d46f8a6244eb55eb053437afd4cbb6613cad6be203811849ed3e4ea038783092879487299fda24af932b86bdfff67c9055ba3612b8c87
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"infer-owner@npm:^1.0.4":
  version: 1.0.4
  resolution: "infer-owner@npm:1.0.4"
  checksum: 181e732764e4a0611576466b4b87dac338972b839920b2a8cde43642e4ed6bd54dc1fb0b40874728f2a2df9a1b097b8ff83b56d5f8f8e3927f837fdcb47d8a89
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.3, inherits@npm:^2.0.4":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: dfd98b0ca3a4fc1e323e38a6c8eb8936e31a97a918d3b377649ea15bdb15d481207a0dda1021efbd86b464cae29a0d33c1d7dcaf6c5672bee17fa849bc50a1b3
  languageName: node
  linkType: hard

"input-otp@npm:^1.2.4":
  version: 1.4.2
  resolution: "input-otp@npm:1.4.2"
  peerDependencies:
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc
  checksum: 9c1a21ac36c8ea6857cb1ea894d88f44d0df2ef2e78090560cbc491888ef44ccd62e6390bc0a826939d6e8d50fbe6bddc6fe082180de715c6fab8ccf52aa2426
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    hasown: ^2.0.2
    side-channel: ^1.1.0
  checksum: 8e0991c2d048cc08dab0a91f573c99f6a4215075887517ea4fa32203ce8aea60fa03f95b177977fa27eb502e5168366d0f3e02c762b799691411d49900611861
  languageName: node
  linkType: hard

"internmap@npm:1 - 2":
  version: 2.0.3
  resolution: "internmap@npm:2.0.3"
  checksum: 7ca41ec6aba8f0072fc32fa8a023450a9f44503e2d8e403583c55714b25efd6390c38a87161ec456bf42d7bc83aab62eb28f5aef34876b1ac4e60693d5e1d241
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: 1.1.0
    sprintf-js: ^1.1.3
  checksum: aa15f12cfd0ef5e38349744e3654bae649a34c3b10c77a674a167e99925d1549486c5b14730eebce9fea26f6db9d5e42097b00aa4f9f612e68c79121c71652dc
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: f88d3825981486f5a1942414c8d77dd6674dd71c065adcfa46f578d677edcb99fda25af42675cb59db492fdf427b34a5abfcde3982da11a8fd83a500b41cfe77
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    get-intrinsic: ^1.2.6
  checksum: f137a2a6e77af682cdbffef1e633c140cf596f72321baf8bba0f4ef22685eb4339dde23dfe9e9ca430b5f961dee4d46577dcf12b792b68518c8449b134fb9156
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 977e64f54d91c8f169b59afcd80ff19227e9f5c791fa28fa2e5bce355cbaf6c2c356711b734656e80c9dd4a854dd7efcf7894402f1031dfc5de5d620775b4d5f
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: ^1.0.0
    call-bound: ^1.0.3
    get-proto: ^1.0.1
    has-tostringtag: ^1.0.2
    safe-regex-test: ^1.1.0
  checksum: 9bece45133da26636488ca127d7686b85ad3ca18927e2850cff1937a650059e90be1c71a48623f8791646bb7a241b0cabf602a0b9252dcfa5ab273f2399000e6
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: ^1.0.2
  checksum: ee1544f0e664f253306786ed1dce494b8cf242ef415d6375d8545b4d8816b0f054bd9f948a8988ae2c6325d1c28260dd02978236b2f7b8fb70dfc4838a6c9fa7
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 0415b181e8f1bfd5d3f8a20f8108e64d372a72131674eea9c2923f39d065b6ad08d654765553bdbffbd92c3746f1007986c34087db1bd89a31f71be8359ccdaa
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 61fd57d03b0d984e2ed3720fb1c7a897827ea174bd44402878e059542ea8c4aeedee0ea0985998aa5cc2736b2fa6e271c08587addb5b3959ac52cf665173d1ac
  languageName: node
  linkType: hard

"is-ci@npm:^3.0.0":
  version: 3.0.1
  resolution: "is-ci@npm:3.0.1"
  dependencies:
    ci-info: ^3.2.0
  bin:
    is-ci: bin.js
  checksum: 192c66dc7826d58f803ecae624860dccf1899fc1f3ac5505284c0a5cf5f889046ffeb958fa651e5725d5705c5bcb14f055b79150ea5fcad7456a9569de60260e
  languageName: node
  linkType: hard

"is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: ^2.0.2
  checksum: 6ec5b3c42d9cbf1ac23f164b16b8a140c3cec338bf8f884c076ca89950c7cc04c33e78f02b8cae7ff4751f3247e3174b2330f1fe4de194c7210deb8b1ea316a7
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    get-intrinsic: ^1.2.6
    is-typed-array: ^1.1.13
  checksum: 31600dd19932eae7fd304567e465709ffbfa17fa236427c9c864148e1b54eb2146357fcf3aed9b686dee13c217e1bb5a649cb3b9c479e1004c0648e9febde1b2
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.2
    has-tostringtag: ^1.0.2
  checksum: d6c36ab9d20971d65f3fc64cef940d57a4900a2ac85fb488a46d164c2072a33da1cb51eefcc039e3e5c208acbce343d3480b84ab5ff0983f617512da2742562a
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
  checksum: 38c646c506e64ead41a36c182d91639833311970b6b6c6268634f109eef0a1a9d2f1f2e499ef4cb43c744a13443c4cdd2f0812d5afdcee5e9b65b72b28c48557
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    get-proto: ^1.0.0
    has-tostringtag: ^1.0.2
    safe-regex-test: ^1.1.0
  checksum: f7f7276131bdf7e28169b86ac55a5b080012a597f9d85a0cbef6fe202a7133fa450a3b453e394870e3cb3685c5a764c64a9f12f614684b46969b1e6f297bed6b
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-interactive@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-interactive@npm:1.0.0"
  checksum: 824808776e2d468b2916cdd6c16acacebce060d844c35ca6d82267da692e92c3a16fdba624c50b54a63f38bdc4016055b6f443ce57d7147240de4f8cdabaf6f9
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 93a32f01940220532e5948538699ad610d5924ac86093fcee83022252b363eb0cc99ba53ab084a04e4fb62bf7b5731f55496257a4c38adf87af9c4d352c71c35
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: e6ce5f6380f32b141b3153e6ba9074892bbbbd655e92e7ba5ff195239777e767a976dcd4e22f864accaf30e53ebf961ab1995424aef91af68788f0591b7396cc
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-negative-zero@npm:2.0.3"
  checksum: c1e6b23d2070c0539d7b36022d5a94407132411d01aba39ec549af824231f3804b1aea90b5e4e58e807a65d23ceb538ed6e355ce76b267bdd86edb757ffcbdcd
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 6517f0a0e8c4b197a21afb45cd3053dc711e79d45d8878aa3565de38d0102b130ca8732485122c7b336e98c27dacd5236854e3e6526e0eb30cae64956535662f
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-promise@npm:^4.0.0":
  version: 4.0.0
  resolution: "is-promise@npm:4.0.0"
  checksum: 0b46517ad47b00b6358fd6553c83ec1f6ba9acd7ffb3d30a0bf519c5c69e7147c132430452351b8a9fc198f8dd6c4f76f8e6f5a7f100f8c77d57d9e0f4261a8a
  languageName: node
  linkType: hard

"is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: ^1.0.2
    gopd: ^1.2.0
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: 99ee0b6d30ef1bb61fa4b22fae7056c6c9b3c693803c0c284ff7a8570f83075a7d38cda53b06b7996d441215c27895ea5d1af62124562e13d91b3dbec41a5e13
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 36e3f8c44bdbe9496c9689762cc4110f6a6a12b767c5d74c0398176aa2678d4467e3bf07595556f2dba897751bde1422480212b97d973c7b08a343100b0c0dfe
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: ^1.0.3
  checksum: 1611fedc175796eebb88f4dfc393dd969a4a8e6c69cadaff424ee9d4464f9f026399a5f84a90f7c62d6d7ee04e3626a912149726de102b0bd6c1ee6a9868fa5a
  languageName: node
  linkType: hard

"is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 2eeaaff605250f5e836ea3500d33d1a5d3aa98d008641d9d42fb941e929ffd25972326c2ef912987e54c95b6f10416281aaf1b35cdf81992cfb7524c5de8e193
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.2
    has-symbols: ^1.1.0
    safe-regex-test: ^1.1.0
  checksum: bfafacf037af6f3c9d68820b74be4ae8a736a658a3344072df9642a090016e281797ba8edbeb1c83425879aae55d1cb1f30b38bf132d703692b2570367358032
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: ^1.1.16
  checksum: ea7cfc46c282f805d19a9ab2084fd4542fed99219ee9dbfbc26284728bd713a51eac66daa74eca00ae0a43b61322920ba334793607dc39907465913e921e0892
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: a2aab86ee7712f5c2f999180daaba5f361bdad1efadc9610ff5b8ab5495b86e4f627839d085c6530363c6d6d4ecbde340fb8e54bdb83da4ba8e0865ed5513c52
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: f36aef758b46990e0d3c37269619c0a08c5b29428c0bb11ecba7f75203442d6c7801239c2f31314bc79199217ef08263787f3837d9e22610ad1da62970d6616d
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
  checksum: 1769b9aed5d435a3a989ffc18fc4ad1947d2acdaf530eb2bd6af844861b545047ea51102f75901f89043bed0267ed61d914ee21e6e8b9aa734ec201cdfc0726f
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: ^1.0.3
    get-intrinsic: ^1.2.6
  checksum: 5c6c8415a06065d78bdd5e3a771483aa1cd928df19138aa73c4c51333226f203f22117b4325df55cc8b3085a6716870a320c2d757efee92d7a7091a039082041
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: bd5bbe4104438c4196ba58a54650116007fa0262eccef13a4c55b2e09a5b36b59f1e75b9fcc49883dd9d4953892e6fc007eef9e9155648ceea036e184b0f930a
  languageName: node
  linkType: hard

"isbinaryfile@npm:^4.0.8":
  version: 4.0.10
  resolution: "isbinaryfile@npm:4.0.10"
  checksum: a6b28db7e23ac7a77d3707567cac81356ea18bd602a4f21f424f862a31d0e7ab4f250759c98a559ece35ffe4d99f0d339f1ab884ffa9795172f632ab8f88e686
  languageName: node
  linkType: hard

"isbinaryfile@npm:^5.0.0":
  version: 5.0.4
  resolution: "isbinaryfile@npm:5.0.4"
  checksum: d88982a889369d83a5937b4b4d2288ed3b3dbbcee8fc74db40058f3c089a2c7beb9e5305b7177e82d87ff38fb62be8d60960f7a2d669ca08240ef31c1435b884
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: be31027fc72e7cc726206b9f560395604b82e0fddb46c4cbf9f97d049bcef607491a5afc0699612eaa4213ca5be8fd3e1e7cd187b3040988b65c9489838a7c00
  languageName: node
  linkType: hard

"jackspeak@npm:^4.1.1":
  version: 4.1.1
  resolution: "jackspeak@npm:4.1.1"
  dependencies:
    "@isaacs/cliui": ^8.0.2
  checksum: daca714c5adebfb80932c0b0334025307b68602765098d73d52ec546bc4defdb083292893384261c052742255d0a77d8fcf96f4c669bcb4a99b498b94a74955e
  languageName: node
  linkType: hard

"jake@npm:^10.8.5":
  version: 10.9.2
  resolution: "jake@npm:10.9.2"
  dependencies:
    async: ^3.2.3
    chalk: ^4.0.2
    filelist: ^1.0.4
    minimatch: ^3.1.2
  bin:
    jake: bin/cli.js
  checksum: f2dc4a086b4f58446d02cb9be913c39710d9ea570218d7681bb861f7eeaecab7b458256c946aeaa7e548c5e0686cc293e6435501e4047174a3b6a504dcbfcaae
  languageName: node
  linkType: hard

"jiti@npm:^1.21.6":
  version: 1.21.7
  resolution: "jiti@npm:1.21.7"
  bin:
    jiti: bin/jiti.js
  checksum: 9cd20dabf82e3a4cceecb746a69381da7acda93d34eed0cdb9c9bdff3bce07e4f2f4a016ca89924392c935297d9aedc58ff9f7d3281bc5293319ad244926e0b7
  languageName: node
  linkType: hard

"js-tiktoken@npm:^1.0.20":
  version: 1.0.20
  resolution: "js-tiktoken@npm:1.0.20"
  dependencies:
    base64-js: ^1.5.1
  checksum: 29106a6faa65c85d13ead291ce17b007ef7c16918fdd570d4636b74da33e54b72af66f9d5dd963f2979733f70d4221e4a9655d236395719c5cc04620d9f1e2cd
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 944f924f2bd67ad533b3850eee47603eed0f6ae425fd1ee8c760f477e8c34a05f144c1bd4f5a5dd1963141dc79a2c55f89ccc5ab77d039e7077f3ad196b64965
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 19c94095ea026725540c0d29da33ab03144f6bcf2d4159e4833d534976e99e0c09c38cefa9a575279a51fc36b31166f8d6d05c9fe2645d5f15851d690b41f17f
  languageName: node
  linkType: hard

"json-bignum@npm:^0.0.3":
  version: 0.0.3
  resolution: "json-bignum@npm:0.0.3"
  checksum: e64b69089fa6760ef6373138754fece6467110a769a57991f6c9f0abf203413606540200e0682c8d3b377421aa9584eeccfaef424f7d8253b3b74c6b670b2fab
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 9026b03edc2847eefa2e37646c579300a1f3a4586cfb62bf857832b60c852042d0d6ae55d1afb8926163fa54c2b01d83ae24705f34990348bdac6273a29d4581
  languageName: node
  linkType: hard

"json-parse-better-errors@npm:^1.0.1":
  version: 1.0.2
  resolution: "json-parse-better-errors@npm:1.0.2"
  checksum: ff2b5ba2a70e88fd97a3cb28c1840144c5ce8fae9cbeeddba15afa333a5c407cf0e42300cd0a2885dbb055227fe68d405070faad941beeffbfde9cf3b2c78c5d
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json-stringify-safe@npm:^5.0.1":
  version: 5.0.1
  resolution: "json-stringify-safe@npm:5.0.1"
  checksum: 48ec0adad5280b8a96bb93f4563aa1667fd7a36334f79149abd42446d0989f2ddc58274b479f4819f1f00617957e6344c886c55d05a4e15ebb4ab931e4a6a8ee
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: ^4.1.6
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 6447d6224f0d31623eef9b51185af03ac328a7553efcee30fa423d98a9e276ca08db87d71e17f2310b0263fd3ffa6c2a90a6308367f661dc21580f9469897c9e
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: ^4.1.6
    universalify: ^2.0.0
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 7af3b8e1ac8fe7f1eccc6263c6ca14e1966fcbc74b618d3c78a0a2075579487547b94f72b7a1114e844a1e15bb00d440e5d1720bfc4612d790a6f285d5ea8354
  languageName: node
  linkType: hard

"keytar@npm:^7.9.0":
  version: 7.9.0
  resolution: "keytar@npm:7.9.0"
  dependencies:
    node-addon-api: ^4.3.0
    node-gyp: latest
    prebuild-install: ^7.0.1
  checksum: 4dbdd21f69e21a53032cbc949847f57338e42df763c5eec04e1b5d7142a689f95d8c3d74fb3b7dc321b5d678271d8d8d1a0dcaa919673ebc50ef8ce76f354e21
  languageName: node
  linkType: hard

"keyv@npm:^4.0.0, keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: 3.0.1
  checksum: 74a24395b1c34bd44ad5cb2b49140d087553e170625240b86755a6604cd65aa16efdbdeae5cdb17ba1284a0fbb25ad06263755dbc71b8d8b06f74232ce3cdd72
  languageName: node
  linkType: hard

"lazy-val@npm:^1.0.5":
  version: 1.0.5
  resolution: "lazy-val@npm:1.0.5"
  checksum: 31e12e0b118826dfae74f8f3ff8ebcddfe4200ff88d0d448db175c7265ee537e0ba55488d411728246337f3ed3c9ec68416f10889f632a2ce28fb7a970909fb5
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"lilconfig@npm:^3.0.0, lilconfig@npm:^3.1.3":
  version: 3.1.3
  resolution: "lilconfig@npm:3.1.3"
  checksum: 644eb10830350f9cdc88610f71a921f510574ed02424b57b0b3abb66ea725d7a082559552524a842f4e0272c196b88dfe1ff7d35ffcc6f45736777185cd67c9a
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"load-json-file@npm:^4.0.0":
  version: 4.0.0
  resolution: "load-json-file@npm:4.0.0"
  dependencies:
    graceful-fs: ^4.1.2
    parse-json: ^4.0.0
    pify: ^3.0.0
    strip-bom: ^3.0.0
  checksum: 8f5d6d93ba64a9620445ee9bde4d98b1eac32cf6c8c2d20d44abfa41a6945e7969456ab5f1ca2fb06ee32e206c9769a20eec7002fe290de462e8c884b6b8b356
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.camelcase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.camelcase@npm:4.3.0"
  checksum: cb9227612f71b83e42de93eccf1232feeb25e705bdb19ba26c04f91e885bfd3dd5c517c4a97137658190581d3493ea3973072ca010aab7e301046d90740393d1
  languageName: node
  linkType: hard

"lodash.castarray@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.castarray@npm:4.4.0"
  checksum: fca8c7047e0ae2738b0b2503fb00157ae0ff6d8a1b716f87ed715b22560e09de438c75b65e01a7e44ceb41c5b31dce2eb576e46db04beb9c699c498e03cbd00f
  languageName: node
  linkType: hard

"lodash.isplainobject@npm:^4.0.6":
  version: 4.0.6
  resolution: "lodash.isplainobject@npm:4.0.6"
  checksum: 29c6351f281e0d9a1d58f1a4c8f4400924b4c79f18dfc4613624d7d54784df07efaff97c1ff2659f3e085ecf4fff493300adc4837553104cef2634110b0d5337
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"lodash@npm:^4.17.15, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"log-symbols@npm:^4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: ^4.1.0
    is-unicode-supported: ^0.1.0
  checksum: fce1497b3135a0198803f9f07464165e9eb83ed02ceb2273930a6f8a508951178d8cf4f0378e9d28300a2ed2bc49050995d2bd5f53ab716bb15ac84d58c6ef74
  languageName: node
  linkType: hard

"long@npm:^4.0.0":
  version: 4.0.0
  resolution: "long@npm:4.0.0"
  checksum: 16afbe8f749c7c849db1f4de4e2e6a31ac6e617cead3bdc4f9605cb703cd20e1e9fc1a7baba674ffcca57d660a6e5b53a9e236d7b25a295d3855cca79cc06744
  languageName: node
  linkType: hard

"loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lovable-tagger@npm:^1.1.7":
  version: 1.1.8
  resolution: "lovable-tagger@npm:1.1.8"
  dependencies:
    "@babel/parser": ^7.25.9
    "@babel/types": ^7.25.8
    esbuild: ^0.25.0
    estree-walker: ^3.0.3
    magic-string: ^0.30.12
    tailwindcss: ^3.4.17
  peerDependencies:
    vite: ^5.0.0
  checksum: d0a97878ad59112cbf9dafa8cdea63d0cf43fcb93d1ece94d3a812963bd7ea7554c7d3980022f1674965f908c262a0c9bc0e31e341a54b507cc8203bfe6aa40d
  languageName: node
  linkType: hard

"lowercase-keys@npm:^2.0.0":
  version: 2.0.0
  resolution: "lowercase-keys@npm:2.0.0"
  checksum: 24d7ebd56ccdf15ff529ca9e08863f3c54b0b9d1edb97a3ae1af34940ae666c01a1e6d200707bce730a8ef76cb57cc10e65f245ecaaf7e6bc8639f2fb460ac23
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 6476138d2125387a6d20f100608c2583d415a4f64a0fecf30c9e2dda976614f09cad4baa0842447bd37dd459a7bd27f57d9d8f8ce558805abd487c583f3d774a
  languageName: node
  linkType: hard

"lru-cache@npm:^11.0.0":
  version: 11.1.0
  resolution: "lru-cache@npm:11.1.0"
  checksum: 6274e90b5fdff87570fe26fe971467a5ae1f25f132bebe187e71c5627c7cd2abb94b47addd0ecdad034107667726ebde1abcef083d80f2126e83476b2c4e7c82
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: c154ae1cbb0c2206d1501a0e94df349653c92c8cbb25236d7e85190bcaf4567a03ac6eb43166fabfa36fd35623694da7233e88d9601fbf411a9a481d85dbd2cb
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: ^4.0.0
  checksum: f97f499f898f23e4585742138a22f22526254fdba6d75d41a1c2526b3b6cc5747ef59c5612ba7375f42aca4f8461950e925ba08c991ead0651b4918b7c978297
  languageName: node
  linkType: hard

"lru-cache@npm:^7.7.1":
  version: 7.18.3
  resolution: "lru-cache@npm:7.18.3"
  checksum: e550d772384709deea3f141af34b6d4fa392e2e418c1498c078de0ee63670f1f46f5eee746e8ef7e69e1c895af0d4224e62ee33e66a543a14763b0f2e74c1356
  languageName: node
  linkType: hard

"lucide-react@npm:^0.462.0":
  version: 0.462.0
  resolution: "lucide-react@npm:0.462.0"
  peerDependencies:
    react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0-rc
  checksum: ffeb54d9c22e6625727f6dc863ae4cde952dbc02ff50c96179f3c0ce14b2f9a63ae8472fe86387c17a534bdae1ca0f518e81c47553a4f97718139805befc625e
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.12":
  version: 0.30.17
  resolution: "magic-string@npm:0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec": ^1.5.0
  checksum: f4b4ed17c5ada64f77fc98491847302ebad64894a905c417c943840c0384662118c9b37f9f68bb86add159fa4749ff6f118c4627d69a470121b46731f8debc6d
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^10.2.1":
  version: 10.2.1
  resolution: "make-fetch-happen@npm:10.2.1"
  dependencies:
    agentkeepalive: ^4.2.1
    cacache: ^16.1.0
    http-cache-semantics: ^4.1.0
    http-proxy-agent: ^5.0.0
    https-proxy-agent: ^5.0.0
    is-lambda: ^1.0.1
    lru-cache: ^7.7.1
    minipass: ^3.1.6
    minipass-collect: ^1.0.2
    minipass-fetch: ^2.0.3
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^0.6.3
    promise-retry: ^2.0.1
    socks-proxy-agent: ^7.0.0
    ssri: ^9.0.0
  checksum: 2332eb9a8ec96f1ffeeea56ccefabcb4193693597b132cd110734d50f2928842e22b84cfa1508e921b8385cdfd06dda9ad68645fed62b50fff629a580f5fb72c
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": ^3.0.0
    cacache: ^19.0.1
    http-cache-semantics: ^4.1.1
    minipass: ^7.0.2
    minipass-fetch: ^4.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^1.0.0
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
    ssri: ^12.0.0
  checksum: 6fb2fee6da3d98f1953b03d315826b5c5a4ea1f908481afc113782d8027e19f080c85ae998454de4e5f27a681d3ec58d57278f0868d4e0b736f51d396b661691
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^9.1.0":
  version: 9.1.0
  resolution: "make-fetch-happen@npm:9.1.0"
  dependencies:
    agentkeepalive: ^4.1.3
    cacache: ^15.2.0
    http-cache-semantics: ^4.1.0
    http-proxy-agent: ^4.0.1
    https-proxy-agent: ^5.0.0
    is-lambda: ^1.0.1
    lru-cache: ^6.0.0
    minipass: ^3.1.3
    minipass-collect: ^1.0.2
    minipass-fetch: ^1.3.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^0.6.2
    promise-retry: ^2.0.1
    socks-proxy-agent: ^6.0.0
    ssri: ^8.0.0
  checksum: 0eb371c85fdd0b1584fcfdf3dc3c62395761b3c14658be02620c310305a9a7ecf1617a5e6fb30c1d081c5c8aaf177fa133ee225024313afabb7aa6a10f1e3d04
  languageName: node
  linkType: hard

"matcher@npm:^3.0.0":
  version: 3.0.0
  resolution: "matcher@npm:3.0.0"
  dependencies:
    escape-string-regexp: ^4.0.0
  checksum: 8bee1a7ab7609c2c21d9c9254b6785fa708eadf289032b556d57a34e98fcd4c537659a004dafee6ce80ab157099e645c199dc52678dff1e7fb0a6684e0da4dbe
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 0e513b29d120f478c85a70f49da0b8b19bc638975eca466f2eeae0071f3ad00454c621bf66e16dd435896c208e719fc91ad79bbfba4e400fe0b372e7c1c9c9a2
  languageName: node
  linkType: hard

"media-typer@npm:^1.1.0":
  version: 1.1.0
  resolution: "media-typer@npm:1.1.0"
  checksum: a58dd60804df73c672942a7253ccc06815612326dc1c0827984b1a21704466d7cde351394f47649e56cf7415e6ee2e26e000e81b51b3eebb5a93540e8bf93cbd
  languageName: node
  linkType: hard

"memorystream@npm:^0.3.1":
  version: 0.3.1
  resolution: "memorystream@npm:0.3.1"
  checksum: f18b42440d24d09516d01466c06adf797df7873f0d40aa7db02e5fb9ed83074e5e65412d0720901d7069363465f82dc4f8bcb44f0cde271567a61426ce6ca2e9
  languageName: node
  linkType: hard

"merge-descriptors@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-descriptors@npm:2.0.0"
  checksum: e383332e700a94682d0125a36c8be761142a1320fc9feeb18e6e36647c9edf064271645f5669b2c21cf352116e561914fd8aa831b651f34db15ef4038c86696a
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: ^3.0.3
    picomatch: ^2.3.1
  checksum: 79920eb634e6f400b464a954fcfa589c4e7c7143209488e44baf627f9affc8b1e306f41f4f0deedde97e69cb725920879462d3e750ab3bd3c1aed675bb3a8966
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-db@npm:^1.54.0":
  version: 1.54.0
  resolution: "mime-db@npm:1.54.0"
  checksum: e99aaf2f23f5bd607deb08c83faba5dd25cf2fec90a7cc5b92d8260867ee08dab65312e1a589e60093dc7796d41e5fae013268418482f1db4c7d52d0a0960ac9
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"mime-types@npm:^3.0.0, mime-types@npm:^3.0.1":
  version: 3.0.1
  resolution: "mime-types@npm:3.0.1"
  dependencies:
    mime-db: ^1.54.0
  checksum: 8d497ad5cb2dd1210ac7d049b5de94af0b24b45a314961e145b44389344604d54752f03bc00bf880c0da60a214be6fb6d423d318104f02c28d95dd8ebeea4fb4
  languageName: node
  linkType: hard

"mime@npm:^2.5.2":
  version: 2.6.0
  resolution: "mime@npm:2.6.0"
  bin:
    mime: cli.js
  checksum: 1497ba7b9f6960694268a557eae24b743fd2923da46ec392b042469f4b901721ba0adcf8b0d3c2677839d0e243b209d76e5edcbd09cfdeffa2dfb6bb4df4b862
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mimic-response@npm:^1.0.0":
  version: 1.0.1
  resolution: "mimic-response@npm:1.0.1"
  checksum: 034c78753b0e622bc03c983663b1cdf66d03861050e0c8606563d149bc2b02d63f62ce4d32be4ab50d0553ae0ffe647fc34d1f5281184c6e1e8cf4d85e8d9823
  languageName: node
  linkType: hard

"mimic-response@npm:^3.1.0":
  version: 3.1.0
  resolution: "mimic-response@npm:3.1.0"
  checksum: 25739fee32c17f433626bf19f016df9036b75b3d84a3046c7d156e72ec963dd29d7fc8a302f55a3d6c5a4ff24259676b15d915aad6480815a969ff2ec0836867
  languageName: node
  linkType: hard

"minimatch@npm:^10.0.0, minimatch@npm:^10.0.1, minimatch@npm:^10.0.3":
  version: 10.0.3
  resolution: "minimatch@npm:10.0.3"
  dependencies:
    "@isaacs/brace-expansion": ^5.0.0
  checksum: 20bfb708095a321cb43c20b78254e484cb7d23aad992e15ca3234a3331a70fa9cd7a50bc1a7c7b2b9c9890c37ff0685f8380028fcc28ea5e6de75b1d4f9374aa
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.0.5, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 7564208ef81d7065a370f788d337cd80a689e981042cb9a1d0e6580b6c6a8c9279eba80010516e258835a988363f99f54a6f711a315089b8b42694f5da9d0d77
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.3, minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 2c035575eda1e50623c731ec6c14f65a85296268f749b9337005210bb2b34e2705f8ef1a358b188f69892286ab99dc42c8fb98a57bde55c8d81b3023c19cea28
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.3, minimist@npm:^1.2.5, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^1.0.2":
  version: 1.0.2
  resolution: "minipass-collect@npm:1.0.2"
  dependencies:
    minipass: ^3.0.0
  checksum: 14df761028f3e47293aee72888f2657695ec66bd7d09cae7ad558da30415fdc4752bbfee66287dcc6fd5e6a2fa3466d6c484dc1cbd986525d9393b9523d97f10
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: ^7.0.3
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^1.3.2":
  version: 1.4.1
  resolution: "minipass-fetch@npm:1.4.1"
  dependencies:
    encoding: ^0.1.12
    minipass: ^3.1.0
    minipass-sized: ^1.0.3
    minizlib: ^2.0.0
  dependenciesMeta:
    encoding:
      optional: true
  checksum: ec93697bdb62129c4e6c0104138e681e30efef8c15d9429dd172f776f83898471bc76521b539ff913248cc2aa6d2b37b652c993504a51cc53282563640f29216
  languageName: node
  linkType: hard

"minipass-fetch@npm:^2.0.3":
  version: 2.1.2
  resolution: "minipass-fetch@npm:2.1.2"
  dependencies:
    encoding: ^0.1.13
    minipass: ^3.1.6
    minipass-sized: ^1.0.3
    minizlib: ^2.1.2
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 3f216be79164e915fc91210cea1850e488793c740534985da017a4cbc7a5ff50506956d0f73bb0cb60e4fe91be08b6b61ef35101706d3ef5da2c8709b5f08f91
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^3.0.1
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 3dfca705ce887ca9ff14d73e8d8593996dea1a1ecd8101fdbb9c10549d1f9670bc8fb66ad0192769ead4c2dc01b4f9ca1cf567ded365adff17827a303b948140
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.2, minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0, minipass@npm:^3.1.0, minipass@npm:^3.1.1, minipass@npm:^3.1.3, minipass@npm:^3.1.6":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 425dab288738853fded43da3314a0b5c035844d6f3097a8e3b5b29b328da8f3c1af6fc70618b32c29ff906284cf6406b6841376f21caaadd0793c1d5a6a620ea
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 2bfd325b95c555f2b4d2814d49325691c7bee937d753814861b0b49d5edcda55cbbf22b6b6a60bb91eddac8668771f03c5ff647dcd9d0f798e9548b9cdc46ee3
  languageName: node
  linkType: hard

"minizlib@npm:^2.0.0, minizlib@npm:^2.1.1, minizlib@npm:^2.1.2":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: ^3.0.0
    yallist: ^4.0.0
  checksum: f1fdeac0b07cf8f30fcf12f4b586795b97be856edea22b5e9072707be51fc95d41487faec3f265b42973a304fe3a64acd91a44a3826a963e37b37bafde0212c3
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: ^7.1.2
  checksum: 493bed14dcb6118da7f8af356a8947cf1473289c09658e5aabd69a737800a8c3b1736fb7d7931b722268a9c9bc038a6d53c049b6a6af24b34a121823bb709996
  languageName: node
  linkType: hard

"mkdirp-classic@npm:^0.5.2, mkdirp-classic@npm:^0.5.3":
  version: 0.5.3
  resolution: "mkdirp-classic@npm:0.5.3"
  checksum: 3f4e088208270bbcc148d53b73e9a5bd9eef05ad2cbf3b3d0ff8795278d50dd1d11a8ef1875ff5aea3fa888931f95bfcb2ad5b7c1061cfefd6284d199e6776ac
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3, mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 972deb188e8fb55547f1e58d66bd6b4a3623bf0c7137802582602d73e6480c1c2268dcbafbfb1be466e00cc7e56ac514d7fd9334b7cf33e3e2ab547c16f83a8d
  languageName: node
  linkType: hard

"ms@npm:^2.0.0, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"mz@npm:^2.7.0":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: ^1.0.0
    object-assign: ^4.0.1
    thenify-all: ^1.0.0
  checksum: 8427de0ece99a07e9faed3c0c6778820d7543e3776f9a84d22cf0ec0a8eb65f6e9aee9c9d353ff9a105ff62d33a9463c6ca638974cc652ee8140cd1e35951c87
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.11":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 3be20d8866a57a6b6d218e82549711c8352ed969f9ab3c45379da28f405363ad4c9aeb0b39e9abc101a529ca65a72ff9502b00bf74a912c4b64a9d62dfd26c29
  languageName: node
  linkType: hard

"napi-build-utils@npm:^2.0.0":
  version: 2.0.0
  resolution: "napi-build-utils@npm:2.0.0"
  checksum: 532121efd2dd2272595580bca48859e404bdd4ed455a72a28432ba44868c38d0e64fac3026a8f82bf8563d2a18b32eb9a1d59e601a9da4e84ba4d45b922297f5
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:^0.6.2, negotiator@npm:^0.6.3":
  version: 0.6.4
  resolution: "negotiator@npm:0.6.4"
  checksum: 7ded10aa02a0707d1d12a9973fdb5954f98547ca7beb60e31cb3a403cc6e8f11138db7a3b0128425cf836fc85d145ec4ce983b2bdf83dca436af879c2d683510
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 20ebfe79b2d2e7cf9cbc8239a72662b584f71164096e6e8896c8325055497c96f6b80cd22c258e8a2f2aa382a787795ec3ee8b37b422a302c7d4381b0d5ecfbb
  languageName: node
  linkType: hard

"next-themes@npm:^0.3.0":
  version: 0.3.0
  resolution: "next-themes@npm:0.3.0"
  peerDependencies:
    react: ^16.8 || ^17 || ^18
    react-dom: ^16.8 || ^17 || ^18
  checksum: 4285c4969eac517ad7addd773bcb71e7d14bc6c6e3b24eb97b80a6e06ac03fb6cb345e75dfb448156d14430d06289948eb8cfdeb52402ca7ce786093d01d2878
  languageName: node
  linkType: hard

"nice-try@npm:^1.0.4":
  version: 1.0.5
  resolution: "nice-try@npm:1.0.5"
  checksum: 0b4af3b5bb5d86c289f7a026303d192a7eb4417231fe47245c460baeabae7277bcd8fd9c728fb6bd62c30b3e15cd6620373e2cf33353b095d8b403d3e8a15aff
  languageName: node
  linkType: hard

"node-abi@npm:^3.3.0, node-abi@npm:^3.45.0":
  version: 3.75.0
  resolution: "node-abi@npm:3.75.0"
  dependencies:
    semver: ^7.3.5
  checksum: b86021c748b316b31efda4f1f4a74db9fd411b0ae63fa50be5b0247546285ae7e31c737e92013478877eaf39a3fd0a06072d48b1cace21ad629862373410416f
  languageName: node
  linkType: hard

"node-abi@npm:^4.2.0":
  version: 4.12.0
  resolution: "node-abi@npm:4.12.0"
  dependencies:
    semver: ^7.6.3
  checksum: 257b6ad9b8118b89f89f15aa0fc97e1ef18716229c10e0a2ae61ca76a9814dffb3678c785022f10281b3219fd2cd8d1964f7f169ff43e26a05525aed971e03ee
  languageName: node
  linkType: hard

"node-addon-api@npm:^1.6.3":
  version: 1.7.2
  resolution: "node-addon-api@npm:1.7.2"
  dependencies:
    node-gyp: latest
  checksum: 938922b3d7cb34ee137c5ec39df6289a3965e8cab9061c6848863324c21a778a81ae3bc955554c56b6b86962f6ccab2043dd5fa3f33deab633636bd28039333f
  languageName: node
  linkType: hard

"node-addon-api@npm:^4.3.0":
  version: 4.3.0
  resolution: "node-addon-api@npm:4.3.0"
  dependencies:
    node-gyp: latest
  checksum: 3de396e23cc209f539c704583e8e99c148850226f6e389a641b92e8967953713228109f919765abc1f4355e801e8f41842f96210b8d61c7dcc10a477002dcf00
  languageName: node
  linkType: hard

"node-addon-api@npm:^6.1.0":
  version: 6.1.0
  resolution: "node-addon-api@npm:6.1.0"
  dependencies:
    node-gyp: latest
  checksum: 3a539510e677cfa3a833aca5397300e36141aca064cdc487554f2017110709a03a95da937e98c2a14ec3c626af7b2d1b6dabe629a481f9883143d0d5bff07bf2
  languageName: node
  linkType: hard

"node-addon-api@npm:^7.0.0":
  version: 7.1.1
  resolution: "node-addon-api@npm:7.1.1"
  dependencies:
    node-gyp: latest
  checksum: 46051999e3289f205799dfaf6bcb017055d7569090f0004811110312e2db94cb4f8654602c7eb77a60a1a05142cc2b96e1b5c56ca4622c41a5c6370787faaf30
  languageName: node
  linkType: hard

"node-addon-api@npm:^8.0.0, node-addon-api@npm:^8.2.1, node-addon-api@npm:^8.2.2, node-addon-api@npm:^8.3.0, node-addon-api@npm:^8.3.1":
  version: 8.4.0
  resolution: "node-addon-api@npm:8.4.0"
  dependencies:
    node-gyp: latest
  checksum: 66835ac32920532ec9617444e81818810ca861adace4d0c7bf58616cd7ffe4956bdc88b7f67f65e757229b56783e0c33ce1469bfae4b10fd3dfcca58f65325be
  languageName: node
  linkType: hard

"node-api-version@npm:^0.2.0, node-api-version@npm:^0.2.1":
  version: 0.2.1
  resolution: "node-api-version@npm:0.2.1"
  dependencies:
    semver: ^7.3.5
  checksum: 78a3056873a8a15c4b0f3ed1f64d294fe4e0ba4a4d08b5f9cfa06a8586ff9bc7c942ef17648171b000d7343d216b7e07dc4787d6ba98307f0a2de9ef13722f3a
  languageName: node
  linkType: hard

"node-gyp-build@npm:^4.8.0, node-gyp-build@npm:^4.8.1, node-gyp-build@npm:^4.8.2, node-gyp-build@npm:^4.8.4":
  version: 4.8.4
  resolution: "node-gyp-build@npm:4.8.4"
  bin:
    node-gyp-build: bin.js
    node-gyp-build-optional: optional.js
    node-gyp-build-test: build-test.js
  checksum: 8b81ca8ffd5fa257ad8d067896d07908a36918bc84fb04647af09d92f58310def2d2b8614d8606d129d9cd9b48890a5d2bec18abe7fcff54818f72bedd3a7d74
  languageName: node
  linkType: hard

"node-gyp@npm:8.x":
  version: 8.4.1
  resolution: "node-gyp@npm:8.4.1"
  dependencies:
    env-paths: ^2.2.0
    glob: ^7.1.4
    graceful-fs: ^4.2.6
    make-fetch-happen: ^9.1.0
    nopt: ^5.0.0
    npmlog: ^6.0.0
    rimraf: ^3.0.2
    semver: ^7.3.5
    tar: ^6.1.2
    which: ^2.0.2
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 341710b5da39d3660e6a886b37e210d33f8282047405c2e62c277bcc744c7552c5b8b972ebc3a7d5c2813794e60cc48c3ebd142c46d6e0321db4db6c92dd0355
  languageName: node
  linkType: hard

"node-gyp@npm:^11.2.0, node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    graceful-fs: ^4.2.6
    make-fetch-happen: ^14.0.3
    nopt: ^8.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
    tar: ^7.4.3
    tinyglobby: ^0.2.12
    which: ^5.0.0
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 2536282ba81f8a94b29482d3622b6ab298611440619e46de4512a6f32396a68b5530357c474b859787069d84a4c537d99e0c71078cce5b9f808bf84eeb78e8fb
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 917dbced519f48c6289a44830a0ca6dc944c3ee9243c468ebd8515a41c97c8b2c256edb7f3f750416bc37952cc9608684e6483c7b6c6f39f6bd8d86c52cfe658
  languageName: node
  linkType: hard

"nopt@npm:^5.0.0":
  version: 5.0.0
  resolution: "nopt@npm:5.0.0"
  dependencies:
    abbrev: 1
  bin:
    nopt: bin/nopt.js
  checksum: d35fdec187269503843924e0114c0c6533fb54bbf1620d0f28b4b60ba01712d6687f62565c55cc20a504eff0fbe5c63e22340c3fad549ad40469ffb611b04f2f
  languageName: node
  linkType: hard

"nopt@npm:^6.0.0":
  version: 6.0.0
  resolution: "nopt@npm:6.0.0"
  dependencies:
    abbrev: ^1.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 82149371f8be0c4b9ec2f863cc6509a7fd0fa729929c009f3a58e4eb0c9e4cae9920e8f1f8eb46e7d032fec8fb01bede7f0f41a67eb3553b7b8e14fa53de1dac
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: ^3.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 49cfd3eb6f565e292bf61f2ff1373a457238804d5a5a63a8d786c923007498cba89f3648e3b952bc10203e3e7285752abf5b14eaf012edb821e84f24e881a92a
  languageName: node
  linkType: hard

"normalize-package-data@npm:^2.3.2":
  version: 2.5.0
  resolution: "normalize-package-data@npm:2.5.0"
  dependencies:
    hosted-git-info: ^2.1.4
    resolve: ^1.10.0
    semver: 2 || 3 || 4 || 5
    validate-npm-package-license: ^3.0.1
  checksum: 7999112efc35a6259bc22db460540cae06564aa65d0271e3bdfa86876d08b0e578b7b5b0028ee61b23f1cae9fc0e7847e4edc0948d3068a39a2a82853efc8499
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-range@npm:^0.1.2":
  version: 0.1.2
  resolution: "normalize-range@npm:0.1.2"
  checksum: 9b2f14f093593f367a7a0834267c24f3cb3e887a2d9809c77d8a7e5fd08738bcd15af46f0ab01cc3a3d660386f015816b5c922cea8bf2ee79777f40874063184
  languageName: node
  linkType: hard

"normalize-url@npm:^6.0.1":
  version: 6.1.0
  resolution: "normalize-url@npm:6.1.0"
  checksum: 4a4944631173e7d521d6b80e4c85ccaeceb2870f315584fa30121f505a6dfd86439c5e3fdd8cd9e0e291290c41d0c3599f0cb12ab356722ed242584c30348e50
  languageName: node
  linkType: hard

"npm-run-all@npm:^4.1.5":
  version: 4.1.5
  resolution: "npm-run-all@npm:4.1.5"
  dependencies:
    ansi-styles: ^3.2.1
    chalk: ^2.4.1
    cross-spawn: ^6.0.5
    memorystream: ^0.3.1
    minimatch: ^3.0.4
    pidtree: ^0.3.0
    read-pkg: ^3.0.0
    shell-quote: ^1.6.1
    string.prototype.padend: ^3.0.0
  bin:
    npm-run-all: bin/npm-run-all/index.js
    run-p: bin/run-p/index.js
    run-s: bin/run-s/index.js
  checksum: 373b72c6a36564da13c1642c1fd9bb4dcc756bce7a3648f883772f02661095319820834ff813762d2fee403e9b40c1cd27c8685807c107440f10eb19c006d4a0
  languageName: node
  linkType: hard

"npmlog@npm:^6.0.0":
  version: 6.0.2
  resolution: "npmlog@npm:6.0.2"
  dependencies:
    are-we-there-yet: ^3.0.0
    console-control-strings: ^1.1.0
    gauge: ^4.0.3
    set-blocking: ^2.0.0
  checksum: ae238cd264a1c3f22091cdd9e2b106f684297d3c184f1146984ecbe18aaa86343953f26b9520dedd1b1372bc0316905b736c1932d778dbeb1fcf5a1001390e2a
  languageName: node
  linkType: hard

"object-assign@npm:^4, object-assign@npm:^4.0.1, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-hash@npm:^3.0.0":
  version: 3.0.0
  resolution: "object-hash@npm:3.0.0"
  checksum: 80b4904bb3857c52cc1bfd0b52c0352532ca12ed3b8a6ff06a90cd209dfda1b95cee059a7625eb9da29537027f68ac4619363491eedb2f5d3dddbba97494fd6c
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3, object-inspect@npm:^1.13.4":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 582810c6a8d2ef988ea0a39e69e115a138dad8f42dd445383b394877e5816eb4268489f316a6f74ee9c4e0a984b3eab1028e3e79d62b1ed67c726661d55c7a8b
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
    has-symbols: ^1.1.0
    object-keys: ^1.1.1
  checksum: 60e07d2651cf4f5528c485f1aa4dbded9b384c47d80e8187cefd11320abb1aebebf78df5483451dfa549059f8281c21f7b4bf7d19e9e5e97d8d617df0df298de
  languageName: node
  linkType: hard

"on-finished@npm:^2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: 1.1.1
  checksum: d20929a25e7f0bb62f937a425b5edeb4e4cde0540d77ba146ec9357f00b0d497cdb3b9b05b9c8e46222407d1548d08166bff69cc56dfa55ba0e4469228920ff0
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: ^2.1.0
  checksum: 2478859ef817fc5d4e9c2f9e5728512ddd1dbc9fb7829ad263765bb6d3b91ce699d6e2332eef6b7dff183c2f490bd3349f1666427eaba4469fba0ac38dfd0d34
  languageName: node
  linkType: hard

"onnx-proto@npm:^4.0.4":
  version: 4.0.4
  resolution: "onnx-proto@npm:4.0.4"
  dependencies:
    protobufjs: ^6.8.8
  checksum: 4122ea200bb4a7c93a464c5a49351025537f5b2c9a5848a9b090700437e6c458a44491096502324a3d7e6fb388be4967a824d12ac18d7be6721d0d5779400fd5
  languageName: node
  linkType: hard

"onnxruntime-common@npm:1.22.0":
  version: 1.22.0
  resolution: "onnxruntime-common@npm:1.22.0"
  checksum: 8555bcd18e6b33b2627c1acf63b875cfcfb66e588f017ee6fab09d96935c2fdfb9846a53a5fc0443c458abcb508de5b37b4d5e600fd9ac42fd92f8a461eb1bdc
  languageName: node
  linkType: hard

"onnxruntime-common@npm:~1.14.0":
  version: 1.14.0
  resolution: "onnxruntime-common@npm:1.14.0"
  checksum: 6f0dda57440e94ad8c3df80c9812b38651daa4482af4159bada6cf19f8e09a5258994e57038acdfd54ecab7b9779e0e8ce37b3315ee6c48dd6c1c943fd15fa13
  languageName: node
  linkType: hard

"onnxruntime-node@npm:1.14.0":
  version: 1.14.0
  resolution: "onnxruntime-node@npm:1.14.0"
  dependencies:
    onnxruntime-common: ~1.14.0
  conditions: (os=win32 | os=darwin | os=linux)
  languageName: node
  linkType: hard

"onnxruntime-node@npm:^1.22.0-rev":
  version: 1.22.0
  resolution: "onnxruntime-node@npm:1.22.0"
  dependencies:
    adm-zip: ^0.5.16
    global-agent: ^3.0.0
    onnxruntime-common: 1.22.0
  checksum: 5fb90c3ec3301f0e9836c4b719295f48d70d953ec45b270ac519facadbe609df87bd9ee8e0af9fc7f84a12484cf899d2f4721dd01e6ae86e4de0081cdfbee5ef
  conditions: (os=win32 | os=darwin | os=linux)
  languageName: node
  linkType: hard

"onnxruntime-web@npm:1.14.0":
  version: 1.14.0
  resolution: "onnxruntime-web@npm:1.14.0"
  dependencies:
    flatbuffers: ^1.12.0
    guid-typescript: ^1.0.9
    long: ^4.0.0
    onnx-proto: ^4.0.4
    onnxruntime-common: ~1.14.0
    platform: ^1.3.6
  checksum: 6faa8886683c301e267dad336a8f819a33253f3b3e93c0fe7af7df2aa45e61f6737b43119d68a448d17d08cbcd83e17607f9242e2222d5b4f9552351ddaa3289
  languageName: node
  linkType: hard

"openai@npm:^5.5.1":
  version: 5.8.3
  resolution: "openai@npm:5.8.3"
  peerDependencies:
    ws: ^8.18.0
    zod: ^3.23.8
  peerDependenciesMeta:
    ws:
      optional: true
    zod:
      optional: true
  bin:
    openai: bin/cli
  checksum: df65f6ca6f48f6e5e6c64ddce60550f005265aa30862e29822a2edf0ea8c61b08ff33f87807b9be3fa4964b4065fcad87385b883cdae0779e54ef2484e4485f5
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
    word-wrap: ^1.2.5
  checksum: ecbd010e3dc73e05d239976422d9ef54a82a13f37c11ca5911dff41c98a6c7f0f163b27f922c37e7f8340af9d36febd3b6e9cef508f3339d4c393d7276d716bb
  languageName: node
  linkType: hard

"ora@npm:^5.1.0":
  version: 5.4.1
  resolution: "ora@npm:5.4.1"
  dependencies:
    bl: ^4.1.0
    chalk: ^4.1.0
    cli-cursor: ^3.1.0
    cli-spinners: ^2.5.0
    is-interactive: ^1.0.0
    is-unicode-supported: ^0.1.0
    log-symbols: ^4.1.0
    strip-ansi: ^6.0.0
    wcwidth: ^1.0.1
  checksum: 28d476ee6c1049d68368c0dc922e7225e3b5600c3ede88fade8052837f9ed342625fdaa84a6209302587c8ddd9b664f71f0759833cbdb3a4cf81344057e63c63
  languageName: node
  linkType: hard

"os-utils@npm:^0.0.14":
  version: 0.0.14
  resolution: "os-utils@npm:0.0.14"
  checksum: bc0ee91ffca6f05150ab5d6a7fe5c691cca8eedff43afcc7a4e24a5d228c3b0a26fa841236d20b5fa385e3a07d8aed79a0eceebc8818a196cd18d2ca3546e098
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: ^1.2.6
    object-keys: ^1.1.1
    safe-push-apply: ^1.0.0
  checksum: cc9dd7d85c4ccfbe8109fce307d581ac7ede7b26de892b537873fbce2dc6a206d89aea0630dbb98e47ce0873517cefeaa7be15fcf94aaf4764a3b34b474a5b61
  languageName: node
  linkType: hard

"p-cancelable@npm:^2.0.0":
  version: 2.1.1
  resolution: "p-cancelable@npm:2.1.1"
  checksum: 3dba12b4fb4a1e3e34524535c7858fc82381bbbd0f247cc32dedc4018592a3950ce66b106d0880b4ec4c2d8d6576f98ca885dc1d7d0f274d1370be20e9523ddf
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2, p-limit@npm:^3.1.0 ":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-limit@npm:^6.1.0":
  version: 6.2.0
  resolution: "p-limit@npm:6.2.0"
  dependencies:
    yocto-queue: ^1.1.1
  checksum: ea46ba4a3013daa3335833013156bdcdd8f5c67b49ad2f33b34832b0d528f003d29b55bf5beeedc39ba9d9ada3dab7b1f4b890db3ebc8260a044a011126c6fc9
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: cb0ab21ec0f32ddffd31dfc250e3afa61e103ef43d957cc45497afe37513634589316de4eb88abdfd969fe6410c22c0b93ab24328833b8eb1ccc087fc0442a1c
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 8c92d533acf82f0d12f7e196edccff773f384098bbb048acdd55a08778ce4fc8889d8f1bde72969487bd96f9c63212698d79744c20bedfce36c5b00b46d369f8
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-json@npm:4.0.0"
  dependencies:
    error-ex: ^1.3.1
    json-parse-better-errors: ^1.0.1
  checksum: 0fe227d410a61090c247e34fa210552b834613c006c2c64d9a05cfe9e89cf8b4246d1246b1a99524b53b313e9ac024438d0680f67e33eaed7e6f38db64cfe7b5
  languageName: node
  linkType: hard

"parseurl@npm:^1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^2.0.1":
  version: 2.0.1
  resolution: "path-key@npm:2.0.1"
  checksum: f7ab0ad42fe3fb8c7f11d0c4f849871e28fbd8e1add65c370e422512fc5887097b9cf34d09c1747d45c942a8c1e26468d6356e2df3f740bf177ab8ca7301ebfd
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: ^10.2.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: 890d5abcd593a7912dcce7cf7c6bf7a0b5648e3dee6caf0712c126ca0a65c7f3d7b9d769072a4d1baf370f61ce493ab5b038d59988688e0c5f3f646ee3c69023
  languageName: node
  linkType: hard

"path-scurry@npm:^2.0.0":
  version: 2.0.0
  resolution: "path-scurry@npm:2.0.0"
  dependencies:
    lru-cache: ^11.0.0
    minipass: ^7.1.2
  checksum: 9953ce3857f7e0796b187a7066eede63864b7e1dfc14bf0484249801a5ab9afb90d9a58fc533ebb1b552d23767df8aa6a2c6c62caf3f8a65f6ce336a97bbb484
  languageName: node
  linkType: hard

"path-to-regexp@npm:^8.0.0":
  version: 8.2.0
  resolution: "path-to-regexp@npm:8.2.0"
  checksum: 56e13e45962e776e9e7cd72e87a441cfe41f33fd539d097237ceb16adc922281136ca12f5a742962e33d8dda9569f630ba594de56d8b7b6e49adf31803c5e771
  languageName: node
  linkType: hard

"path-type@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-type@npm:3.0.0"
  dependencies:
    pify: ^3.0.0
  checksum: 735b35e256bad181f38fa021033b1c33cfbe62ead42bb2222b56c210e42938eecb272ae1949f3b6db4ac39597a61b44edd8384623ec4d79bfdc9a9c0f12537a6
  languageName: node
  linkType: hard

"pe-library@npm:^0.4.1":
  version: 0.4.1
  resolution: "pe-library@npm:0.4.1"
  checksum: a31b532fd5e28c8d45d82fc5b774e220d2f6d8e9d1145d4711e0737a50e624eb4ff3e1b0f1faf44cc5be22abdf4aa9177ae4741d1cceb695188e5d1f1ccdede9
  languageName: node
  linkType: hard

"pend@npm:~1.2.0":
  version: 1.2.0
  resolution: "pend@npm:1.2.0"
  checksum: 6c72f5243303d9c60bd98e6446ba7d30ae29e3d56fdb6fae8767e8ba6386f33ee284c97efe3230a0d0217e2b1723b8ab490b1bbf34fcbb2180dbc8a9de47850d
  languageName: node
  linkType: hard

"picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: a7a5188c954f82c6585720e9143297ccd0e35ad8072231608086ca950bee672d51b0ef676254af0788205e59bd4e4deb4e7708769226bed725bf13370a7d1464
  languageName: node
  linkType: hard

"pidtree@npm:^0.3.0":
  version: 0.3.1
  resolution: "pidtree@npm:0.3.1"
  bin:
    pidtree: bin/pidtree.js
  checksum: eb49025099f1af89a4696f7673351421f13420f3397b963c901fe23a1c9c2ff50f4750321970d4472c0ffbb065e4a6c3c27f75e226cc62284b19e21d32ce7012
  languageName: node
  linkType: hard

"pify@npm:^2.3.0":
  version: 2.3.0
  resolution: "pify@npm:2.3.0"
  checksum: 9503aaeaf4577acc58642ad1d25c45c6d90288596238fb68f82811c08104c800e5a7870398e9f015d82b44ecbcbef3dc3d4251a1cbb582f6e5959fe09884b2ba
  languageName: node
  linkType: hard

"pify@npm:^3.0.0":
  version: 3.0.0
  resolution: "pify@npm:3.0.0"
  checksum: 6cdcbc3567d5c412450c53261a3f10991665d660961e06605decf4544a61a97a54fefe70a68d5c37080ff9d6f4cf51444c90198d1ba9f9309a6c0d6e9f5c4fde
  languageName: node
  linkType: hard

"pirates@npm:^4.0.1":
  version: 4.0.7
  resolution: "pirates@npm:4.0.7"
  checksum: 3dcbaff13c8b5bc158416feb6dc9e49e3c6be5fddc1ea078a05a73ef6b85d79324bbb1ef59b954cdeff000dbf000c1d39f32dc69310c7b78fbada5171b583e40
  languageName: node
  linkType: hard

"platform@npm:^1.3.6":
  version: 1.3.6
  resolution: "platform@npm:1.3.6"
  checksum: 6f472a09c61d418c7e26c1c16d0bdc029549d512dbec6526216a1e59ec68100d07007d0097dcba69dddad883d6f2a83361b4bdfe0094a3d9a2af24158643d85e
  languageName: node
  linkType: hard

"plist@npm:3.1.0, plist@npm:^3.0.4, plist@npm:^3.0.5, plist@npm:^3.1.0":
  version: 3.1.0
  resolution: "plist@npm:3.1.0"
  dependencies:
    "@xmldom/xmldom": ^0.8.8
    base64-js: ^1.5.1
    xmlbuilder: ^15.1.1
  checksum: c8ea013da8646d4c50dff82f9be39488054621cc229957621bb00add42b5d4ce3657cf58d4b10c50f7dea1a81118f825838f838baeb4e6f17fab453ecf91d424
  languageName: node
  linkType: hard

"portfinder@npm:^1.0.37":
  version: 1.0.37
  resolution: "portfinder@npm:1.0.37"
  dependencies:
    async: ^3.2.6
    debug: ^4.3.6
  checksum: 05fb2a8204ba342dfb3e152320455646fe944f0bafa1a0bec25495475133349bb803d238ae61d56845eeb40ba17428e64a1f04ee65084c4a468903246074872d
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: cfcd4f05264eee8fd184cd4897a17890561d1d473434b43ab66ad3673d9c9128981ec01e0cb1d65a52cd6b1eebfb2eae1e53e39b2e0eca86afc823ede7a4f41b
  languageName: node
  linkType: hard

"postcss-import@npm:^15.1.0":
  version: 15.1.0
  resolution: "postcss-import@npm:15.1.0"
  dependencies:
    postcss-value-parser: ^4.0.0
    read-cache: ^1.0.0
    resolve: ^1.1.7
  peerDependencies:
    postcss: ^8.0.0
  checksum: 7bd04bd8f0235429009d0022cbf00faebc885de1d017f6d12ccb1b021265882efc9302006ba700af6cab24c46bfa2f3bc590be3f9aee89d064944f171b04e2a3
  languageName: node
  linkType: hard

"postcss-js@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-js@npm:4.0.1"
  dependencies:
    camelcase-css: ^2.0.1
  peerDependencies:
    postcss: ^8.4.21
  checksum: 5c1e83efeabeb5a42676193f4357aa9c88f4dc1b3c4a0332c132fe88932b33ea58848186db117cf473049fc233a980356f67db490bd0a7832ccba9d0b3fd3491
  languageName: node
  linkType: hard

"postcss-load-config@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-load-config@npm:4.0.2"
  dependencies:
    lilconfig: ^3.0.0
    yaml: ^2.3.4
  peerDependencies:
    postcss: ">=8.0.9"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    postcss:
      optional: true
    ts-node:
      optional: true
  checksum: 7c27dd3801db4eae207a5116fed2db6b1ebb780b40c3dd62a3e57e087093a8e6a14ee17ada729fee903152d6ef4826c6339eb135bee6208e0f3140d7e8090185
  languageName: node
  linkType: hard

"postcss-nested@npm:^6.2.0":
  version: 6.2.0
  resolution: "postcss-nested@npm:6.2.0"
  dependencies:
    postcss-selector-parser: ^6.1.1
  peerDependencies:
    postcss: ^8.2.14
  checksum: 2c86ecf2d0ce68f27c87c7e24ae22dc6dd5515a89fcaf372b2627906e11f5c1f36e4a09e4c15c20fd4a23d628b3d945c35839f44496fbee9a25866258006671b
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:6.0.10":
  version: 6.0.10
  resolution: "postcss-selector-parser@npm:6.0.10"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: 46afaa60e3d1998bd7adf6caa374baf857cc58d3ff944e29459c9a9e4680a7fe41597bd5b755fc81d7c388357e9bf67c0251d047c640a09f148e13606b8a8608
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.1.1, postcss-selector-parser@npm:^6.1.2":
  version: 6.1.2
  resolution: "postcss-selector-parser@npm:6.1.2"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: ce9440fc42a5419d103f4c7c1847cb75488f3ac9cbe81093b408ee9701193a509f664b4d10a2b4d82c694ee7495e022f8f482d254f92b7ffd9ed9dea696c6f84
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 819ffab0c9d51cf0acbabf8996dffbfafbafa57afc0e4c98db88b67f2094cb44488758f06e5da95d7036f19556a4a732525e84289a425f4f6fd8e412a9d7442f
  languageName: node
  linkType: hard

"postcss@npm:^8.4.47, postcss@npm:^8.5.3":
  version: 8.5.6
  resolution: "postcss@npm:8.5.6"
  dependencies:
    nanoid: ^3.3.11
    picocolors: ^1.1.1
    source-map-js: ^1.2.1
  checksum: 20f3b5d673ffeec2b28d65436756d31ee33f65b0a8bedb3d32f556fbd5973be38c3a7fb5b959a5236c60a5db7b91b0a6b14ffaac0d717dce1b903b964ee1c1bb
  languageName: node
  linkType: hard

"prebuild-install@npm:^7.0.1, prebuild-install@npm:^7.1.1":
  version: 7.1.3
  resolution: "prebuild-install@npm:7.1.3"
  dependencies:
    detect-libc: ^2.0.0
    expand-template: ^2.0.3
    github-from-package: 0.0.0
    minimist: ^1.2.3
    mkdirp-classic: ^0.5.3
    napi-build-utils: ^2.0.0
    node-abi: ^3.3.0
    pump: ^3.0.0
    rc: ^1.2.7
    simple-get: ^4.0.0
    tar-fs: ^2.0.0
    tunnel-agent: ^0.6.0
  bin:
    prebuild-install: bin.js
  checksum: 300740ca415e9ddbf2bd363f1a6d2673cc11dd0665c5ec431bbb5bf024c2f13c56791fb939ce2b2a2c12f2d2a09c91316169e8063a80eb4482a44b8fe5b265e1
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"proc-log@npm:^2.0.1":
  version: 2.0.1
  resolution: "proc-log@npm:2.0.1"
  checksum: f6f23564ff759097db37443e6e2765af84979a703d2c52c1b9df506ee9f87caa101ba49d8fdc115c1a313ec78e37e8134704e9069e6a870f3499d98bb24c436f
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: c78b26ecef6d5cce4a7489a1e9923d7b4b1679028c8654aef0463b27f4a90b0946cd598f55799da602895c52feb085ec76381d007ab8dcceebd40b89c2f9dfe0
  languageName: node
  linkType: hard

"progress@npm:^2.0.3":
  version: 2.0.3
  resolution: "progress@npm:2.0.3"
  checksum: f67403fe7b34912148d9252cb7481266a354bd99ce82c835f79070643bb3c6583d10dbcfda4d41e04bbc1d8437e9af0fb1e1f2135727878f5308682a579429b7
  languageName: node
  linkType: hard

"promise-inflight@npm:^1.0.1":
  version: 1.0.1
  resolution: "promise-inflight@npm:1.0.1"
  checksum: 22749483091d2c594261517f4f80e05226d4d5ecc1fc917e1886929da56e22b5718b7f2a75f3807e7a7d471bc3be2907fe92e6e8f373ddf5c64bae35b5af3981
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"prop-types@npm:^15.6.2, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: ^1.4.0
    object-assign: ^4.1.1
    react-is: ^16.13.1
  checksum: c056d3f1c057cb7ff8344c645450e14f088a915d078dcda795041765047fa080d38e5d626560ccaac94a4e16e3aa15f3557c1a9a8d1174530955e992c675e459
  languageName: node
  linkType: hard

"protobufjs@npm:^6.8.8":
  version: 6.11.4
  resolution: "protobufjs@npm:6.11.4"
  dependencies:
    "@protobufjs/aspromise": ^1.1.2
    "@protobufjs/base64": ^1.1.2
    "@protobufjs/codegen": ^2.0.4
    "@protobufjs/eventemitter": ^1.1.0
    "@protobufjs/fetch": ^1.1.0
    "@protobufjs/float": ^1.0.2
    "@protobufjs/inquire": ^1.1.0
    "@protobufjs/path": ^1.1.2
    "@protobufjs/pool": ^1.1.0
    "@protobufjs/utf8": ^1.1.0
    "@types/long": ^4.0.1
    "@types/node": ">=13.7.0"
    long: ^4.0.0
  bin:
    pbjs: bin/pbjs
    pbts: bin/pbts
  checksum: b2fc6a01897b016c2a7e43a854ab4a3c57080f61be41e552235436e7a730711b8e89e47cb4ae52f0f065b5ab5d5989fc932f390337ce3a8ccf07203415700850
  languageName: node
  linkType: hard

"proxy-addr@npm:^2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: 0.2.0
    ipaddr.js: 1.9.1
  checksum: 29c6990ce9364648255454842f06f8c46fcd124d3e6d7c5066df44662de63cdc0bad032e9bf5a3d653ff72141cc7b6019873d685708ac8210c30458ad99f2b74
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.3
  resolution: "pump@npm:3.0.3"
  dependencies:
    end-of-stream: ^1.1.0
    once: ^1.3.1
  checksum: 52843fc933b838c0330f588388115a1b28ef2a5ffa7774709b142e35431e8ab0c2edec90de3fa34ebb72d59fef854f151eea7dfc211b6dcf586b384556bd2f39
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: bb0a0ceedca4c3c57a9b981b90601579058903c62be23c5e8e843d2c2d4148a3ecf029d5133486fb0e1822b098ba8bba09e89d6b21742d02fa26bda6441a6fb2
  languageName: node
  linkType: hard

"qs@npm:^6.14.0":
  version: 6.14.0
  resolution: "qs@npm:6.14.0"
  dependencies:
    side-channel: ^1.1.0
  checksum: 189b52ad4e9a0da1a16aff4c58b2a554a8dad9bd7e287c7da7446059b49ca2e33a49e570480e8be406b87fccebf134f51c373cbce36c8c83859efa0c9b71d635
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"quick-lru@npm:^5.1.1":
  version: 5.1.1
  resolution: "quick-lru@npm:5.1.1"
  checksum: a516faa25574be7947969883e6068dbe4aa19e8ef8e8e0fd96cddd6d36485e9106d85c0041a27153286b0770b381328f4072aa40d3b18a19f5f7d2b78b94b5ed
  languageName: node
  linkType: hard

"range-parser@npm:^1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 0a268d4fea508661cf5743dfe3d5f47ce214fd6b7dec1de0da4d669dd4ef3d2144468ebe4179049eff253d9d27e719c88dae55be64f954e80135a0cada804ec9
  languageName: node
  linkType: hard

"raw-body@npm:^3.0.0":
  version: 3.0.0
  resolution: "raw-body@npm:3.0.0"
  dependencies:
    bytes: 3.1.2
    http-errors: 2.0.0
    iconv-lite: 0.6.3
    unpipe: 1.0.0
  checksum: 25b7cf7964183db322e819050d758a5abd0f22c51e9f37884ea44a9ed6855a1fb61f8caa8ec5b61d07e69f54db43dbbc08ad98ef84556696d6aa806be247af0e
  languageName: node
  linkType: hard

"rc@npm:^1.2.7":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: ^0.6.0
    ini: ~1.3.0
    minimist: ^1.2.0
    strip-json-comments: ~2.0.1
  bin:
    rc: ./cli.js
  checksum: 2e26e052f8be2abd64e6d1dabfbd7be03f80ec18ccbc49562d31f617d0015fbdbcf0f9eed30346ea6ab789e0fdfe4337f033f8016efdbee0df5354751842080e
  languageName: node
  linkType: hard

"react-day-picker@npm:^8.10.1":
  version: 8.10.1
  resolution: "react-day-picker@npm:8.10.1"
  peerDependencies:
    date-fns: ^2.28.0 || ^3.0.0
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: daf7fb5aad1e355df9149e954fafc2cd40ceb90de799db4f36ef86d24729aaecfdf022fa9e63f24d093d81dc2beb60f65ae796741dff0f7305a2af80b333fb4c
  languageName: node
  linkType: hard

"react-dom@npm:^19.1.0":
  version: 19.1.0
  resolution: "react-dom@npm:19.1.0"
  dependencies:
    scheduler: ^0.26.0
  peerDependencies:
    react: ^19.1.0
  checksum: 1d154b6543467095ac269e61ca59db546f34ef76bcdeb90f2dad41d682cd210aae492e70c85010ed5d0a2caea225e9a55139ebc1a615ee85bf197d7f99678cdf
  languageName: node
  linkType: hard

"react-hook-form@npm:^7.53.0":
  version: 7.60.0
  resolution: "react-hook-form@npm:7.60.0"
  peerDependencies:
    react: ^16.8.0 || ^17 || ^18 || ^19
  checksum: 8700c2932604fe2b3fbdd47a459bec58fb5a2003a336b418b98d8771d1a6ec472531c6758188e23329f86413f736cc5a1c1a84a59341f5b3b9dd4f9fd704fb18
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react-is@npm:^18.3.1":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: e20fe84c86ff172fc8d898251b7cc2c43645d108bf96d0b8edf39b98f9a2cae97b40520ee7ed8ee0085ccc94736c4886294456033304151c3f94978cec03df21
  languageName: node
  linkType: hard

"react-refresh@npm:^0.17.0":
  version: 0.17.0
  resolution: "react-refresh@npm:0.17.0"
  checksum: e9d23a70543edde879263976d7909cd30c6f698fa372a1240142cf7c8bf99e0396378b9c07c2d39c3a10261d7ba07dc49f990cd8f1ac7b88952e99040a0be5e9
  languageName: node
  linkType: hard

"react-remove-scroll-bar@npm:^2.3.7":
  version: 2.3.8
  resolution: "react-remove-scroll-bar@npm:2.3.8"
  dependencies:
    react-style-singleton: ^2.2.2
    tslib: ^2.0.0
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: c4663247f689dbe51c370836edf735487f6d8796acb7f15b09e8a1c14e84c7997360e8e3d54de2bc9c0e782fed2b2c4127d15b4053e4d2cf26839e809e57605f
  languageName: node
  linkType: hard

"react-remove-scroll@npm:^2.6.3":
  version: 2.7.1
  resolution: "react-remove-scroll@npm:2.7.1"
  dependencies:
    react-remove-scroll-bar: ^2.3.7
    react-style-singleton: ^2.2.3
    tslib: ^2.1.0
    use-callback-ref: ^1.3.3
    use-sidecar: ^1.1.3
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: c8b1988d473ca0b4911a0a42f09dc7806d5db998c3ec938ae2791a5f82d807c2cdebb78a1c58a0bab62a83112528dda2f20d509d0e048fe281b9dfc027c39763
  languageName: node
  linkType: hard

"react-resizable-panels@npm:^2.1.3":
  version: 2.1.9
  resolution: "react-resizable-panels@npm:2.1.9"
  peerDependencies:
    react: ^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    react-dom: ^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  checksum: a807d6cd572c1323e21cea896cb27ae1544219dd813c746607ecb05d4f3a81a166921f86720481ac07b7e37e7812e0a24abbb7a3237b8e11cf69d29f7022a2cb
  languageName: node
  linkType: hard

"react-router-dom@npm:^6.26.2":
  version: 6.30.1
  resolution: "react-router-dom@npm:6.30.1"
  dependencies:
    "@remix-run/router": 1.23.0
    react-router: 6.30.1
  peerDependencies:
    react: ">=16.8"
    react-dom: ">=16.8"
  checksum: 89949352a702c1d83d11349900b6852250499e2bc5256d594d4895449b4769e79f7179955c86fecae1f6c7e672f21d49f5b7e2fede39bbb3dd45e6de4f129ccb
  languageName: node
  linkType: hard

"react-router@npm:6.30.1":
  version: 6.30.1
  resolution: "react-router@npm:6.30.1"
  dependencies:
    "@remix-run/router": 1.23.0
  peerDependencies:
    react: ">=16.8"
  checksum: cab6c2ef3e4bc2b7d92c17f9de30d56f169ffe10a082de52a5842a335289d798aec91ff7bc39dfe7d73f6c50ae56e9e5d1597e8edfcdd80910b93383138a7525
  languageName: node
  linkType: hard

"react-smooth@npm:^4.0.4":
  version: 4.0.4
  resolution: "react-smooth@npm:4.0.4"
  dependencies:
    fast-equals: ^5.0.1
    prop-types: ^15.8.1
    react-transition-group: ^4.4.5
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 909305d40bae79a011ff21a10c4bc7ddadc87ac5ff093b4a5f827f730f093ec4e044c4330688d29b3ad2db83aab8997c3bb1bae550a9c66de74521b8ed52cc53
  languageName: node
  linkType: hard

"react-style-singleton@npm:^2.2.2, react-style-singleton@npm:^2.2.3":
  version: 2.2.3
  resolution: "react-style-singleton@npm:2.2.3"
  dependencies:
    get-nonce: ^1.0.0
    tslib: ^2.0.0
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: a7b0bf493c9231065ebafa84c4237aed997c746c561196121b7de82fe155a5355b372db5070a3ac9fe980cf7f60dc0f1e8cf6402a2aa5b2957392932ccf76e76
  languageName: node
  linkType: hard

"react-toastify@npm:^11.0.5":
  version: 11.0.5
  resolution: "react-toastify@npm:11.0.5"
  dependencies:
    clsx: ^2.1.1
  peerDependencies:
    react: ^18 || ^19
    react-dom: ^18 || ^19
  checksum: e2150aabe6a2b70d1d06bd6d4ef14d14e7ff38703980339660a34ef2251df206c4850dbf52a483aacf22265b41a8af0e27210ce20bf10ea7df4600e492a58352
  languageName: node
  linkType: hard

"react-transition-group@npm:^4.4.5":
  version: 4.4.5
  resolution: "react-transition-group@npm:4.4.5"
  dependencies:
    "@babel/runtime": ^7.5.5
    dom-helpers: ^5.0.1
    loose-envify: ^1.4.0
    prop-types: ^15.6.2
  peerDependencies:
    react: ">=16.6.0"
    react-dom: ">=16.6.0"
  checksum: 75602840106aa9c6545149d6d7ae1502fb7b7abadcce70a6954c4b64a438ff1cd16fc77a0a1e5197cdd72da398f39eb929ea06f9005c45b132ed34e056ebdeb1
  languageName: node
  linkType: hard

"react@npm:^19.1.0":
  version: 19.1.0
  resolution: "react@npm:19.1.0"
  checksum: c0905f8cfb878b0543a5522727e5ed79c67c8111dc16ceee135b7fe19dce77b2c1c19293513061a8934e721292bfc1517e0487e262d1906f306bdf95fa54d02f
  languageName: node
  linkType: hard

"read-binary-file-arch@npm:^1.0.6":
  version: 1.0.6
  resolution: "read-binary-file-arch@npm:1.0.6"
  dependencies:
    debug: ^4.3.4
  bin:
    read-binary-file-arch: cli.js
  checksum: 7a25894816ff9caf5c27886b0aea1740bfab29483443a2859e5a0dc367c56ee9489f3cdba9da676a6d5913d3e421e71c6afbdbcfb636714ff49d93d152c72ba5
  languageName: node
  linkType: hard

"read-cache@npm:^1.0.0":
  version: 1.0.0
  resolution: "read-cache@npm:1.0.0"
  dependencies:
    pify: ^2.3.0
  checksum: cffc728b9ede1e0667399903f9ecaf3789888b041c46ca53382fa3a06303e5132774dc0a96d0c16aa702dbac1ea0833d5a868d414f5ab2af1e1438e19e6657c6
  languageName: node
  linkType: hard

"read-pkg@npm:^3.0.0":
  version: 3.0.0
  resolution: "read-pkg@npm:3.0.0"
  dependencies:
    load-json-file: ^4.0.0
    normalize-package-data: ^2.3.2
    path-type: ^3.0.0
  checksum: 398903ebae6c7e9965419a1062924436cc0b6f516c42c4679a90290d2f87448ed8f977e7aa2dbba4aa1ac09248628c43e493ac25b2bc76640e946035200e34c6
  languageName: node
  linkType: hard

"readable-stream@npm:^3.1.1, readable-stream@npm:^3.4.0, readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: bdcbe6c22e846b6af075e32cf8f4751c2576238c5043169a1c221c92ee2878458a816a4ea33f4c67623c0b6827c8a400409bfb3cf0bf3381392d0b1dfb52ac8d
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"recharts-scale@npm:^0.4.4":
  version: 0.4.5
  resolution: "recharts-scale@npm:0.4.5"
  dependencies:
    decimal.js-light: ^2.4.1
  checksum: e970377190a610e684a32c7461c7684ac3603c2e0ac0020bbba1eea9d099b38138143a8e80bf769bb49c0b7cecf22a2f5c6854885efed2d56f4540d4aa7052bd
  languageName: node
  linkType: hard

"recharts@npm:^2.15.3":
  version: 2.15.4
  resolution: "recharts@npm:2.15.4"
  dependencies:
    clsx: ^2.0.0
    eventemitter3: ^4.0.1
    lodash: ^4.17.21
    react-is: ^18.3.1
    react-smooth: ^4.0.4
    recharts-scale: ^0.4.4
    tiny-invariant: ^1.3.1
    victory-vendor: ^36.6.8
  peerDependencies:
    react: ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 7d7d89da8875a7aa3e157d4caf6c8fdbaf22fcf111c70cd67d8259dca6b7f878287b6f2db5af9b2daf6a85046de06ca2dcf18cb7cd7bb5a6ddd3639690c5d888
  languageName: node
  linkType: hard

"reflect-metadata@npm:^0.2.2":
  version: 0.2.2
  resolution: "reflect-metadata@npm:0.2.2"
  checksum: a66c7b583e4efdd8f3c3124fbff33da2d0c86d8280617516308b32b2159af7a3698c961db3246387f56f6316b1d33a608f39bb2b49d813316dfc58f6d3bf3210
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.9
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.7
    get-proto: ^1.0.1
    which-builtin-type: ^1.2.1
  checksum: ccc5debeb66125e276ae73909cecb27e47c35d9bb79d9cc8d8d055f008c58010ab8cb401299786e505e4aab733a64cba9daf5f312a58e96a43df66adad221870
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.4":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-errors: ^1.3.0
    get-proto: ^1.0.1
    gopd: ^1.2.0
    set-function-name: ^2.0.2
  checksum: 18cb667e56cb328d2dda569d7f04e3ea78f2683135b866d606538cf7b1d4271f7f749f09608c877527799e6cf350e531368f3c7a20ccd1bb41048a48926bdeeb
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"resedit@npm:^1.7.0":
  version: 1.7.2
  resolution: "resedit@npm:1.7.2"
  dependencies:
    pe-library: ^0.4.1
  checksum: 53ee7ddd19c93005f4a71525088f64bee783a6e8ba3c3a84fc34fae334227bd498e5c8a2dc2356558435b848be972c0fd54bda1e5500baa36c9d2a045925ed50
  languageName: node
  linkType: hard

"resolve-alpn@npm:^1.0.0":
  version: 1.2.1
  resolution: "resolve-alpn@npm:1.2.1"
  checksum: f558071fcb2c60b04054c99aebd572a2af97ef64128d59bef7ab73bd50d896a222a056de40ffc545b633d99b304c259ea9d0c06830d5c867c34f0bfa60b8eae0
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve@npm:^1.1.7, resolve@npm:^1.10.0, resolve@npm:^1.22.8":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: ab7a32ff4046fcd7c6fdd525b24a7527847d03c3650c733b909b01b757f92eb23510afa9cc3e9bf3f26a3e073b48c88c706dfd4c1d2fb4a16a96b73b6328ddcf
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.1.7#~builtin<compat/resolve>, resolve@patch:resolve@^1.10.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.8#~builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#~builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 8aac1e4e4628bd00bf4b94b23de137dd3fe44097a8d528fd66db74484be929936e20c696e1a3edf4488f37e14180b73df6f600992baea3e089e8674291f16c9d
  languageName: node
  linkType: hard

"responselike@npm:^2.0.0":
  version: 2.0.1
  resolution: "responselike@npm:2.0.1"
  dependencies:
    lowercase-keys: ^2.0.0
  checksum: b122535466e9c97b55e69c7f18e2be0ce3823c5d47ee8de0d9c0b114aa55741c6db8bfbfce3766a94d1272e61bfb1ebf0a15e9310ac5629fbb7446a861b4fd3a
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: ^5.1.0
    signal-exit: ^3.0.2
  checksum: f877dd8741796b909f2a82454ec111afb84eb45890eb49ac947d87991379406b3b83ff9673a46012fca0d7844bb989f45cc5b788254cf1a39b6b5a9659de0630
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 64cb3142ac5e9ad689aca289585cb41d22521f4571f73e9488af39f6b1bd62f0cbb3d65e2ecc768ec6494052523f473f1eb4b55c3e9014b3590c17fc6a03e22a
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"roarr@npm:^2.15.3":
  version: 2.15.4
  resolution: "roarr@npm:2.15.4"
  dependencies:
    boolean: ^3.0.1
    detect-node: ^2.0.4
    globalthis: ^1.0.1
    json-stringify-safe: ^5.0.1
    semver-compare: ^1.0.0
    sprintf-js: ^1.1.2
  checksum: 682e28d5491e3ae99728a35ba188f4f0ccb6347dbd492f95dc9f4bfdfe8ee63d8203ad234766ee2db88c8d7a300714304976eb095ce5c9366fe586c03a21586c
  languageName: node
  linkType: hard

"rollup@npm:@rollup/wasm-node@*":
  version: 4.44.2
  resolution: "@rollup/wasm-node@npm:4.44.2"
  dependencies:
    "@types/estree": 1.0.8
    fsevents: ~2.3.2
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 7b8307eb660892c7f7204b0e27edd9e07b8247a0431be9966c9b90db83e5801e126844d1f25d25d772f4faa4837dea588123e8efde8bcda91d3f11483efc641a
  languageName: node
  linkType: hard

"router@npm:^2.2.0":
  version: 2.2.0
  resolution: "router@npm:2.2.0"
  dependencies:
    debug: ^4.4.0
    depd: ^2.0.0
    is-promise: ^4.0.0
    parseurl: ^1.3.3
    path-to-regexp: ^8.0.0
  checksum: 4c3bec8011ed10bb07d1ee860bc715f245fff0fdff991d8319741d2932d89c3fe0a56766b4fa78e95444bc323fd2538e09c8e43bfbd442c2a7fab67456df7fa5
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    get-intrinsic: ^1.2.6
    has-symbols: ^1.1.0
    isarray: ^2.0.5
  checksum: 00f6a68140e67e813f3ad5e73e6dedcf3e42a9fa01f04d44b0d3f7b1f4b257af876832a9bfc82ac76f307e8a6cc652e3cf95876048a26cbec451847cf6ae3707
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:^5.0.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    isarray: ^2.0.5
  checksum: 8c11cbee6dc8ff5cc0f3d95eef7052e43494591384015902e4292aef4ae9e539908288520ed97179cee17d6ffb450fe5f05a46ce7a1749685f7524fd568ab5db
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    is-regex: ^1.2.1
  checksum: 3c809abeb81977c9ed6c869c83aca6873ea0f3ab0f806b8edbba5582d51713f8a6e9757d24d2b4b088f563801475ea946c8e77e7713e8c65cdd02305b6caedab
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"sanitize-filename@npm:^1.6.3":
  version: 1.6.3
  resolution: "sanitize-filename@npm:1.6.3"
  dependencies:
    truncate-utf8-bytes: ^1.0.0
  checksum: aa733c012b7823cf65730603cf3b503c641cee6b239771d3164ca482f22d81a50e434a713938d994071db18e4202625669cc56bccc9d13d818b4c983b5f47fde
  languageName: node
  linkType: hard

"sax@npm:^1.2.4":
  version: 1.4.1
  resolution: "sax@npm:1.4.1"
  checksum: 3ad64df16b743f0f2eb7c38ced9692a6d924f1cd07bbe45c39576c2cf50de8290d9d04e7b2228f924c7d05fecc4ec5cf651423278e0c7b63d260c387ef3af84a
  languageName: node
  linkType: hard

"scheduler@npm:^0.26.0":
  version: 0.26.0
  resolution: "scheduler@npm:0.26.0"
  checksum: c63a9f1c0e5089b537231cff6c11f75455b5c8625ae09535c1d7cd0a1b0c77ceecdd9f1074e5e063da5d8dc11e73e8033dcac3361791088be08a6e60c0283ed9
  languageName: node
  linkType: hard

"semver-compare@npm:^1.0.0":
  version: 1.0.0
  resolution: "semver-compare@npm:1.0.0"
  checksum: dd1d7e2909744cf2cf71864ac718efc990297f9de2913b68e41a214319e70174b1d1793ac16e31183b128c2b9812541300cb324db8168e6cf6b570703b171c68
  languageName: node
  linkType: hard

"semver@npm:2 || 3 || 4 || 5, semver@npm:^5.5.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: fb4ab5e0dd1c22ce0c937ea390b4a822147a9c53dbd2a9a0132f12fe382902beef4fbf12cf51bb955248d8d15874ce8cd89532569756384f994309825f10b686
  languageName: node
  linkType: hard

"semver@npm:^6.2.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: ae47d06de28836adb9d3e25f22a92943477371292d9b665fb023fae278d345d508ca1958232af086d85e0155aee22e313e100971898bbb8d5d89b8b1d4054ca2
  languageName: node
  linkType: hard

"semver@npm:^7.3.2, semver@npm:^7.3.5, semver@npm:^7.3.8, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.0, semver@npm:^7.6.3, semver@npm:^7.7.2":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: dd94ba8f1cbc903d8eeb4dd8bf19f46b3deb14262b6717d0de3c804b594058ae785ef2e4b46c5c3b58733c99c83339068203002f9e37cfe44f7e2cc5e3d2f621
  languageName: node
  linkType: hard

"send@npm:^1.1.0, send@npm:^1.2.0":
  version: 1.2.0
  resolution: "send@npm:1.2.0"
  dependencies:
    debug: ^4.3.5
    encodeurl: ^2.0.0
    escape-html: ^1.0.3
    etag: ^1.8.1
    fresh: ^2.0.0
    http-errors: ^2.0.0
    mime-types: ^3.0.1
    ms: ^2.1.3
    on-finished: ^2.4.1
    range-parser: ^1.2.1
    statuses: ^2.0.1
  checksum: 7557ee6c1c257a1c53b402b4fba8ed88c95800b08abe085fc79e0824869274f213491be2efb2df3de228c70e4d40ce2019e5f77b58c42adb97149135420c3f34
  languageName: node
  linkType: hard

"serialize-error@npm:^7.0.1":
  version: 7.0.1
  resolution: "serialize-error@npm:7.0.1"
  dependencies:
    type-fest: ^0.13.1
  checksum: e0aba4dca2fc9fe74ae1baf38dbd99190e1945445a241ba646290f2176cdb2032281a76443b02ccf0caf30da5657d510746506368889a593b9835a497fc0732e
  languageName: node
  linkType: hard

"serve-static@npm:^2.2.0":
  version: 2.2.0
  resolution: "serve-static@npm:2.2.0"
  dependencies:
    encodeurl: ^2.0.0
    escape-html: ^1.0.3
    parseurl: ^1.3.3
    send: ^1.2.0
  checksum: 74f39e88f0444aa6732aae3b9597739c47552adecdc83fa32aa42555e76f1daad480d791af73894655c27a2d378275a461e691cead33fb35d8b976f1e2d24665
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 6e65a05f7cf7ebdf8b7c75b101e18c0b7e3dff4940d480efed8aad3a36a4005140b660fa1d804cb8bce911cac290441dc728084a30504d3516ac2ff7ad607b02
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.4
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.2
  checksum: a8248bdacdf84cb0fab4637774d9fb3c7a8e6089866d04c817583ff48e14149c87044ce683d7f50759a8c50fb87c7a7e173535b06169c87ef76f5fb276dfff72
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    functions-have-names: ^1.2.3
    has-property-descriptors: ^1.0.2
  checksum: d6229a71527fd0404399fc6227e0ff0652800362510822a291925c9d7b48a1ca1a468b11b281471c34cd5a2da0db4f5d7ff315a61d26655e77f6e971e6d0c80f
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
  checksum: ec27cbbe334598547e99024403e96da32aca3e530583e4dba7f5db1c43cbc4affa9adfbd77c7b2c210b9b8b2e7b2e600bad2a6c44fd62e804d8233f96bbb62f4
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: be18cbbf70e7d8097c97f713a2e76edf84e87299b40d085c6bf8b65314e994cc15e2e317727342fa6996e38e1f52c59720b53fe621e2eb593a6847bf0356db89
  languageName: node
  linkType: hard

"sharp@npm:^0.32.0":
  version: 0.32.6
  resolution: "sharp@npm:0.32.6"
  dependencies:
    color: ^4.2.3
    detect-libc: ^2.0.2
    node-addon-api: ^6.1.0
    node-gyp: latest
    prebuild-install: ^7.1.1
    semver: ^7.5.4
    simple-get: ^4.0.1
    tar-fs: ^3.0.4
    tunnel-agent: ^0.6.0
  checksum: 0cca1d16b1920800c0e22d27bc6305f4c67c9ebe44f67daceb30bf645ae39e7fb7dfbd7f5d6cd9f9eebfddd87ac3f7e2695f4eb906d19b7a775286238e6a29fc
  languageName: node
  linkType: hard

"sharp@npm:^0.34.2":
  version: 0.34.2
  resolution: "sharp@npm:0.34.2"
  dependencies:
    "@img/sharp-darwin-arm64": 0.34.2
    "@img/sharp-darwin-x64": 0.34.2
    "@img/sharp-libvips-darwin-arm64": 1.1.0
    "@img/sharp-libvips-darwin-x64": 1.1.0
    "@img/sharp-libvips-linux-arm": 1.1.0
    "@img/sharp-libvips-linux-arm64": 1.1.0
    "@img/sharp-libvips-linux-ppc64": 1.1.0
    "@img/sharp-libvips-linux-s390x": 1.1.0
    "@img/sharp-libvips-linux-x64": 1.1.0
    "@img/sharp-libvips-linuxmusl-arm64": 1.1.0
    "@img/sharp-libvips-linuxmusl-x64": 1.1.0
    "@img/sharp-linux-arm": 0.34.2
    "@img/sharp-linux-arm64": 0.34.2
    "@img/sharp-linux-s390x": 0.34.2
    "@img/sharp-linux-x64": 0.34.2
    "@img/sharp-linuxmusl-arm64": 0.34.2
    "@img/sharp-linuxmusl-x64": 0.34.2
    "@img/sharp-wasm32": 0.34.2
    "@img/sharp-win32-arm64": 0.34.2
    "@img/sharp-win32-ia32": 0.34.2
    "@img/sharp-win32-x64": 0.34.2
    color: ^4.2.3
    detect-libc: ^2.0.4
    semver: ^7.7.2
  dependenciesMeta:
    "@img/sharp-darwin-arm64":
      optional: true
    "@img/sharp-darwin-x64":
      optional: true
    "@img/sharp-libvips-darwin-arm64":
      optional: true
    "@img/sharp-libvips-darwin-x64":
      optional: true
    "@img/sharp-libvips-linux-arm":
      optional: true
    "@img/sharp-libvips-linux-arm64":
      optional: true
    "@img/sharp-libvips-linux-ppc64":
      optional: true
    "@img/sharp-libvips-linux-s390x":
      optional: true
    "@img/sharp-libvips-linux-x64":
      optional: true
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
    "@img/sharp-linux-arm":
      optional: true
    "@img/sharp-linux-arm64":
      optional: true
    "@img/sharp-linux-s390x":
      optional: true
    "@img/sharp-linux-x64":
      optional: true
    "@img/sharp-linuxmusl-arm64":
      optional: true
    "@img/sharp-linuxmusl-x64":
      optional: true
    "@img/sharp-wasm32":
      optional: true
    "@img/sharp-win32-arm64":
      optional: true
    "@img/sharp-win32-ia32":
      optional: true
    "@img/sharp-win32-x64":
      optional: true
  checksum: beb34afe75cc6492fc7e6331efebfa11a0f92bf0f54ac850bf4c93ab48ab4152103cf096a892802bacca7c8102b721312b098bfdda16a4bf6c95716dabb28a16
  languageName: node
  linkType: hard

"shebang-command@npm:^1.2.0":
  version: 1.2.0
  resolution: "shebang-command@npm:1.2.0"
  dependencies:
    shebang-regex: ^1.0.0
  checksum: 9eed1750301e622961ba5d588af2212505e96770ec376a37ab678f965795e995ade7ed44910f5d3d3cb5e10165a1847f52d3348c64e146b8be922f7707958908
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "shebang-regex@npm:1.0.0"
  checksum: 404c5a752cd40f94591dfd9346da40a735a05139dac890ffc229afba610854d8799aaa52f87f7e0c94c5007f2c6af55bdcaeb584b56691926c5eaf41dc8f1372
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"shell-quote@npm:^1.6.1":
  version: 1.8.3
  resolution: "shell-quote@npm:1.8.3"
  checksum: 550dd84e677f8915eb013d43689c80bb114860649ec5298eb978f40b8f3d4bc4ccb072b82c094eb3548dc587144bb3965a8676f0d685c1cf4c40b5dc27166242
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
  checksum: 603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
  checksum: 42501371cdf71f4ccbbc9c9e2eb00aaaab80a4c1c429d5e8da713fd4d39ef3b8d4a4b37ed4f275798a65260a551a7131fd87fe67e922dba4ac18586d6aab8b06
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
    side-channel-map: ^1.0.1
  checksum: a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
    side-channel-list: ^1.0.0
    side-channel-map: ^1.0.1
    side-channel-weakmap: ^1.0.2
  checksum: bf73d6d6682034603eb8e99c63b50155017ed78a522d27c2acec0388a792c3ede3238b878b953a08157093b85d05797217d270b7666ba1f111345fbe933380ff
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"simple-concat@npm:^1.0.0":
  version: 1.0.1
  resolution: "simple-concat@npm:1.0.1"
  checksum: 4d211042cc3d73a718c21ac6c4e7d7a0363e184be6a5ad25c8a1502e49df6d0a0253979e3d50dbdd3f60ef6c6c58d756b5d66ac1e05cda9cacd2e9fc59e3876a
  languageName: node
  linkType: hard

"simple-get@npm:^4.0.0, simple-get@npm:^4.0.1":
  version: 4.0.1
  resolution: "simple-get@npm:4.0.1"
  dependencies:
    decompress-response: ^6.0.0
    once: ^1.3.1
    simple-concat: ^1.0.0
  checksum: e4132fd27cf7af230d853fa45c1b8ce900cb430dd0a3c6d3829649fe4f2b26574c803698076c4006450efb0fad2ba8c5455fbb5755d4b0a5ec42d4f12b31d27e
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: ^0.3.1
  checksum: a7f3f2ab5c76c4472d5c578df892e857323e452d9f392e1b5cf74b74db66e6294a1e1b8b390b519fa1b96b5b613f2a37db6cffef52c3f1f8f3c5ea64eb2d54c0
  languageName: node
  linkType: hard

"simple-update-notifier@npm:2.0.0":
  version: 2.0.0
  resolution: "simple-update-notifier@npm:2.0.0"
  dependencies:
    semver: ^7.5.3
  checksum: 9ba00d38ce6a29682f64a46213834e4eb01634c2f52c813a9a7b8873ca49cdbb703696f3290f3b27dc067de6d9418b0b84bef22c3eb074acf352529b2d6c27fd
  languageName: node
  linkType: hard

"slice-ansi@npm:^3.0.0":
  version: 3.0.0
  resolution: "slice-ansi@npm:3.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    astral-regex: ^2.0.0
    is-fullwidth-code-point: ^3.0.0
  checksum: 5ec6d022d12e016347e9e3e98a7eb2a592213a43a65f1b61b74d2c78288da0aded781f665807a9f3876b9daa9ad94f64f77d7633a0458876c3a4fdc4eb223f24
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.0.2, smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^6.0.0":
  version: 6.2.1
  resolution: "socks-proxy-agent@npm:6.2.1"
  dependencies:
    agent-base: ^6.0.2
    debug: ^4.3.3
    socks: ^2.6.2
  checksum: 9ca089d489e5ee84af06741135c4b0d2022977dad27ac8d649478a114cdce87849e8d82b7c22b51501a4116e231241592946fc7fae0afc93b65030ee57084f58
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^7.0.0":
  version: 7.0.0
  resolution: "socks-proxy-agent@npm:7.0.0"
  dependencies:
    agent-base: ^6.0.2
    debug: ^4.3.3
    socks: ^2.6.2
  checksum: 720554370154cbc979e2e9ce6a6ec6ced205d02757d8f5d93fe95adae454fc187a5cbfc6b022afab850a5ce9b4c7d73e0f98e381879cf45f66317a4895953846
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: ^7.1.2
    debug: ^4.3.4
    socks: ^2.8.3
  checksum: b4fbcdb7ad2d6eec445926e255a1fb95c975db0020543fbac8dfa6c47aecc6b3b619b7fb9c60a3f82c9b2969912a5e7e174a056ae4d98cb5322f3524d6036e1d
  languageName: node
  linkType: hard

"socks@npm:^2.6.2, socks@npm:^2.8.3":
  version: 2.8.5
  resolution: "socks@npm:2.8.5"
  dependencies:
    ip-address: ^9.0.5
    smart-buffer: ^4.2.0
  checksum: d39a77a8c91cfacafc75c67dba45925eccfd884a8a4a68dcda6fb9ab7f37de6e250bb6db3721e8a16a066a8e1ebe872d4affc26f3eb763f4befedcc7b733b7ed
  languageName: node
  linkType: hard

"sonner@npm:^1.5.0":
  version: 1.7.4
  resolution: "sonner@npm:1.7.4"
  peerDependencies:
    react: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  checksum: f6388f0ccb64e0d6973991e24c23515a377c9c45b2da304f077cadac8534d03b6f90005187ac89a69086cdb4c4fd68ba682bf1119c73ca6674ead557786e746f
  languageName: node
  linkType: hard

"source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 4eb0cd997cdf228bc253bcaff9340afeb706176e64868ecd20efbe6efea931465f43955612346d6b7318789e5265bdc419bc7669c1cebe3db0eb255f57efa76b
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.19":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 43e98d700d79af1d36f859bdb7318e601dfc918c7ba2e98456118ebc4c4872b327773e5a1df09b0524e9e5063bb18f0934538eace60cca2710d1fa687645d137
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"spdx-correct@npm:^3.0.0":
  version: 3.2.0
  resolution: "spdx-correct@npm:3.2.0"
  dependencies:
    spdx-expression-parse: ^3.0.0
    spdx-license-ids: ^3.0.0
  checksum: e9ae98d22f69c88e7aff5b8778dc01c361ef635580e82d29e5c60a6533cc8f4d820803e67d7432581af0cc4fb49973125076ee3b90df191d153e223c004193b2
  languageName: node
  linkType: hard

"spdx-exceptions@npm:^2.1.0":
  version: 2.5.0
  resolution: "spdx-exceptions@npm:2.5.0"
  checksum: bb127d6e2532de65b912f7c99fc66097cdea7d64c10d3ec9b5e96524dbbd7d20e01cba818a6ddb2ae75e62bb0c63d5e277a7e555a85cbc8ab40044984fa4ae15
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^3.0.0":
  version: 3.0.1
  resolution: "spdx-expression-parse@npm:3.0.1"
  dependencies:
    spdx-exceptions: ^2.1.0
    spdx-license-ids: ^3.0.0
  checksum: a1c6e104a2cbada7a593eaa9f430bd5e148ef5290d4c0409899855ce8b1c39652bcc88a725259491a82601159d6dc790bedefc9016c7472f7de8de7361f8ccde
  languageName: node
  linkType: hard

"spdx-license-ids@npm:^3.0.0":
  version: 3.0.21
  resolution: "spdx-license-ids@npm:3.0.21"
  checksum: 681dfe26d250f48cc725c9118adf1eb0a175e3c298cd8553c039bfae37ed21bea30a27bc02dbb99b4a0d3a25c644c5dda952090e11ef4b3093f6ec7db4b93b58
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.2, sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: a3fdac7b49643875b70864a9d9b469d87a40dfeaf5d34d9d0c5b1cda5fd7d065531fcb43c76357d62254c57184a7b151954156563a4d6a747015cfb41021cad0
  languageName: node
  linkType: hard

"sqlite3@npm:^5.1.7":
  version: 5.1.7
  resolution: "sqlite3@npm:5.1.7"
  dependencies:
    bindings: ^1.5.0
    node-addon-api: ^7.0.0
    node-gyp: 8.x
    prebuild-install: ^7.1.1
    tar: ^6.1.11
  peerDependencies:
    node-gyp: 8.x
  dependenciesMeta:
    node-gyp:
      optional: true
  peerDependenciesMeta:
    node-gyp:
      optional: true
  checksum: 37e387b74e34aea3d0fc5cea76e14de3139e4bdbf6574ff6ca876c3b5e36e859b278e99922c179c14337cb0d487d8da8dbbaaf7d63fbab5928dc134a9d5db262
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: ^7.0.3
  checksum: ef4b6b0ae47b4a69896f5f1c4375f953b9435388c053c36d27998bc3d73e046969ccde61ab659e679142971a0b08e50478a1228f62edb994105b280f17900c98
  languageName: node
  linkType: hard

"ssri@npm:^8.0.0, ssri@npm:^8.0.1":
  version: 8.0.1
  resolution: "ssri@npm:8.0.1"
  dependencies:
    minipass: ^3.1.1
  checksum: bc447f5af814fa9713aa201ec2522208ae0f4d8f3bda7a1f445a797c7b929a02720436ff7c478fb5edc4045adb02b1b88d2341b436a80798734e2494f1067b36
  languageName: node
  linkType: hard

"ssri@npm:^9.0.0":
  version: 9.0.1
  resolution: "ssri@npm:9.0.1"
  dependencies:
    minipass: ^3.1.1
  checksum: fb58f5e46b6923ae67b87ad5ef1c5ab6d427a17db0bead84570c2df3cd50b4ceb880ebdba2d60726588272890bae842a744e1ecce5bd2a2a582fccd5068309eb
  languageName: node
  linkType: hard

"stat-mode@npm:^1.0.0":
  version: 1.0.0
  resolution: "stat-mode@npm:1.0.0"
  checksum: f9daea2dba41e1dffae5543a8af087ec8b56ff6ae1c729b5373b4f528e214f53260108dab522d2660cca2215dc3e61f164920a82136ad142dab50b3faa6f6090
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"statuses@npm:^2.0.1":
  version: 2.0.2
  resolution: "statuses@npm:2.0.2"
  checksum: 6927feb50c2a75b2a4caab2c565491f7a93ad3d8dbad7b1398d52359e9243a20e2ebe35e33726dee945125ef7a515e9097d8a1b910ba2bbd818265a2f6c39879
  languageName: node
  linkType: hard

"stop-iteration-iterator@npm:^1.1.0":
  version: 1.1.0
  resolution: "stop-iteration-iterator@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    internal-slot: ^1.1.0
  checksum: be944489d8829fb3bdec1a1cc4a2142c6b6eb317305eeace1ece978d286d6997778afa1ae8cb3bd70e2b274b9aa8c69f93febb1e15b94b1359b11058f9d3c3a1
  languageName: node
  linkType: hard

"streamx@npm:^2.15.0, streamx@npm:^2.21.0":
  version: 2.22.1
  resolution: "streamx@npm:2.22.1"
  dependencies:
    bare-events: ^2.2.0
    fast-fifo: ^1.3.2
    text-decoder: ^1.1.0
  dependenciesMeta:
    bare-events:
      optional: true
  checksum: 26e66c75eca24bf00c0fe8392a67c5c5783450e2596a5bb598c25fd51241c42aaaa64c31ff89c97cdf77ee7c9e915d5e201bc0dd4f8d949ec0c66dad6bf7cd91
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^1.0.2 || 2 || 3 || 4, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string.prototype.padend@npm:^3.0.0":
  version: 3.1.6
  resolution: "string.prototype.padend@npm:3.1.6"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-object-atoms: ^1.0.0
  checksum: d9fc23c21bdfb6850756002ef09cebc420882003f29eafbd8322df77a90726bc2a64892d01f94f1fc9fc6f809414fbcbd8615610bb3cddd33512c12b6b3643a2
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    define-data-property: ^1.1.4
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-object-atoms: ^1.0.0
    has-property-descriptors: ^1.0.2
  checksum: 87659cd8561237b6c69f5376328fda934693aedde17bb7a2c57008e9d9ff992d0c253a391c7d8d50114e0e49ff7daf86a362f7961cf92f7564cd01342ca2e385
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: cb86f639f41d791a43627784be2175daa9ca3259c7cb83e7a207a729909b74f2ea0ec5d85de5761e6835e5f443e9420c6ff3f63a845378e4a61dd793177bc287
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: df1007a7f580a49d692375d996521dc14fd103acda7f3034b3c558a60b82beeed3a64fa91e494e164581793a8ab0ae2f59578a49896a7af6583c1f20472bce96
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 8d50ff27b7ebe5ecc78f1fe1e00fcdff7af014e73cf724b46fb81ef889eeb1015fc5184b64e81a2efe002180f3ba431bdd77e300da5c6685d702780fbf0c8d5b
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 1074ccb63270d32ca28edfb0a281c96b94dc679077828135141f27d52a5a398ef5e78bcf22809d23cadc2b81dfbe345eb5fd8699b385c8b1128907dec4a7d1e1
  languageName: node
  linkType: hard

"strnum@npm:^2.1.0":
  version: 2.1.1
  resolution: "strnum@npm:2.1.1"
  checksum: 566139b218ef13bdde2a69c744852ac41ea167588f624d46c3b3bebb5d1d1775c55bca4702a0ad2a6a66eb4b3b7de4cbbc83e8d40c5835feabebf6f9cc468993
  languageName: node
  linkType: hard

"sucrase@npm:^3.35.0":
  version: 3.35.0
  resolution: "sucrase@npm:3.35.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.2
    commander: ^4.0.0
    glob: ^10.3.10
    lines-and-columns: ^1.1.6
    mz: ^2.7.0
    pirates: ^4.0.1
    ts-interface-checker: ^0.1.9
  bin:
    sucrase: bin/sucrase
    sucrase-node: bin/sucrase-node
  checksum: 9fc5792a9ab8a14dcf9c47dcb704431d35c1cdff1d17d55d382a31c2e8e3063870ad32ce120a80915498486246d612e30cda44f1624d9d9a10423e1a43487ad1
  languageName: node
  linkType: hard

"sumchecker@npm:^3.0.1":
  version: 3.0.1
  resolution: "sumchecker@npm:3.0.1"
  dependencies:
    debug: ^4.1.0
  checksum: 31ba7a62c889236b5b07f75b5c250d481158a1ca061b8f234fca0457bdbe48a20e5011c12c715343dc577e111463dc3d9e721b98015a445a2a88c35e0c9f0f91
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"table-layout@npm:^4.1.0":
  version: 4.1.1
  resolution: "table-layout@npm:4.1.1"
  dependencies:
    array-back: ^6.2.2
    wordwrapjs: ^5.1.0
  checksum: 6de52785440b3b2ca9522a06b9ce20f81a3a999c15ef7e5d10c38a2e0008b286bf145e7f88b00f0346e874a548a922906107c492d6da5d438332e7c1bb62307a
  languageName: node
  linkType: hard

"tailwind-merge@npm:^2.5.2":
  version: 2.6.0
  resolution: "tailwind-merge@npm:2.6.0"
  checksum: 18976c4096920bc6125f1dc837479805de996d86bcc636f98436f65c297003bde89ffe51dfd325b7c97fc71b1dbba8505459dd96010e7b181badd29aea996440
  languageName: node
  linkType: hard

"tailwindcss-animate@npm:^1.0.7":
  version: 1.0.7
  resolution: "tailwindcss-animate@npm:1.0.7"
  peerDependencies:
    tailwindcss: "*"
  checksum: c1760983eb3fec0c8421e95082bf308e6845df43e2f90862386366e82545c801b26b4d189c4cd23d6915252b76d18005c8e5f591f8b119944c7fb8650d0f8bce
  languageName: node
  linkType: hard

"tailwindcss@npm:^3.4.11, tailwindcss@npm:^3.4.17":
  version: 3.4.17
  resolution: "tailwindcss@npm:3.4.17"
  dependencies:
    "@alloc/quick-lru": ^5.2.0
    arg: ^5.0.2
    chokidar: ^3.6.0
    didyoumean: ^1.2.2
    dlv: ^1.1.3
    fast-glob: ^3.3.2
    glob-parent: ^6.0.2
    is-glob: ^4.0.3
    jiti: ^1.21.6
    lilconfig: ^3.1.3
    micromatch: ^4.0.8
    normalize-path: ^3.0.0
    object-hash: ^3.0.0
    picocolors: ^1.1.1
    postcss: ^8.4.47
    postcss-import: ^15.1.0
    postcss-js: ^4.0.1
    postcss-load-config: ^4.0.2
    postcss-nested: ^6.2.0
    postcss-selector-parser: ^6.1.2
    resolve: ^1.22.8
    sucrase: ^3.35.0
  bin:
    tailwind: lib/cli.js
    tailwindcss: lib/cli.js
  checksum: bda962f30e9a2f0567e2ee936ec863d5178958078e577ced13da60b3af779062a53a7e95f2f32b5c558f12a7477dea3ce071441a7362c6d7bf50bc9e166728a4
  languageName: node
  linkType: hard

"tar-fs@npm:^2.0.0":
  version: 2.1.3
  resolution: "tar-fs@npm:2.1.3"
  dependencies:
    chownr: ^1.1.1
    mkdirp-classic: ^0.5.2
    pump: ^3.0.0
    tar-stream: ^2.1.4
  checksum: 8dd66c20779c1fe535df5cf2ab5132705c12aba3ab95283f225a798329c5aaa8bbe92144c8e21bc9404f46a0d3ce59fc4997f5c42bafc55b6a225d4ad15aa966
  languageName: node
  linkType: hard

"tar-fs@npm:^3.0.4":
  version: 3.1.0
  resolution: "tar-fs@npm:3.1.0"
  dependencies:
    bare-fs: ^4.0.1
    bare-path: ^3.0.0
    pump: ^3.0.0
    tar-stream: ^3.1.5
  dependenciesMeta:
    bare-fs:
      optional: true
    bare-path:
      optional: true
  checksum: 279c6c4ee1c53593bad4e5ae044ff14b2210d68f559097ac48bf0638698f740f4b894ab22fa1860f5f9f0be8c4003bf16c3614759aeebf50ee3593c49213c357
  languageName: node
  linkType: hard

"tar-stream@npm:^2.1.4":
  version: 2.2.0
  resolution: "tar-stream@npm:2.2.0"
  dependencies:
    bl: ^4.0.3
    end-of-stream: ^1.4.1
    fs-constants: ^1.0.0
    inherits: ^2.0.3
    readable-stream: ^3.1.1
  checksum: 699831a8b97666ef50021c767f84924cfee21c142c2eb0e79c63254e140e6408d6d55a065a2992548e72b06de39237ef2b802b99e3ece93ca3904a37622a66f3
  languageName: node
  linkType: hard

"tar-stream@npm:^3.1.5":
  version: 3.1.7
  resolution: "tar-stream@npm:3.1.7"
  dependencies:
    b4a: ^1.6.4
    fast-fifo: ^1.2.0
    streamx: ^2.15.0
  checksum: 6393a6c19082b17b8dcc8e7fd349352bb29b4b8bfe1075912b91b01743ba6bb4298f5ff0b499a3bbaf82121830e96a1a59d4f21a43c0df339e54b01789cb8cc6
  languageName: node
  linkType: hard

"tar@npm:^6.0.2, tar@npm:^6.0.5, tar@npm:^6.1.11, tar@npm:^6.1.12, tar@npm:^6.1.2, tar@npm:^6.2.1":
  version: 6.2.1
  resolution: "tar@npm:6.2.1"
  dependencies:
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    minipass: ^5.0.0
    minizlib: ^2.1.1
    mkdirp: ^1.0.3
    yallist: ^4.0.0
  checksum: f1322768c9741a25356c11373bce918483f40fa9a25c69c59410c8a1247632487edef5fe76c5f12ac51a6356d2f1829e96d2bc34098668a2fc34d76050ac2b6c
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": ^4.0.0
    chownr: ^3.0.0
    minipass: ^7.1.2
    minizlib: ^3.0.1
    mkdirp: ^3.0.1
    yallist: ^5.0.0
  checksum: 8485350c0688331c94493031f417df069b778aadb25598abdad51862e007c39d1dd5310702c7be4a6784731a174799d8885d2fde0484269aea205b724d7b2ffa
  languageName: node
  linkType: hard

"temp-file@npm:^3.4.0":
  version: 3.4.0
  resolution: "temp-file@npm:3.4.0"
  dependencies:
    async-exit-hook: ^2.0.1
    fs-extra: ^10.0.0
  checksum: 8e2b90321c9d865ad3e9e613cc524c9a9e22cd7820d3c8378840a01ab720116f4de4d340bbca6a50a9562b37f8ce614451fdb02dc2f993b4f9866cf81840b3cb
  languageName: node
  linkType: hard

"text-decoder@npm:^1.1.0":
  version: 1.2.3
  resolution: "text-decoder@npm:1.2.3"
  dependencies:
    b4a: ^1.6.4
  checksum: d7642a61f9d72330eac52ff6b6e8d34dea03ebbb1e82749a8734e7892e246cf262ed70730d20c4351c5dc5334297b9cc6c0b6a8725a204a63a197d7728bb35e5
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: ">= 3.1.0 < 4"
  checksum: dba7cc8a23a154cdcb6acb7f51d61511c37a6b077ec5ab5da6e8b874272015937788402fd271fdfc5f187f8cb0948e38d0a42dcc89d554d731652ab458f5343e
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: ^1.0.0
  checksum: 84e1b804bfec49f3531215f17b4a6e50fd4397b5f7c1bccc427b9c656e1ecfb13ea79d899930184f78bc2f57285c54d9a50a590c8868f4f0cef5c1d9f898b05e
  languageName: node
  linkType: hard

"tiktoken@npm:^1.0.21":
  version: 1.0.21
  resolution: "tiktoken@npm:1.0.21"
  checksum: 6e98ccd062a09443d4c33e8c7707026955a35c5575533d7cb0233c5794e0079a1393fa7106b02c1f3b8b237295d912558cf16d8af6675addd0190e0cd994cce2
  languageName: node
  linkType: hard

"tiny-async-pool@npm:1.3.0":
  version: 1.3.0
  resolution: "tiny-async-pool@npm:1.3.0"
  dependencies:
    semver: ^5.5.0
  checksum: d51630522317b82355afa32b79ac3a02bcc29ef81e8045a95a2e4dfa736525bf4bcba1394abfb4169188fe42e3a9d9e4b378367f46daeb6b4b945d7cb727f860
  languageName: node
  linkType: hard

"tiny-invariant@npm:^1.3.1":
  version: 1.3.3
  resolution: "tiny-invariant@npm:1.3.3"
  checksum: 5e185c8cc2266967984ce3b352a4e57cb89dad5a8abb0dea21468a6ecaa67cd5bb47a3b7a85d08041008644af4f667fb8b6575ba38ba5fb00b3b5068306e59fe
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12, tinyglobby@npm:^0.2.13":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: ^6.4.4
    picomatch: ^4.0.2
  checksum: 261e986e3f2062dec3a582303bad2ce31b4634b9348648b46828c000d464b012cf474e38f503312367d4117c3f2f18611992738fca684040758bba44c24de522
  languageName: node
  linkType: hard

"tmp-promise@npm:^3.0.2":
  version: 3.0.3
  resolution: "tmp-promise@npm:3.0.3"
  dependencies:
    tmp: ^0.2.0
  checksum: f854f5307dcee6455927ec3da9398f139897faf715c5c6dcee6d9471ae85136983ea06662eba2edf2533bdcb0fca66d16648e79e14381e30c7fb20be9c1aa62c
  languageName: node
  linkType: hard

"tmp@npm:^0.2.0":
  version: 0.2.3
  resolution: "tmp@npm:0.2.3"
  checksum: 73b5c96b6e52da7e104d9d44afb5d106bb1e16d9fa7d00dbeb9e6522e61b571fbdb165c756c62164be9a3bbe192b9b268c236d370a2a0955c7689cd2ae377b95
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"tree-sitter-bash@npm:^0.25.0":
  version: 0.25.0
  resolution: "tree-sitter-bash@npm:0.25.0"
  dependencies:
    node-addon-api: ^8.2.1
    node-gyp: latest
    node-gyp-build: ^4.8.2
  peerDependencies:
    tree-sitter: ^0.25.0
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: b5c2c05ae8f4347204e6c574995502251482464498cd59c4a1deeafa0b7f258246c7d4ded449dc1759beab17be9f9bdce34d2e8c60eb42f7be72adcada48d5e1
  languageName: node
  linkType: hard

"tree-sitter-c-sharp@npm:^0.23.1":
  version: 0.23.1
  resolution: "tree-sitter-c-sharp@npm:0.23.1"
  dependencies:
    node-addon-api: ^8.2.2
    node-gyp: latest
    node-gyp-build: ^4.8.2
  peerDependencies:
    tree-sitter: ^0.21.1
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: b02b3a7a613ff5b84ad8778c6f468206a80c83c6b644c09fa79da0315f38d347cfa49b91dc71eb2b00a735620aabd14d4cf52930f04e44c869e510254990eb80
  languageName: node
  linkType: hard

"tree-sitter-c@npm:^0.23.1":
  version: 0.23.6
  resolution: "tree-sitter-c@npm:0.23.6"
  dependencies:
    node-addon-api: ^8.3.0
    node-gyp: latest
    node-gyp-build: ^4.8.4
  peerDependencies:
    tree-sitter: ^0.22.1
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: 48ea662266f0b394dec33d31c30d00616c9651fa723fa6abea7a4d3755e3cf7ae437d6d2d30f79b1b21d5acc6332630bae87aa333d2a51f20b992aa059a7008e
  languageName: node
  linkType: hard

"tree-sitter-c@npm:^0.24.1":
  version: 0.24.1
  resolution: "tree-sitter-c@npm:0.24.1"
  dependencies:
    node-addon-api: ^8.3.1
    node-gyp: latest
    node-gyp-build: ^4.8.4
  peerDependencies:
    tree-sitter: ^0.22.4
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: a5a2264057b3fdd05df2e37da872c546a9ef25b63db180a97a869c85192942f5c6c02c936a8d6e70bfe6f3c86d281d0623cb19cb3510767478048b71d3f1539c
  languageName: node
  linkType: hard

"tree-sitter-cpp@npm:^0.23.4":
  version: 0.23.4
  resolution: "tree-sitter-cpp@npm:0.23.4"
  dependencies:
    node-addon-api: ^8.2.1
    node-gyp: latest
    node-gyp-build: ^4.8.2
    tree-sitter-c: ^0.23.1
  peerDependencies:
    tree-sitter: ^0.21.1
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: 68f3e63aea8c5060c8ed12c71a876389eafae9ede51a10fecd1743c196734bd84428ca8f4740a48f1df4596859783ab56bcf6001a66bc545869d0bb9c3a7037f
  languageName: node
  linkType: hard

"tree-sitter-css@npm:0.21.0":
  version: 0.21.0
  resolution: "tree-sitter-css@npm:0.21.0"
  dependencies:
    node-addon-api: ^8.0.0
    node-gyp: latest
    node-gyp-build: ^4.8.1
  peerDependencies:
    tree-sitter: ^0.21.0
  peerDependenciesMeta:
    tree_sitter:
      optional: true
  checksum: 660a64707c2e50e327e059c7a8b99a7ff8aad5de5b192b3ac8c2e3de89702b6abcab4e7cc9ac9a245c286ee002679e75c00f96ae023f69693f86d33c74ebd3a8
  languageName: node
  linkType: hard

"tree-sitter-dockerfile@npm:^0.0.1-security":
  version: 0.0.1-security
  resolution: "tree-sitter-dockerfile@npm:0.0.1-security"
  checksum: c3f768e49a791d5f0b60e2ccf9731c361cd5b6c6ad3ba840610e4f5b7b2edbddec5a6ada3cd828da5c851e1343c9021bf770096edcd9da3754a70f5d4e3a9401
  languageName: node
  linkType: hard

"tree-sitter-go@npm:^0.23.4":
  version: 0.23.4
  resolution: "tree-sitter-go@npm:0.23.4"
  dependencies:
    node-addon-api: ^8.2.1
    node-gyp: latest
    node-gyp-build: ^4.8.2
  peerDependencies:
    tree-sitter: ^0.21.1
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: c1e0ac1fd2545e00ad9631375bec29ee8f7db474d2f16dd779199b9d80e39adcb891a6cb55c5d50140fabef50ac47b24d724d9afd5da65a2e6ea57c20d64a715
  languageName: node
  linkType: hard

"tree-sitter-html@npm:^0.23.2":
  version: 0.23.2
  resolution: "tree-sitter-html@npm:0.23.2"
  dependencies:
    node-addon-api: ^8.2.2
    node-gyp: latest
    node-gyp-build: ^4.8.2
  peerDependencies:
    tree-sitter: ^0.21.1
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: 91c6e94b36e3527aeae8c8bf2bc1035fbcbcb8a0dc9c49b2679642dedbad097250e454660840e1fa042d900d10f5a08fec0b8b4fd62f0bf4ae5c7daf4830cb6f
  languageName: node
  linkType: hard

"tree-sitter-java@npm:^0.23.5":
  version: 0.23.5
  resolution: "tree-sitter-java@npm:0.23.5"
  dependencies:
    node-addon-api: ^8.2.2
    node-gyp: latest
    node-gyp-build: ^4.8.2
  peerDependencies:
    tree-sitter: ^0.21.1
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: 3942a62cc622fc940324af0b50f0ce8072a8fcefe06fbfae7c03c97829f2692042177a8160c33ede2a4041a2b7edb6108cc405c6167ce54b5b7ad0072fba5721
  languageName: node
  linkType: hard

"tree-sitter-javascript@npm:^0.23.1":
  version: 0.23.1
  resolution: "tree-sitter-javascript@npm:0.23.1"
  dependencies:
    node-addon-api: ^8.2.2
    node-gyp: latest
    node-gyp-build: ^4.8.2
  peerDependencies:
    tree-sitter: ^0.21.1
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: ad62706652ce1290ce39fbbbc763e456f401b7e65dc18359d23f0dbffe19589edced2142a3c6c44a44f2a9b918037e570c44e3119f5aa40a8701f77b1dc8525e
  languageName: node
  linkType: hard

"tree-sitter-json@npm:^0.24.8":
  version: 0.24.8
  resolution: "tree-sitter-json@npm:0.24.8"
  dependencies:
    node-addon-api: ^8.2.2
    node-gyp: latest
    node-gyp-build: ^4.8.2
  peerDependencies:
    tree-sitter: ^0.21.1
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: 740f3483a5b2dbd8b439f4b409994187029a0a26f2b8609dbad3b4405c9005f1134a813d461793d5628a593fbd7df04ecf366a917dfcd32790292e8526b5a4df
  languageName: node
  linkType: hard

"tree-sitter-php@npm:^0.23.12":
  version: 0.23.12
  resolution: "tree-sitter-php@npm:0.23.12"
  dependencies:
    node-addon-api: ^8.2.2
    node-gyp: latest
    node-gyp-build: ^4.8.2
  peerDependencies:
    tree-sitter: ^0.21.1
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: f0d1f547d142e4739d7d41d3815eacfee8abd43555f13ddc3eef2476168075b3980f82794ff6d050a6e2aa799863f2a24de37ba9f8da734e920075802fe8ae31
  languageName: node
  linkType: hard

"tree-sitter-python@npm:^0.23.6":
  version: 0.23.6
  resolution: "tree-sitter-python@npm:0.23.6"
  dependencies:
    node-addon-api: ^8.3.0
    node-gyp: latest
    node-gyp-build: ^4.8.4
  peerDependencies:
    tree-sitter: ^0.22.1
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: 49181adbd1880e88cf0acec9cc7b4938bedd6fa3f296607ef29669cc093c099925087b6a9806463172671f8168c0d1476ce1c394342fcc77105f8168680c1cdf
  languageName: node
  linkType: hard

"tree-sitter-ruby@npm:^0.23.1":
  version: 0.23.1
  resolution: "tree-sitter-ruby@npm:0.23.1"
  dependencies:
    node-addon-api: ^8.2.2
    node-gyp: latest
    node-gyp-build: ^4.8.2
  peerDependencies:
    tree-sitter: ^0.21.1
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: f12a2a951c7d9a3e20855bd33dd5f9e36015a34b44bdb81f5db22f057a6a84dfd29cf3ae9f6390117442292cc443e0a726136c9a5cd9e9f97ae83d70c086cd10
  languageName: node
  linkType: hard

"tree-sitter-rust@npm:^0.24.0":
  version: 0.24.0
  resolution: "tree-sitter-rust@npm:0.24.0"
  dependencies:
    node-addon-api: ^8.2.2
    node-gyp: latest
    node-gyp-build: ^4.8.4
  peerDependencies:
    tree-sitter: ^0.22.1
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: f030b70bd22de3de61db84604f3fa566a90ae7dd5c2bb0b65c46360b8f60640eabdb79f119ccfa2aa619ac52ac033d5b3593abec1159a40ed4ddc5d2f48b87c8
  languageName: node
  linkType: hard

"tree-sitter-scss@npm:1.0.0":
  version: 1.0.0
  resolution: "tree-sitter-scss@npm:1.0.0"
  dependencies:
    node-addon-api: ^8.0.0
    node-gyp: latest
    node-gyp-build: ^4.8.0
    tree-sitter-css: ^0.20.0
  peerDependencies:
    tree-sitter: ^0.21.0
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: 43c0328531298767302509914bc8dde3e2d5f4253361bec9ca2f86f534bda91d1cd307d6956c69c30ea4317cb95e526cc03f0550b1245d86be3c4aafe2dd8bff
  languageName: node
  linkType: hard

"tree-sitter-typescript@npm:^0.23.2":
  version: 0.23.2
  resolution: "tree-sitter-typescript@npm:0.23.2"
  dependencies:
    node-addon-api: ^8.2.2
    node-gyp: latest
    node-gyp-build: ^4.8.2
    tree-sitter-javascript: ^0.23.1
  peerDependencies:
    tree-sitter: ^0.21.0
  peerDependenciesMeta:
    tree-sitter:
      optional: true
  checksum: 733c25771ece52c29b8927c21cdc68010acb19677ec437e242dd13e7ea8f71bd6790becc75277545c7320588ae33b2c8bac5b131fed3d43bf2ede835d9f9c9ba
  languageName: node
  linkType: hard

"tree-sitter@npm:^0.25.0":
  version: 0.25.0
  resolution: "tree-sitter@npm:0.25.0"
  dependencies:
    node-addon-api: ^8.3.0
    node-gyp: latest
    node-gyp-build: ^4.8.4
  checksum: 7e1645eee7c4eed203ab790cd6a3c1ea2f5c6ff9b63687f04d6f648cf3a9e35c2240d32135fde5f7fe380608b41ddeb2786420bafd5dc186cfe8b139b10de7bd
  languageName: node
  linkType: hard

"truncate-utf8-bytes@npm:^1.0.0":
  version: 1.0.2
  resolution: "truncate-utf8-bytes@npm:1.0.2"
  dependencies:
    utf8-byte-length: ^1.0.1
  checksum: ad097314709ea98444ad9c80c03aac8da805b894f37ceb5685c49ad297483afe3a5ec9572ebcaff699dda72b6cd447a2ba2a3fd10e96c2628cd16d94abeb328a
  languageName: node
  linkType: hard

"ts-api-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "ts-api-utils@npm:2.1.0"
  peerDependencies:
    typescript: ">=4.8.4"
  checksum: 5b1ef89105654d93d67582308bd8dfe4bbf6874fccbcaa729b08fbb00a940fd4c691ca6d0d2b18c3c70878d9a7e503421b7cc473dbc3d0d54258b86401d4b15d
  languageName: node
  linkType: hard

"ts-interface-checker@npm:^0.1.9":
  version: 0.1.13
  resolution: "ts-interface-checker@npm:0.1.13"
  checksum: 20c29189c2dd6067a8775e07823ddf8d59a33e2ffc47a1bd59a5cb28bb0121a2969a816d5e77eda2ed85b18171aa5d1c4005a6b88ae8499ec7cc49f78571cb5e
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.1.0, tslib@npm:^2.4.0, tslib@npm:^2.6.2, tslib@npm:^2.8.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: e4aba30e632b8c8902b47587fd13345e2827fa639e7c3121074d5ee0880723282411a8838f830b55100cbe4517672f84a2472667d355b81e8af165a55dc6203a
  languageName: node
  linkType: hard

"tunnel-agent@npm:^0.6.0":
  version: 0.6.0
  resolution: "tunnel-agent@npm:0.6.0"
  dependencies:
    safe-buffer: ^5.0.1
  checksum: 05f6510358f8afc62a057b8b692f05d70c1782b70db86d6a1e0d5e28a32389e52fa6e7707b6c5ecccacc031462e4bc35af85ecfe4bbc341767917b7cf6965711
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"type-fest@npm:^0.13.1":
  version: 0.13.1
  resolution: "type-fest@npm:0.13.1"
  checksum: e6bf2e3c449f27d4ef5d56faf8b86feafbc3aec3025fc9a5fbe2db0a2587c44714521f9c30d8516a833c8c506d6263f5cc11267522b10c6ccdb6cc55b0a9d1c4
  languageName: node
  linkType: hard

"type-is@npm:^2.0.0, type-is@npm:^2.0.1":
  version: 2.0.1
  resolution: "type-is@npm:2.0.1"
  dependencies:
    content-type: ^1.0.5
    media-typer: ^1.1.0
    mime-types: ^3.0.0
  checksum: 0266e7c782238128292e8c45e60037174d48c6366bb2d45e6bd6422b611c193f83409a8341518b6b5f33f8e4d5a959f38658cacfea77f0a3505b9f7ac1ddec8f
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-typed-array: ^1.1.14
  checksum: 3fb91f0735fb413b2bbaaca9fabe7b8fc14a3fa5a5a7546bab8a57e755be0e3788d893195ad9c2b842620592de0e68d4c077d4c2c41f04ec25b8b5bb82fa9a80
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.8
    for-each: ^0.3.3
    gopd: ^1.2.0
    has-proto: ^1.2.0
    is-typed-array: ^1.1.14
  checksum: cda9352178ebeab073ad6499b03e938ebc30c4efaea63a26839d89c4b1da9d2640b0d937fc2bd1f049eb0a38def6fbe8a061b601292ae62fe079a410ce56e3a6
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    for-each: ^0.3.3
    gopd: ^1.2.0
    has-proto: ^1.2.0
    is-typed-array: ^1.1.15
    reflect.getprototypeof: ^1.0.9
  checksum: 670b7e6bb1d3c2cf6160f27f9f529e60c3f6f9611c67e47ca70ca5cfa24ad95415694c49d1dbfeda016d3372cab7dfc9e38c7b3e1bb8d692cae13a63d3c144d7
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    is-typed-array: ^1.1.13
    possible-typed-array-names: ^1.0.0
    reflect.getprototypeof: ^1.0.6
  checksum: deb1a4ffdb27cd930b02c7030cb3e8e0993084c643208e52696e18ea6dd3953dfc37b939df06ff78170423d353dc8b10d5bae5796f3711c1b3abe52872b3774c
  languageName: node
  linkType: hard

"typescript-eslint@npm:^8.30.1":
  version: 8.36.0
  resolution: "typescript-eslint@npm:8.36.0"
  dependencies:
    "@typescript-eslint/eslint-plugin": 8.36.0
    "@typescript-eslint/parser": 8.36.0
    "@typescript-eslint/utils": 8.36.0
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: b3c2482c1f859d051177f86383a8b52c4d4db9d2a8db8a20ac1cacd3e31094ebd7a71143a76b86b91c52b139fa540b81a2ec77893a32401b9c9f3f11dea3e17a
  languageName: node
  linkType: hard

"typescript@npm:^5.4.3, typescript@npm:~5.8.3":
  version: 5.8.3
  resolution: "typescript@npm:5.8.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: cb1d081c889a288b962d3c8ae18d337ad6ee88a8e81ae0103fa1fecbe923737f3ba1dbdb3e6d8b776c72bc73bfa6d8d850c0306eed1a51377d2fccdfd75d92c4
  languageName: node
  linkType: hard

"typescript@patch:typescript@^5.4.3#~builtin<compat/typescript>, typescript@patch:typescript@~5.8.3#~builtin<compat/typescript>":
  version: 5.8.3
  resolution: "typescript@patch:typescript@npm%3A5.8.3#~builtin<compat/typescript>::version=5.8.3&hash=14eedb"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 1b503525a88ff0ff5952e95870971c4fb2118c17364d60302c21935dedcd6c37e6a0a692f350892bafcef6f4a16d09073fe461158547978d2f16fbe4cb18581c
  languageName: node
  linkType: hard

"typical@npm:^4.0.0":
  version: 4.0.0
  resolution: "typical@npm:4.0.0"
  checksum: a242081956825328f535e6195a924240b34daf6e7fdb573a1809a42b9f37fb8114fa99c7ab89a695e0cdb419d4149d067f6723e4b95855ffd39c6c4ca378efb3
  languageName: node
  linkType: hard

"typical@npm:^7.1.1":
  version: 7.3.0
  resolution: "typical@npm:7.3.0"
  checksum: edbb9beed7ffb355806d434d1dd0d41a2b78be0a41d9f1684fabbd4fb512ee220989b5ff91b04c79d19b850d6025d6c07417d63b8e7c9a3b2229a4a0676e17da
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    has-bigints: ^1.0.2
    has-symbols: ^1.1.0
    which-boxed-primitive: ^1.1.1
  checksum: 729f13b84a5bfa3fead1d8139cee5c38514e63a8d6a437819a473e241ba87eeb593646568621c7fc7f133db300ef18d65d1a5a60dc9c7beb9000364d93c581df
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: 46331c7d6016bf85b3e8f20c159d62f5ae471aba1eb3dc52fff35a0259d58dcc7d592d4cc4f00c5f9243fa738a11cfa48bd20203040d4a9e6bc25e807fab7ab3
  languageName: node
  linkType: hard

"undici-types@npm:~7.8.0":
  version: 7.8.0
  resolution: "undici-types@npm:7.8.0"
  checksum: 59521a5b9b50e72cb838a29466b3557b4eacbc191a83f4df5a2f7b156bc8263072b145dc4bb8ec41da7d56a7e9b178892458da02af769243d57f801a50ac5751
  languageName: node
  linkType: hard

"unique-filename@npm:^1.1.1":
  version: 1.1.1
  resolution: "unique-filename@npm:1.1.1"
  dependencies:
    unique-slug: ^2.0.0
  checksum: cf4998c9228cc7647ba7814e255dec51be43673903897b1786eff2ac2d670f54d4d733357eb08dea969aa5e6875d0e1bd391d668fbdb5a179744e7c7551a6f80
  languageName: node
  linkType: hard

"unique-filename@npm:^2.0.0":
  version: 2.0.1
  resolution: "unique-filename@npm:2.0.1"
  dependencies:
    unique-slug: ^3.0.0
  checksum: 807acf3381aff319086b64dc7125a9a37c09c44af7620bd4f7f3247fcd5565660ac12d8b80534dcbfd067e6fe88a67e621386dd796a8af828d1337a8420a255f
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: ^5.0.0
  checksum: 6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^2.0.0":
  version: 2.0.2
  resolution: "unique-slug@npm:2.0.2"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 5b6876a645da08d505dedb970d1571f6cebdf87044cb6b740c8dbb24f0d6e1dc8bdbf46825fd09f994d7cf50760e6f6e063cfa197d51c5902c00a861702eb75a
  languageName: node
  linkType: hard

"unique-slug@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-slug@npm:3.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 49f8d915ba7f0101801b922062ee46b7953256c93ceca74303bd8e6413ae10aa7e8216556b54dc5382895e8221d04f1efaf75f945c2e4a515b4139f77aa6640c
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 222d0322bc7bbf6e45c08967863212398313ef73423f4125e075f893a02405a5ffdbaaf150f7dd1e99f8861348a486dd079186d27c5f2c60e465b7dcbb1d3e5b
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: 40cdc60f6e61070fe658ca36016a8f4ec216b29bf04a55dce14e3710cc84c7448538ef4dad3728d0bfe29975ccd7bfb5f414c45e7b78883567fb31b246f02dff
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: ecd8469fe0db28e7de9e5289d32bd1b6ba8f7183db34f3bfc4ca53c49891c2d6aa05f3fb3936a81285a905cc509fb641a0c3fc131ec786167eff41236ae32e60
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: ^3.2.0
    picocolors: ^1.1.1
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 7b6d8d08c34af25ee435bccac542bedcb9e57c710f3c42421615631a80aa6dd28b0a81c9d2afbef53799d482fb41453f714b8a7a0a8003e3b4ec8fb1abb819af
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2, uri-js@npm:^4.4.1":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"use-callback-ref@npm:^1.3.3":
  version: 1.3.3
  resolution: "use-callback-ref@npm:1.3.3"
  dependencies:
    tslib: ^2.0.0
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 4da1c82d7a2409cee6c882748a40f4a083decf238308bf12c3d0166f0e338f8d512f37b8d11987eb5a421f14b9b5b991edf3e11ed25c3bb7a6559081f8359b44
  languageName: node
  linkType: hard

"use-sidecar@npm:^1.1.3":
  version: 1.1.3
  resolution: "use-sidecar@npm:1.1.3"
  dependencies:
    detect-node-es: ^1.1.0
    tslib: ^2.0.0
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 88664c6b2c5b6e53e4d5d987694c9053cea806da43130248c74ca058945c8caa6ccb7b1787205a9eb5b9d124633e42153848904002828acabccdc48cda026622
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.5.0":
  version: 1.5.0
  resolution: "use-sync-external-store@npm:1.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 5e639c9273200adb6985b512c96a3a02c458bc8ca1a72e91da9cdc6426144fc6538dca434b0f99b28fb1baabc82e1c383ba7900b25ccdcb43758fb058dc66c34
  languageName: node
  linkType: hard

"utf8-byte-length@npm:^1.0.1":
  version: 1.0.5
  resolution: "utf8-byte-length@npm:1.0.5"
  checksum: 168edff8f7baca974b5bfb5256cebd57deaef8fbf2d0390301dd1009da52de64774d62f088254c94021e372147b6c938aa82f2318a3a19f9ebd21e48b7f40029
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 39931f6da74e307f51c0fb463dc2462807531dc80760a9bff1e35af4316131b4fc3203d16da60ae33f07fdca5b56f3f1dd662da0c99fea9aaeab2004780cc5f4
  languageName: node
  linkType: hard

"validate-npm-package-license@npm:^3.0.1":
  version: 3.0.4
  resolution: "validate-npm-package-license@npm:3.0.4"
  dependencies:
    spdx-correct: ^3.0.0
    spdx-expression-parse: ^3.0.0
  checksum: 35703ac889d419cf2aceef63daeadbe4e77227c39ab6287eeb6c1b36a746b364f50ba22e88591f5d017bc54685d8137bc2d328d0a896e4d3fd22093c0f32a9ad
  languageName: node
  linkType: hard

"vary@npm:^1, vary@npm:^1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: ae0123222c6df65b437669d63dfa8c36cee20a504101b2fcd97b8bf76f91259c17f9f2b4d70a1e3c6bbcee7f51b28392833adb6b2770b23b01abec84e369660b
  languageName: node
  linkType: hard

"vaul@npm:^0.9.3":
  version: 0.9.9
  resolution: "vaul@npm:0.9.9"
  dependencies:
    "@radix-ui/react-dialog": ^1.1.1
  peerDependencies:
    react: ^16.8 || ^17.0 || ^18.0
    react-dom: ^16.8 || ^17.0 || ^18.0
  checksum: 02136ea498fa730b1bc7be200fa29b2ef93c80d030d16870bcd76cfdfb258e1aa590ae054455bfc92f471c6a561166aa978197615b037570369df50ac4eb6635
  languageName: node
  linkType: hard

"verror@npm:^1.10.0":
  version: 1.10.1
  resolution: "verror@npm:1.10.1"
  dependencies:
    assert-plus: ^1.0.0
    core-util-is: 1.0.2
    extsprintf: ^1.2.0
  checksum: 690a8d6ad5a4001672290e9719e3107c86269bc45fe19f844758eecf502e59f8aa9631b19b839f6d3dea562334884d22d1eb95ae7c863032075a9212c889e116
  languageName: node
  linkType: hard

"victory-vendor@npm:^36.6.8":
  version: 36.9.2
  resolution: "victory-vendor@npm:36.9.2"
  dependencies:
    "@types/d3-array": ^3.0.3
    "@types/d3-ease": ^3.0.0
    "@types/d3-interpolate": ^3.0.1
    "@types/d3-scale": ^4.0.2
    "@types/d3-shape": ^3.1.0
    "@types/d3-time": ^3.0.0
    "@types/d3-timer": ^3.0.0
    d3-array: ^3.1.6
    d3-ease: ^3.0.1
    d3-interpolate: ^3.0.1
    d3-scale: ^4.0.2
    d3-shape: ^3.1.0
    d3-time: ^3.0.0
    d3-timer: ^3.0.1
  checksum: a755110e287b700202d08ac81982093ab100edaa9d61beef1476d59e9705605bd8299a3aa41fa04b933a12bd66737f4c8f7d18448dd6488c69d4f72480023a2e
  languageName: node
  linkType: hard

"vite-plugin-electron-renderer@npm:^0.14.6":
  version: 0.14.6
  resolution: "vite-plugin-electron-renderer@npm:0.14.6"
  checksum: d239977df9d0da6a071eff5a61381da64f174b7dd26a0e86743812f798feebd492bd18206f26e0f39e794c4e03eb0e067c97aa225313644cef7741ccee40ad64
  languageName: node
  linkType: hard

"vite-plugin-electron@npm:^0.29.0":
  version: 0.29.0
  resolution: "vite-plugin-electron@npm:0.29.0"
  peerDependencies:
    vite-plugin-electron-renderer: "*"
  peerDependenciesMeta:
    vite-plugin-electron-renderer:
      optional: true
  checksum: 551ce609a00346a65ac069b518d5cda5d759424ed2585aecb95f667d7cf5a888f49dd658cf95c22fa093831bee033ba48f3cc61a15ad9b572473928b2211e1f4
  languageName: node
  linkType: hard

"vite@npm:^6.3.5":
  version: 6.3.5
  resolution: "vite@npm:6.3.5"
  dependencies:
    esbuild: ^0.25.0
    fdir: ^6.4.4
    fsevents: ~2.3.3
    picomatch: ^4.0.2
    postcss: ^8.5.3
    rollup: ^4.34.9
    tinyglobby: ^0.2.13
  peerDependencies:
    "@types/node": ^18.0.0 || ^20.0.0 || >=22.0.0
    jiti: ">=1.21.0"
    less: "*"
    lightningcss: ^1.21.0
    sass: "*"
    sass-embedded: "*"
    stylus: "*"
    sugarss: "*"
    terser: ^5.16.0
    tsx: ^4.8.1
    yaml: ^2.4.2
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    jiti:
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
    tsx:
      optional: true
    yaml:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: b7f1ebaae483090441f17ca09ea2c9b803688d2a2ed9860fbd8b72271918776ea3ceca643e807a5ee00628d65b79656d32529a4b8dd388aa33e41bc3f38732d0
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: ^1.0.3
  checksum: 814e9d1ddcc9798f7377ffa448a5a3892232b9275ebb30a41b529607691c0491de47cba426e917a4d08ded3ee7e9ba2f3fe32e62ee3cd9c7d3bafb7754bd553c
  languageName: node
  linkType: hard

"web-tree-sitter@npm:^0.22.6":
  version: 0.22.6
  resolution: "web-tree-sitter@npm:0.22.6"
  checksum: 1c207db125c8e4743a47f41d19ed05fe5ec8590fd2b46e168e55306403826140a79cfec8b203f30a07f14b501197926f0806201b8d9e19e256d4285393c8a00d
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: ^1.1.0
    is-boolean-object: ^1.2.1
    is-number-object: ^1.1.1
    is-string: ^1.1.1
    is-symbol: ^1.1.1
  checksum: ee41d0260e4fd39551ad77700c7047d3d281ec03d356f5e5c8393fe160ba0db53ef446ff547d05f76ffabfd8ad9df7c9a827e12d4cccdbc8fccf9239ff8ac21e
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: ^1.0.2
    function.prototype.name: ^1.1.6
    has-tostringtag: ^1.0.2
    is-async-function: ^2.0.0
    is-date-object: ^1.1.0
    is-finalizationregistry: ^1.1.0
    is-generator-function: ^1.0.10
    is-regex: ^1.2.1
    is-weakref: ^1.0.2
    isarray: ^2.0.5
    which-boxed-primitive: ^1.1.0
    which-collection: ^1.0.2
    which-typed-array: ^1.1.16
  checksum: 7a3617ba0e7cafb795f74db418df889867d12bce39a477f3ee29c6092aa64d396955bf2a64eae3726d8578440e26777695544057b373c45a8bcf5fbe920bf633
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: ^2.0.3
    is-set: ^2.0.3
    is-weakmap: ^2.0.2
    is-weakset: ^2.0.3
  checksum: c51821a331624c8197916598a738fc5aeb9a857f1e00d89f5e4c03dc7c60b4032822b8ec5696d28268bb83326456a8b8216344fb84270d18ff1d7628051879d9
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.19":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    for-each: ^0.3.5
    get-proto: ^1.0.1
    gopd: ^1.2.0
    has-tostringtag: ^1.0.2
  checksum: 162d2a07f68ea323f88ed9419861487ce5d02cb876f2cf9dd1e428d04a63133f93a54f89308f337b27cabd312ee3d027cae4a79002b2f0a85b79b9ef4c190670
  languageName: node
  linkType: hard

"which@npm:^1.2.9":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: ^2.0.0
  bin:
    which: ./bin/which
  checksum: f2e185c6242244b8426c9df1510e86629192d93c1a986a7d2a591f2c24869e7ffd03d6dac07ca863b2e4c06f59a4cc9916c585b72ee9fa1aa609d0124df15e04
  languageName: node
  linkType: hard

"which@npm:^2.0.1, which@npm:^2.0.2":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: 6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"wide-align@npm:^1.1.5":
  version: 1.1.5
  resolution: "wide-align@npm:1.1.5"
  dependencies:
    string-width: ^1.0.2 || 2 || 3 || 4
  checksum: d5fc37cd561f9daee3c80e03b92ed3e84d80dde3365a8767263d03dacfc8fa06b065ffe1df00d8c2a09f731482fcacae745abfbb478d4af36d0a891fad4834d3
  languageName: node
  linkType: hard

"wink-distance@npm:^2.0.1":
  version: 2.0.2
  resolution: "wink-distance@npm:2.0.2"
  dependencies:
    wink-helpers: ^2.0.0
    wink-jaro-distance: ^2.0.0
  checksum: 23892f14b24d06f5e4be6e39acd342fed1f43dcab78ab82c8c3af5584623c199b1166c48758845b6e7073f65c1a24e9a46d975be4d5ba7e8a02de7c7d7495224
  languageName: node
  linkType: hard

"wink-eng-lite-web-model@npm:^1.4.3":
  version: 1.8.1
  resolution: "wink-eng-lite-web-model@npm:1.8.1"
  checksum: 8464e070fcbb91d68a3766a2fb1d4fc28fd3c54cae4385342f16aa03116ec9cd19a11a1d2f8372a902ac4d17aa757e4e76697fbf58826a3d0e5f3b865e24013b
  languageName: node
  linkType: hard

"wink-helpers@npm:^2.0.0":
  version: 2.0.0
  resolution: "wink-helpers@npm:2.0.0"
  checksum: 28369d160d6582549689671944958bbe17c7998649b2bf4ffba5e644c67851accb058ca6d4020057f244a04f951fe54ed55c0cb4534ba8daf617aa9fa78d3188
  languageName: node
  linkType: hard

"wink-jaro-distance@npm:^2.0.0":
  version: 2.0.0
  resolution: "wink-jaro-distance@npm:2.0.0"
  checksum: 61a3f3c9615f2b0a5a48688e183230fc4f03504b1b6f0b25be439e3c498c1df9ac9907640a1833c7ae5bdcb55a90610ca7f20d41e867ee2e8e22ba93a40a79bd
  languageName: node
  linkType: hard

"wink-nlp-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "wink-nlp-utils@npm:2.1.0"
  dependencies:
    wink-distance: ^2.0.1
    wink-eng-lite-web-model: ^1.4.3
    wink-helpers: ^2.0.0
    wink-nlp: ^1.12.0
    wink-porter2-stemmer: ^2.0.1
    wink-tokenizer: ^5.2.3
  checksum: 84d3b5eb7e943915a0352eb3acf5e0fcee65e1a051c77424db8824277b2200a27bf5c96baa03d5ba0409d211186d4671b061302055ea50b2ff7ca3708f005a1c
  languageName: node
  linkType: hard

"wink-nlp@npm:^1.12.0":
  version: 1.14.3
  resolution: "wink-nlp@npm:1.14.3"
  checksum: c13e9bf904015800f2ace5f16425e7f4c96578a480d8552b623bf466a5f53491d27c719853595c0af39af02f18c46ded1f3e15796ec0eaf6120a01ee11fcee95
  languageName: node
  linkType: hard

"wink-porter2-stemmer@npm:^2.0.1":
  version: 2.0.1
  resolution: "wink-porter2-stemmer@npm:2.0.1"
  checksum: 65b71040f5ba361d24f09f1d07146293033ab3dd2ef12b486b6d5671c579038ccd47b9bf265b9f2a40959c85bf9277b0ec52475b13cd08732f3754a7648c0ea8
  languageName: node
  linkType: hard

"wink-tokenizer@npm:^5.2.3":
  version: 5.3.0
  resolution: "wink-tokenizer@npm:5.3.0"
  dependencies:
    emoji-regex: ^9.0.0
  checksum: fb53926e9766fca6b7f393174b0e213d7db43102a51c1f430ca6b84e9488d7ee36d20b06fa9ce3e76dcdb7d44ceaed398d4d45b9745c9d3266336e4a8a3770a6
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: f93ba3586fc181f94afdaff3a6fef27920b4b6d9eaefed0f428f8e07adea2a7f54a5f2830ce59406c8416f033f86902b91eb824072354645eea687dff3691ccb
  languageName: node
  linkType: hard

"wordwrapjs@npm:^5.1.0":
  version: 5.1.0
  resolution: "wordwrapjs@npm:5.1.0"
  checksum: 063c7a5a85b694be1a5fd96f7ae0c0f4d717a087909e5c70cf25edec6eb5df5f2f5561f23e939cf6d7514cf81902610f74f288ef1612a49bf5451de15e0e29db
  languageName: node
  linkType: hard

"workerpool@npm:^9.3.2":
  version: 9.3.3
  resolution: "workerpool@npm:9.3.3"
  checksum: 88ac57d881383dbfce04c88f29f89e7a5787dc489f33a901e75fad5c92df7dc88dfb47b26def8676df779e3ed46c6ff36786205eaed9c9720cebc9794e2142dd
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"xmlbuilder@npm:>=11.0.1, xmlbuilder@npm:^15.1.1":
  version: 15.1.1
  resolution: "xmlbuilder@npm:15.1.1"
  checksum: 14f7302402e28d1f32823583d121594a9dca36408d40320b33f598bd589ca5163a352d076489c9c64d2dc1da19a790926a07bf4191275330d4de2b0d85bb1843
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 54f0fb95621ee60898a38c572c515659e51cc9d9f787fb109cef6fde4befbe1c4602dc999d30110feee37456ad0f1660fa2edcfde6a9a740f86a290999550d30
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 48f7bb00dc19fc635a13a39fe547f527b10c9290e7b3e836b9a8f1ca04d4d342e85714416b3c2ab74949c9c66f9cebb0473e6bc353b79035356103b47641285d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: eba51182400b9f35b017daa7f419f434424410691bbc5de4f4240cc830fdef906b504424992700dc047f16b4d99100a6f8b8b11175c193f38008e9c96322b6a5
  languageName: node
  linkType: hard

"yaml@npm:^2.3.4":
  version: 2.8.0
  resolution: "yaml@npm:2.8.0"
  bin:
    yaml: bin.mjs
  checksum: 66f103ca5a2f02dac0526895cc7ae7626d91aa8c43aad6fdcff15edf68b1199be4012140b390063877913441aaa5288fdf57eca30e06268a8282dd741525e626
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: ed2d96a616a9e3e1cc7d204c62ecc61f7aaab633dcbfab2c6df50f7f87b393993fe6640d017759fe112d0cb1e0119f2b4150a87305cc873fd90831c6a58ccf1c
  languageName: node
  linkType: hard

"yargs@npm:^17.0.1, yargs@npm:^17.6.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: ^8.0.1
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.3
    y18n: ^5.0.5
    yargs-parser: ^21.1.1
  checksum: 73b572e863aa4a8cbef323dd911d79d193b772defd5a51aab0aca2d446655216f5002c42c5306033968193bdbf892a7a4c110b0d77954a7fdf563e653967b56a
  languageName: node
  linkType: hard

"yauzl@npm:^2.10.0":
  version: 2.10.0
  resolution: "yauzl@npm:2.10.0"
  dependencies:
    buffer-crc32: ~0.2.3
    fd-slicer: ~1.1.0
  checksum: 7f21fe0bbad6e2cb130044a5d1d0d5a0e5bf3d8d4f8c4e6ee12163ce798fee3de7388d22a7a0907f563ac5f9d40f8699a223d3d5c1718da90b0156da6904022b
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"yocto-queue@npm:^1.1.1":
  version: 1.2.1
  resolution: "yocto-queue@npm:1.2.1"
  checksum: 0843d6c2c0558e5c06e98edf9c17942f25c769e21b519303a5c2adefd5b738c9b2054204dc856ac0cd9d134b1bc27d928ce84fd23c9e2423b7e013d5a6f50577
  languageName: node
  linkType: hard

"zod@npm:^3.23.8":
  version: 3.25.76
  resolution: "zod@npm:3.25.76"
  checksum: c9a403a62b329188a5f6bd24d5d935d2bba345f7ab8151d1baa1505b5da9f227fb139354b043711490c798e91f3df75991395e40142e6510a4b16409f302b849
  languageName: node
  linkType: hard
