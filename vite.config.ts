import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  base: './',
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src/ui'), // 👈 fix points to "src/ui"
    },
  },

  build: {
    outDir: 'dist-react',
  },
  server: {
    port: 5132,
    strictPort: true,
  },
})


// import { defineConfig } from 'vite';
// import react from '@vitejs/plugin-react';
// import electron from 'vite-plugin-electron';
// import renderer from 'vite-plugin-electron-renderer';

// export default defineConfig({
//   plugins: [
//     react(),
//     electron([
//       {
//         // Main process
//         entry: 'electron/main.ts',
//       },
//       {
//         // Preload script
//         entry: 'electron/preload.ts',
//         onstart(options) {
//           // Open dev tools or restart Electron
//           options.reload();
//         },
//       },
//     ]),
//     renderer(), // allow `electron`, `ipcRenderer`, etc. in renderer process
//   ],
//   base: './',
//   build: {
//     outDir: 'dist-react',
//   },
//   server: {
//     port: 5132,
//     strictPort: true,
//   },
// });
