# GitHub MCP Server Dockerfile
# This Dockerfile creates a containerized GitHub MCP server for Alpine Intellect

FROM node:22-alpine

# Install system dependencies first
RUN apk add --no-cache \
    git \
    curl \
    ca-certificates

# Create non-root user for security
RUN addgroup -g 1001 -S mcpuser && \
    adduser -S mcpuser -u 1001 -G mcpuser

# Install the GitHub MCP server package globally
RUN npm install -g @modelcontextprotocol/server-github

# Create app directory with proper permissions
RUN mkdir -p /home/<USER>/app && \
    chown -R mcpuser:mcpuser /home/<USER>/app

# Switch to non-root user
USER mcpuser

# Set working directory to user's home
WORKDIR /home/<USER>/app

# Set environment variables
ENV NODE_ENV=production
ENV MCP_TRANSPORT=stdio

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node -e "console.log('GitHub MCP Server is healthy')" || exit 1

# Expose the MCP server
ENTRYPOINT ["npx", "@modelcontextprotocol/server-github"]

# Default command (can be overridden)
CMD []

# Labels for metadata
LABEL maintainer="Alpine Code <<EMAIL>>"
LABEL description="GitHub MCP Server for Alpine Intellect"
LABEL version="1.0.0"
LABEL org.opencontainers.image.source="https://github.com/alpine-code/desktop-app"
LABEL org.opencontainers.image.description="Containerized GitHub MCP server for Alpine Intellect desktop application"
LABEL org.opencontainers.image.licenses="MIT"
