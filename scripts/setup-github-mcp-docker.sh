#!/bin/bash

# GitHub MCP Docker Setup Script
# This script builds and manages the GitHub MCP server Docker image for Alpine Intellect

set -e

# Configuration
IMAGE_NAME="alpine-intellect/github-mcp-server"
IMAGE_TAG="latest"
DOCKERFILE_PATH="docker/Dockerfile.github-mcp-server"
CONTAINER_NAME="alpine-intellect-github-mcp"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed and running
check_docker() {
    log_info "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker Desktop and try again."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker Desktop and try again."
        exit 1
    fi
    
    log_success "Docker is installed and running"
}

# Build the Docker image
build_image() {
    log_info "Building GitHub MCP server Docker image..."
    
    if [ ! -f "$DOCKERFILE_PATH" ]; then
        log_error "Dockerfile not found at $DOCKERFILE_PATH"
        exit 1
    fi
    
    # Build the image
    docker build \
        -f "$DOCKERFILE_PATH" \
        -t "$IMAGE_NAME:$IMAGE_TAG" \
        . || {
        log_error "Failed to build Docker image"
        exit 1
    }
    
    log_success "Docker image built successfully: $IMAGE_NAME:$IMAGE_TAG"
}

# Test the Docker image
test_image() {
    log_info "Testing GitHub MCP server Docker image..."
    
    # Test that the image can start
    local test_container="test-$CONTAINER_NAME-$$"
    
    # Run a quick test
    if docker run --rm --name "$test_container" "$IMAGE_NAME:$IMAGE_TAG" --help &> /dev/null; then
        log_success "Docker image test passed"
    else
        log_error "Docker image test failed"
        exit 1
    fi
}

# Pull the official GitHub MCP server image (alternative to building)
pull_official_image() {
    log_info "Pulling official GitHub MCP server image..."
    
    # Note: This is a placeholder - the actual official image may have a different name
    # For now, we'll use our custom build
    log_warning "Official image not yet available, using custom build"
    build_image
}

# Clean up old images and containers
cleanup() {
    log_info "Cleaning up old Docker resources..."
    
    # Stop and remove any running containers
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_info "Stopping running containers..."
        docker stop $(docker ps -q -f name="$CONTAINER_NAME") || true
    fi
    
    if docker ps -aq -f name="$CONTAINER_NAME" | grep -q .; then
        log_info "Removing old containers..."
        docker rm $(docker ps -aq -f name="$CONTAINER_NAME") || true
    fi
    
    # Remove old images (keep latest)
    local old_images=$(docker images "$IMAGE_NAME" --format "table {{.Repository}}:{{.Tag}}\t{{.ID}}" | grep -v "$IMAGE_TAG" | awk '{print $2}' | tail -n +2)
    if [ ! -z "$old_images" ]; then
        log_info "Removing old images..."
        echo "$old_images" | xargs docker rmi || true
    fi
    
    log_success "Cleanup completed"
}

# Show image information
show_info() {
    log_info "Docker image information:"
    
    if docker images "$IMAGE_NAME:$IMAGE_TAG" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.Size}}\t{{.CreatedAt}}" | grep -q "$IMAGE_NAME"; then
        docker images "$IMAGE_NAME:$IMAGE_TAG" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.Size}}\t{{.CreatedAt}}"
    else
        log_warning "Image $IMAGE_NAME:$IMAGE_TAG not found"
    fi
}

# Run a test container
run_test() {
    log_info "Running test container..."
    
    # Set a test PAT (this should be replaced with a real PAT for actual testing)
    local test_pat="ghp_test1234567890123456789012345678901234567890"
    
    log_warning "Starting test container with dummy PAT..."
    log_warning "This will fail authentication but should show the server starting"
    
    docker run -it --rm \
        --name "test-$CONTAINER_NAME" \
        -e GITHUB_PERSONAL_ACCESS_TOKEN="$test_pat" \
        "$IMAGE_NAME:$IMAGE_TAG" || {
        log_info "Test container exited (expected with dummy PAT)"
    }
}

# Show usage information
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build     Build the GitHub MCP server Docker image"
    echo "  test      Test the Docker image"
    echo "  pull      Pull the official image (or build if not available)"
    echo "  cleanup   Clean up old Docker resources"
    echo "  info      Show image information"
    echo "  run-test  Run a test container"
    echo "  setup     Complete setup (build + test + cleanup)"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup     # Complete setup process"
    echo "  $0 build     # Just build the image"
    echo "  $0 cleanup   # Clean up old resources"
}

# Main execution
main() {
    local command="${1:-setup}"
    
    case "$command" in
        "build")
            check_docker
            build_image
            ;;
        "test")
            check_docker
            test_image
            ;;
        "pull")
            check_docker
            pull_official_image
            ;;
        "cleanup")
            check_docker
            cleanup
            ;;
        "info")
            show_info
            ;;
        "run-test")
            check_docker
            run_test
            ;;
        "setup")
            check_docker
            cleanup
            build_image
            test_image
            show_info
            log_success "GitHub MCP Docker setup completed successfully!"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            log_error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
