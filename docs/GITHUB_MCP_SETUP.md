# GitHub MCP Server Setup Guide

This guide walks you through setting up the GitHub MCP (Model Context Protocol) server integration in Alpine Intellect.

## Prerequisites

### System Requirements

- **Docker Desktop**: Required for running the GitHub MCP server
- **Node.js**: 22.12.0 or higher
- **Alpine Intellect**: Latest version with MCP support

### GitHub Requirements

- **GitHub Account**: Active GitHub account
- **Personal Access Token**: With required scopes (repo, user, read:org)

## Quick Setup

### 1. Install Docker Desktop

Download and install Docker Desktop for your platform:
- **macOS**: [Docker Desktop for Mac](https://docs.docker.com/desktop/install/mac-install/)
- **Windows**: [Docker Desktop for Windows](https://docs.docker.com/desktop/install/windows-install/)
- **Linux**: [Docker Desktop for Linux](https://docs.docker.com/desktop/install/linux-install/)

Ensure Docker is running before proceeding.

### 2. Setup GitHub MCP Docker Image

Run the setup script to build and configure the Docker image:

```bash
# Make the script executable
chmod +x scripts/setup-github-mcp-docker.sh

# Run complete setup
./scripts/setup-github-mcp-docker.sh setup
```

This will:
- Check Docker installation
- Build the GitHub MCP server image
- Test the image
- Clean up old resources

### 3. Create GitHub Personal Access Token

1. **Go to GitHub Settings**:
   - Navigate to [GitHub Settings → Developer settings → Personal access tokens](https://github.com/settings/tokens)

2. **Generate New Token**:
   - Click "Generate new token (classic)"
   - Set a descriptive note: "Alpine Intellect Desktop"
   - Choose expiration (recommended: 90 days or no expiration)

3. **Select Required Scopes**:
   - ✅ `repo` - Full control of private repositories
   - ✅ `user` - Read/write user data  
   - ✅ `read:org` - Read org and team membership

4. **Generate and Copy**:
   - Click "Generate token"
   - **Copy the token immediately** (you won't see it again)

### 4. Connect in Alpine Intellect

1. **Open Alpine Intellect**
2. **Navigate to MCP Services**:
   - Go to Settings → MCP Services
   - Or use the direct menu option

3. **Connect GitHub MCP Server**:
   - Click "Connect" on the GitHub Service Card
   - Paste your Personal Access Token
   - Click "Connect"

4. **Verify Connection**:
   - Status should show "Connected"
   - Available tools should be listed
   - Your GitHub username should appear

## Manual Setup

If you prefer manual setup or encounter issues with the automated script:

### Build Docker Image Manually

```bash
# Build the GitHub MCP server image
docker build -f docker/Dockerfile.github-mcp-server -t alpine-intellect/github-mcp-server:latest .

# Test the image
docker run --rm alpine-intellect/github-mcp-server:latest --help
```

### Verify Docker Setup

```bash
# Check if Docker is running
docker info

# List available images
docker images alpine-intellect/github-mcp-server

# Test container startup
docker run --rm -e GITHUB_PERSONAL_ACCESS_TOKEN=test alpine-intellect/github-mcp-server:latest
```

## Troubleshooting

### Docker Issues

**Docker not found**:
```bash
# Check if Docker is installed
docker --version

# If not installed, download from docker.com
```

**Docker daemon not running**:
```bash
# Start Docker Desktop application
# Or on Linux:
sudo systemctl start docker
```

**Permission denied**:
```bash
# Add user to docker group (Linux)
sudo usermod -aG docker $USER
# Log out and back in
```

### GitHub PAT Issues

**Invalid token format**:
- Ensure token starts with `ghp_` or `github_pat_`
- Token should be at least 40 characters
- Copy token without extra spaces

**Authentication failed**:
- Verify token is still valid on GitHub
- Check if token has been revoked
- Regenerate token if necessary

**Insufficient permissions**:
- Ensure all required scopes are selected:
  - `repo` (for repository access)
  - `user` (for user information)
  - `read:org` (for organization access)

### Connection Issues

**Server won't start**:
```bash
# Check Docker logs
docker logs alpine-intellect-github-mcp

# Verify image exists
docker images alpine-intellect/github-mcp-server
```

**Connection timeout**:
- Check internet connectivity
- Verify GitHub.com is accessible
- Check corporate firewall settings

**Tools not appearing**:
- Wait for server initialization (30-60 seconds)
- Check server status in Alpine Intellect
- Restart connection if needed

## Advanced Configuration

### Custom Docker Configuration

You can customize the Docker setup by modifying the Dockerfile:

```dockerfile
# docker/Dockerfile.github-mcp-server
FROM node:22-alpine

# Add custom configurations here
ENV CUSTOM_CONFIG=value

# Install additional tools if needed
RUN apk add --no-cache git curl
```

### Environment Variables

The GitHub MCP server supports these environment variables:

- `GITHUB_PERSONAL_ACCESS_TOKEN`: Your GitHub PAT (injected by Alpine Intellect)
- `NODE_ENV`: Environment mode (production/development)
- `MCP_TRANSPORT`: Transport protocol (stdio)

### Resource Limits

Configure Docker resource limits if needed:

```bash
# Run with memory limit
docker run --memory=512m alpine-intellect/github-mcp-server:latest

# Run with CPU limit
docker run --cpus=1.0 alpine-intellect/github-mcp-server:latest
```

## Available Tools

Once connected, the GitHub MCP server provides these tools:

### Repository Management
- `create_repository` - Create new repositories
- `fork_repository` - Fork existing repositories
- `search_repositories` - Search for repositories

### Issue Management
- `create_issue` - Create new issues
- `get_issue` - Retrieve issue details
- `update_issue` - Update existing issues

### Pull Request Management
- `create_pull_request` - Create new pull requests
- `get_pull_request` - Retrieve PR details
- `merge_pull_request` - Merge pull requests

### File Operations
- `get_file_contents` - Read file contents
- `create_or_update_file` - Create or update files
- `delete_file` - Delete files

### Branch Management
- `create_branch` - Create new branches
- `get_branch` - Get branch information
- `delete_branch` - Delete branches

## Security Considerations

### Token Security
- **Never commit tokens to version control**
- **Use tokens with minimal required scopes**
- **Regularly rotate tokens (every 90 days)**
- **Revoke tokens when no longer needed**

### Docker Security
- **Images run as non-root user**
- **No persistent storage of credentials**
- **Isolated container environment**
- **Regular image updates recommended**

### Network Security
- **All communication over HTTPS**
- **No open network ports**
- **Stdio transport for local communication**

## Maintenance

### Regular Updates

```bash
# Update the GitHub MCP server package
./scripts/setup-github-mcp-docker.sh build

# Clean up old resources
./scripts/setup-github-mcp-docker.sh cleanup
```

### Monitoring

Check the MCP Services page in Alpine Intellect for:
- Connection status
- Available tools
- Error messages
- Performance metrics

### Logs

Access logs for troubleshooting:

```bash
# Docker container logs
docker logs alpine-intellect-github-mcp

# Alpine Intellect logs
# Check: ~/Library/Logs/alpine-intellect/ (macOS)
# Check: %APPDATA%/alpine-intellect/logs/ (Windows)
# Check: ~/.config/alpine-intellect/logs/ (Linux)
```

## Support

If you encounter issues:

1. **Check this documentation** for common solutions
2. **Review logs** for error details
3. **Verify prerequisites** are met
4. **Test with a fresh PAT** if authentication fails
5. **Contact support** at <EMAIL>

For the latest updates and documentation, visit our [GitHub repository](https://github.com/alpine-code/desktop-app).
