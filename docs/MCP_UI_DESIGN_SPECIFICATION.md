# MCP Management UI Design Specification

## Overview

This document provides detailed UI/UX specifications for the MCP Management module, including wireframes, component designs, and interaction patterns.

## Page Layout

### Main MCP Management Page (`/mcp`)

```
┌─────────────────────────────────────────────────────────────────┐
│ Alpine Intellect - MCP Management                               │
├─────────────────────────────────────────────────────────────────┤
│ [Sidebar]  │ Main Content Area                                  │
│ 🏠 Dashboard│ ┌─────────────────────────────────────────────────┐ │
│ 📁 Projects│ │ MCP Server Management                           │ │
│ 🤖 Models  │ │ ┌─────────────────┐ ┌─────────────────────────┐ │ │
│ 🔌 MCP     │ │ │ Server Store    │ │ Connected Servers       │ │ │
│ 🔍 Search  │ │ │ [Browse/Search] │ │ [Status Dashboard]      │ │ │
│ ⚙️ Settings│ │ └─────────────────┘ └─────────────────────────┘ │ │
│            │ │ ┌─────────────────────────────────────────────┐ │ │
│            │ │ │ Tools & Capabilities                        │ │ │
│            │ │ │ [Available Tools from Connected Servers]   │ │ │
│            │ │ └─────────────────────────────────────────────┘ │ │
│            │ └─────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Component Specifications

### 1. Server Store/Directory Component

**Location**: Left panel of main page
**Purpose**: Browse and discover available MCP servers

#### Layout
```
┌─────────────────────────────────────┐
│ 🔍 Search servers...                │
├─────────────────────────────────────┤
│ Categories:                         │
│ □ All (24)                         │
│ □ Development (8)                  │
│ □ Productivity (6)                 │
│ □ AI/ML (4)                        │
│ □ Data (3)                         │
│ □ Communication (2)                │
│ □ Custom (1)                       │
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │ 🐙 GitHub Integration          │ │
│ │ Repository management          │ │
│ │ ● Connected                    │ │
│ │ [Configure] [Disconnect]       │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ 📊 Figma Design System        │ │
│ │ Design asset management        │ │
│ │ ○ Available                    │ │
│ │ [Install] [Learn More]         │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### Server Card States
- **Available**: Not installed, can be installed
- **Installed**: Installed but not configured
- **Configured**: Configured but not connected
- **Connected**: Active and ready to use
- **Error**: Connection or configuration issues

#### Interaction Patterns
- **Click on server card**: Opens detailed server information modal
- **Install button**: Triggers installation workflow
- **Configure button**: Opens configuration dialog
- **Connect button**: Initiates connection process

### 2. Connected Servers Dashboard

**Location**: Right panel of main page
**Purpose**: Monitor and control connected servers

#### Layout
```
┌─────────────────────────────────────┐
│ Connected Servers (3/5)             │
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │ 🐙 GitHub                      │ │
│ │ ● Running (2.3s latency)       │ │
│ │ 8 tools available              │ │
│ │ [●] [⏸] [⚙️] [📊]              │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ 💬 Slack                       │ │
│ │ ● Connected (1.1s latency)     │ │
│ │ 5 tools available              │ │
│ │ [●] [⏸] [⚙️] [📊]              │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ 📊 Analytics                   │ │
│ │ ⚠️ Connection issues           │ │
│ │ 0 tools available              │ │
│ │ [🔄] [⏸] [⚙️] [📊]              │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### Status Indicators
- **● Green**: Healthy and responsive
- **● Yellow**: Connected but slow/warnings
- **● Red**: Error state or disconnected
- **⚠️**: Warning state with issues

#### Action Buttons
- **[●]**: Connect/Disconnect toggle
- **[⏸]**: Start/Stop server process
- **[⚙️]**: Open configuration
- **[📊]**: View detailed metrics
- **[🔄]**: Restart/Retry connection

### 3. Tools & Capabilities Panel

**Location**: Bottom panel of main page
**Purpose**: Display and manage available tools

#### Layout
```
┌─────────────────────────────────────────────────────────────────┐
│ Available Tools (23)                    🔍 Search tools...      │
├─────────────────────────────────────────────────────────────────┤
│ Filter: [All] [GitHub] [Slack] [Analytics]    Sort: [Name ↓]    │
├─────────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │ 📝 Create   │ │ 🔍 Search   │ │ 📊 Get File │ │ 🔀 Create   │ │
│ │ Repository  │ │ Repos       │ │ Contents    │ │ Branch      │ │
│ │ GitHub      │ │ GitHub      │ │ GitHub      │ │ GitHub      │ │
│ │ Used 12x    │ │ Used 8x     │ │ Used 15x    │ │ Used 3x     │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │ 💬 Send     │ │ 👥 List     │ │ 📋 Create   │ │ 🔔 Set      │ │
│ │ Message     │ │ Channels    │ │ Channel     │ │ Status      │ │
│ │ Slack       │ │ Slack       │ │ Slack       │ │ Slack       │ │
│ │ Used 5x     │ │ Used 2x     │ │ Used 1x     │ │ Used 0x     │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

#### Tool Card Information
- **Tool name and icon**
- **Brief description**
- **Source server**
- **Usage statistics**
- **Last used timestamp**

#### Interaction Patterns
- **Click on tool card**: Opens tool details modal
- **Right-click**: Context menu with options (Test, Documentation, etc.)
- **Drag & drop**: Future feature for workflow creation

## Modal Dialogs

### Server Installation Modal

```
┌─────────────────────────────────────────────────────────────┐
│ Install Figma Design System Server                         │
├─────────────────────────────────────────────────────────────┤
│ Description:                                                │
│ Connect to Figma to access design tokens, components,      │
│ and design system assets directly in your AI workflows.    │
│                                                             │
│ Requirements:                                               │
│ • Node.js 18+ ✓                                           │
│ • 50MB disk space ✓                                       │
│ • Internet connection ✓                                   │
│                                                             │
│ Capabilities:                                               │
│ • Access design tokens                                     │
│ • Retrieve component specifications                        │
│ • Export assets and images                                 │
│ • Sync design system changes                               │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ⚠️ This server requires authentication with Figma      │ │
│ │ You'll need to provide your Figma API token after      │ │
│ │ installation.                                           │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│                                    [Cancel] [Install]      │
└─────────────────────────────────────────────────────────────┘
```

### Server Configuration Modal

```
┌─────────────────────────────────────────────────────────────┐
│ Configure GitHub Integration                                │
├─────────────────────────────────────────────────────────────┤
│ Connection Settings                                         │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Server Type: ● Local Process  ○ Remote Endpoint        │ │
│ │                                                         │ │
│ │ Command: npx @modelcontextprotocol/server-github       │ │
│ │ Arguments: [                                    ]       │ │
│ │ Working Directory: /usr/local/lib/node_modules/...     │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Authentication                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ GitHub Personal Access Token                            │ │
│ │ [●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●] [👁]        │ │
│ │                                                         │ │
│ │ Required Scopes: repo, user, read:org                  │ │
│ │ Status: ✓ Valid token with required permissions        │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Advanced Settings                                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ☑ Auto-start with application                          │ │
│ │ ☑ Restart on failure                                   │ │
│ │ ☐ Enable debug logging                                 │ │
│ │                                                         │ │
│ │ Timeout: [30] seconds                                   │ │
│ │ Max retries: [3]                                        │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│                          [Test Connection] [Cancel] [Save] │
└─────────────────────────────────────────────────────────────┘
```

### Tool Details Modal

```
┌─────────────────────────────────────────────────────────────┐
│ 📝 Create Repository Tool                                   │
├─────────────────────────────────────────────────────────────┤
│ Server: GitHub Integration                                  │
│ Category: Repository Management                             │
│ Last Used: 2 hours ago                                      │
│ Usage Count: 12 times                                       │
│                                                             │
│ Description:                                                │
│ Creates a new GitHub repository with specified settings    │
│ including visibility, initialization options, and basic    │
│ configuration.                                              │
│                                                             │
│ Parameters:                                                 │
│ • name (required): Repository name                         │
│ • description (optional): Repository description           │
│ • private (boolean): Make repository private               │
│ • auto_init (boolean): Initialize with README              │
│ • gitignore_template (optional): Gitignore template        │
│ • license_template (optional): License template            │
│                                                             │
│ Example Usage:                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ {                                                       │ │
│ │   "name": "my-new-project",                            │ │
│ │   "description": "A sample project",                   │ │
│ │   "private": false,                                    │ │
│ │   "auto_init": true,                                   │ │
│ │   "gitignore_template": "Node"                         │ │
│ │ }                                                       │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│                              [Test Tool] [Close]           │
└─────────────────────────────────────────────────────────────┘
```

## Color Scheme & Visual Design

### Status Colors
- **Green (#10B981)**: Healthy, connected, success states
- **Yellow (#F59E0B)**: Warning, slow response, attention needed
- **Red (#EF4444)**: Error, disconnected, failure states
- **Blue (#3B82F6)**: Information, primary actions
- **Gray (#6B7280)**: Inactive, disabled, secondary information

### Typography
- **Headers**: Inter, 18-24px, font-weight: 600
- **Body text**: Inter, 14-16px, font-weight: 400
- **Labels**: Inter, 12-14px, font-weight: 500
- **Code/Technical**: JetBrains Mono, 12-14px

### Spacing
- **Component padding**: 16px
- **Card margins**: 12px
- **Button padding**: 8px 16px
- **Input padding**: 8px 12px

## Responsive Design

### Desktop (1200px+)
- Three-column layout: Sidebar (240px) + Server Store (400px) + Connected Servers (flexible)
- Tools panel spans full width below

### Tablet (768px - 1199px)
- Two-column layout: Sidebar (collapsed) + Main content
- Server store and connected servers stack vertically
- Tools panel remains full width

### Mobile (< 768px)
- Single column layout
- Sidebar becomes slide-out menu
- All panels stack vertically
- Touch-optimized button sizes (44px minimum)

## Accessibility

### Keyboard Navigation
- Tab order: Sidebar → Server Store → Connected Servers → Tools
- Arrow keys for navigation within components
- Enter/Space for activation
- Escape to close modals

### Screen Reader Support
- Semantic HTML structure
- ARIA labels for interactive elements
- Live regions for status updates
- Descriptive alt text for icons

### Visual Accessibility
- High contrast mode support
- Focus indicators (2px blue outline)
- Minimum 4.5:1 color contrast ratio
- Scalable text up to 200%
