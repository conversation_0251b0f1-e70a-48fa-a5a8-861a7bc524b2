const fs = require("fs-extra");
const path = require("path");

// Copy .scm files
const srcScmDir = path.resolve("src/electron/core/tree-sitter");
const destScmDir = path.resolve("dist-electron/core/tree-sitter");

const copyScmFiles = fs.copy(srcScmDir, destScmDir, {
  filter: (src) => src.endsWith(".scm") || fs.statSync(src).isDirectory(),
});

// Run both
Promise.all([copyScmFiles])
  .then(() => {
    console.log("✅ Copied .scm and files successfully!");
  })
  .catch((err) => {
    console.error("❌ Error copying files:", err);
    process.exit(1);
  });
