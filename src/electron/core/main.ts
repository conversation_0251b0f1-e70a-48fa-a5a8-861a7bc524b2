// src/index.ts

import { CodebaseIndexer } from "./indexing/index-initializer.js";
import { closeDb } from "./rds-db/db.js";
import { ProgressYield, SearchResult, SnippetResult } from "./types.js";
import { promises as fs } from "fs";
import path from 'path'
import os from 'os'
import stateManager from "../global-state.js";

let elapsed = 0;
const intervalMs = 1000;
const timeoutMs = 60000;

export const indexer = new CodebaseIndexer();

export const startIndexing = async (dirPath: string) => {
    try {
        console.log("Starting indexing...");
        const debugDir = path.join(os.homedir(), ".ain-index");
        await fs.mkdir(debugDir, { recursive: true });
        console.log("Created .ain-debug dir..", debugDir);
        // await indexer.clearIndexes();

        await indexer.refreshCodebaseIndex([dirPath]);

        console.log("Indexing complete.");
    } catch (error) {
        console.log("Error while initializing indexing...", error);
    }
};

export const startReindexingFromRenderer = async (dirPath: string) => {
    try {
        void (async () => {
            try {
                if (stateManager.hasActive()) {
                    stateManager.enqueue(dirPath);
                } else {
                    stateManager.setActive(dirPath);
                    startIndexing(dirPath);
                }
            } catch (e) {
                console.log(
                    "Error in /ain/start-indexing initialization...",
                    e
                );
            }
        })();
    } catch (error) {
        console.log("Error while reindexing...", error);
    }
};

export const latestIndexingStatus = (folderPath: string): ProgressYield => {
    return stateManager.indexingSessions[folderPath];
};

export const cancelIndexing = async (dirPath: string, currentProgress: number = 0) => {
    console.log({ cancelIndexingPathh: dirPath });
    try {
        indexer.indexingCancellationController?.abort();
        stateManager.removeFromQueue(dirPath);
        stateManager.indexingSessions[dirPath] = {
            progress: currentProgress,
            desc: "Indexing got cancelled...",
            status: "resetting",
        };
        return true;
    } catch (error) {
        console.log(
            `Error while cancelling the indexing for ${dirPath}...`,
            error
        );
        return false;
    }
};

async function main(): Promise<void> {
    const command = process.argv[2];
    const codebasePath = process.argv[3];

    console.log(command,codebasePath)

    if (!command) {
        console.error(
            "Usage: node src/main.js [index|search [fts|vector|snippets] <query>]"
        );
        process.exit(1);
    }

    if (command === "index") {
        console.log("Starting indexing...");
        await indexer.clearIndexes();
        await indexer.refreshCodebaseIndex([codebasePath]);
        console.log("Indexing complete.");
    } else if (command === "search") {
        const type =
            (process.argv[3] as "fts" | "vector" | "snippets") || "fts";
        const query = process.argv[4] || "";

        if (!query && type !== "snippets") {
            console.error(
                "Please provide a search query (except for snippets)."
            );
            process.exit(1);
        }

        // Use any for results if types are not defined in your implementation
        const results: any[] = await indexer.search(codebasePath,query, 10, type);

        if (type === "snippets") {
            console.log(`Found ${results.length} snippets:`);
            results.forEach((result: SnippetResult, i: number) => {
                console.log(`\nSnippet ${i + 1}:`);
                console.log(`Title: ${result.title}`);
                console.log(`Path: ${result.description}`);
                console.log(`ID: ${result.id}\n`);
            });
        } else {
            console.log(`Found ${results.length} results:`);
            results.forEach((result: SearchResult, i: number) => {
                console.log(`\nResult ${i + 1}:`);
                console.log(`File: ${result.filepath}`);
                console.log(
                    `Lines: ${result.startLine + 1}-${result.endLine + 1}`
                );
                console.log(`Content:\n${result.content}\n`);
            });
        }
    } else {
        console.error(
            "Usage: node src/index.js [index|search [fts|vector|snippets] <query>]"
        );
        process.exit(1);
    }

    closeDb();
}

// main().catch((err: unknown) => {
//     console.error("Error:", err);
//     closeDb();
//     process.exit(1);
// });
