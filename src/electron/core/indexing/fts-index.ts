import {
    BranchAndDir,
    Chunk,
    IndexResultType,
    MarkCompleteFn,
    ProgressYield,
    RetrieveConfig,
    Tag,
    UpdateResults,
} from "../types.js";
import { getDb } from "../rds-db/db.js";
import { countTokensAsync, getUriPathBasename, tagToString } from "../utils.js";
import { CodeSnippetsCodebaseIndex } from "./snippets-index.js";
import stateManager from "../../global-state.js";
import log from "electron-log";

export class FullTextSearchCodebaseIndex {
    static artifactId = "fts";
    artifactId = FullTextSearchCodebaseIndex.artifactId;
    relativeExpectedTime = 0.2;

    buildTagFilter(tags: Tag[]): string {
        const tagStrings = tags.map(tagToString);
        return `AND ain_code_snippets_tags.tag IN (${tagStrings
            .map(() => "?")
            .join(",")})`;
    }

    buildPathFilter(filterPaths?: string[]): string {
        if (!filterPaths || filterPaths.length === 0) {
            return "";
        }
        return `AND ain_fts_metadata.path IN (${filterPaths
            .map(() => "?")
            .join(",")})`;
    }

    buildRetrieveQuery(config: RetrieveConfig): string {
        return `
            SELECT ain_fts_metadata.snippetId, ain_fts_metadata.path, ain_fts.content, rank
            FROM ain_fts
            JOIN ain_fts_metadata ON ain_fts.rowid = ain_fts_metadata.fts_rowid
            JOIN ain_code_snippets_tags ON ain_fts_metadata.snippetId = ain_code_snippets_tags.snippetId
            WHERE ain_fts MATCH ?
            ${this.buildTagFilter(config.tags)}
            ${this.buildPathFilter(config.filterPaths)}
            ORDER BY bm25(ain_fts, ${
                stateManager.retrievalParams.ftsPathWeight
            },${stateManager.retrievalParams.ftsContentWeight})
            LIMIT ?
            `;
    }

    async *update(
        tag: Tag,
        results: UpdateResults,
        markComplete: MarkCompleteFn,
        repoName: string
    ): AsyncGenerator<ProgressYield, void, unknown> {
        const db = getDb();
        let accumulatedProgress = 0;

        // Handle compute results
        for (let i = 0; i < results.compute.length; i++) {
            const item = results.compute[i];
            try {
                // Insert chunks
                const chunks = db
                    .prepare(
                        "SELECT * FROM ain_code_snippets WHERE path = ? AND cacheKey = ?"
                    )
                    .all(item.path, item.cacheKey) as Chunk[];

                const insertFts = db.prepare(
                    "INSERT INTO ain_fts (path, content) VALUES (?, ?)"
                );
                const insertMeta = db.prepare(
                    `INSERT INTO ain_fts_metadata (fts_rowid, path, cacheKey, snippetId) 
                    VALUES (?, ?, ?, ?)
                    ON CONFLICT(fts_rowid) DO UPDATE SET
                    path = excluded.path,
                    cacheKey = excluded.cacheKey,
                    snippetId = excluded.snippetId`
                );

                const transaction = db.transaction(() => {
                    for (const chunk of chunks) {
                        // Insert into fts first and get lastID
                        const result = insertFts.run(item.path, chunk.content);
                        const ftsRowid = result.lastInsertRowid;
                        // Then insert into fts_metadata using the FTS rowid
                        insertMeta.run(
                            ftsRowid,
                            item.path,
                            item.cacheKey,
                            chunk.id
                        );
                    }
                });
                transaction();

                yield {
                    progress:
                        accumulatedProgress +
                        (i + 1) / results.compute.length / 2,
                    desc: `Indexing ${getUriPathBasename(
                        item.path
                    )} for full-text search`,
                    status: "indexing",
                };
                await markComplete([item], IndexResultType.Compute);
            } catch (e: any) {
                console.error(`Failed to index ${item.path}: ${e.message}`);
                yield {
                    progress: accumulatedProgress,
                    desc: `Failed indexing ${item.path}: ${e.message}`,
                    status: "failed",
                };
            }
        }

        // // Handle addTag results
        // for (const item of results.addTag) {
        //     try {
        //         const chunks = db
        //             .prepare("SELECT id FROM ain_chunks WHERE cacheKey = ?")
        //             .all(item.cacheKey) as Chunk[];
        //         const insertMeta = db.prepare(
        //             "INSERT INTO ain_fts_metadata (fts_rowid, path, cacheKey, chunkId) VALUES (?, ?, ?, ?)"
        //         );
        //         const transaction = db.transaction(() => {
        //             for (const chunk of chunks) {
        //                 // Find the FTS rowid for this path and chunk content
        //                 const chunkData = db
        //                     .prepare(
        //                         "SELECT content FROM ain_chunks WHERE id = ?"
        //                     )
        //                     .get(chunk.id) as { content: string } | undefined;

        //                 if (chunkData) {
        //                     const existing = db
        //                         .prepare(
        //                             "SELECT rowid FROM ain_fts WHERE path = ? AND content = ?"
        //                         )
        //                         .get(item.path, chunkData.content) as
        //                         | { rowid: number }
        //                         | undefined;

        //                     if (existing) {
        //                         insertMeta.run(
        //                             existing.rowid,
        //                             item.path,
        //                             item.cacheKey,
        //                             chunk.id
        //                         );
        //                     } else {
        //                         console.warn(
        //                             `No fts entry for ${item.path} with matching content, skipping metadata insertion`
        //                         );
        //                     }
        //                 }
        //             }
        //         });
        //         transaction();
        //         await markComplete([item], IndexResultType.AddTag);
        //         accumulatedProgress += 1 / (results.addTag.length || 1) / 4;
        //         yield {
        //             progress: accumulatedProgress,
        //             desc: `Adding ${getUriPathBasename(item.path)} to FTS`,
        //             status: "indexing",
        //         };
        //     } catch (e: any) {
        //         console.error(
        //             `Failed to addTag for ${item.path}: ${e.message}`
        //         );
        //     }
        // }

        // // Handle removeTag results
        // for (const item of results.removeTag) {
        //     try {
        //         db.prepare(
        //             "DELETE FROM ain_fts_metadata WHERE cacheKey = ? AND path = ?"
        //         ).run(item.cacheKey, item.path);
        //         await markComplete([item], IndexResultType.RemoveTag);
        //         accumulatedProgress += 1 / (results.removeTag.length || 1) / 4;
        //         yield {
        //             progress: accumulatedProgress,
        //             desc: `Removing ${getUriPathBasename(item.path)} from FTS`,
        //             status: "indexing",
        //         };
        //     } catch (e: any) {
        //         console.error(
        //             `Failed to removeTag for ${item.path}: ${e.message}`
        //         );
        //     }
        // }

        // Add tag
        for (const item of results.addTag) {
            await markComplete([item], IndexResultType.AddTag);
        }

        // Remove tag
        for (const item of results.removeTag) {
            await markComplete([item], IndexResultType.RemoveTag);
        }

        // // Handle del results
        // for (const item of results.del) {
        //     try {
        //         // Delete from FTS using the rowids from metadata
        //         db.prepare(
        //             "DELETE FROM ain_fts WHERE rowid IN (SELECT fts_rowid FROM ain_fts_metadata WHERE path = ? AND cacheKey = ?)"
        //         ).run(item.path, item.cacheKey);

        //         // Delete metadata entries
        //         db.prepare(
        //             "DELETE FROM ain_fts_metadata WHERE path = ? AND cacheKey = ?"
        //         ).run(item.path, item.cacheKey);

        //         await markComplete([item], IndexResultType.Delete);
        //         accumulatedProgress += 1 / (results.del.length || 1) / 4;
        //         yield {
        //             progress: accumulatedProgress,
        //             desc: `Deleting ${getUriPathBasename(item.path)} from FTS`,
        //             status: "indexing",
        //         };
        //     } catch (e: any) {
        //         console.error(`Failed to delete ${item.path}: ${e.message}`);
        //     }
        // }

        // Delete
        for (const item of results.del) {
            await markComplete([item], IndexResultType.Delete);
        }
    }

    private getRetrieveQueryParameters(config: RetrieveConfig) {
        const { text, tags, filterPaths, n } = config;
        const tagStrings = this.convertTags(tags);

        return [
            text.replace(/\?/g, ""),
            ...tagStrings,
            ...(filterPaths || []),
            Math.ceil(n),
        ];
    }

    private convertTags(tags: BranchAndDir[]): string[] {
        // Notice that the "chunks" artifactId is used because of linking between tables
        return tags.map((tag) =>
            // tagToString({ ...tag, artifactId: ChunkCodebaseIndex.artifactId })
            tagToString({
                ...tag,
                artifactId: CodeSnippetsCodebaseIndex.artifactId,
            })
        );
    }

    async retrieve(config: RetrieveConfig): Promise<Chunk[]> {
        const db = getDb();

        const query = this.buildRetrieveQuery(config);
        const parameters = this.getRetrieveQueryParameters(config);

        const stmt = db.prepare(query);
        const query_results = stmt.all(parameters);

        const results = query_results.filter(
            (result: any) =>
                result.rank <= stateManager.retrievalParams.bm25Threshold
        );

        const snippetIds = results.map((result: any) => result.snippetId);
        let chunks: any[] = [];
        if (snippetIds.length > 0) {
            chunks = db
                .prepare(
                    `SELECT * FROM ain_code_snippets WHERE id IN (${snippetIds
                        .map(() => "?")
                        .join(",")})`
                )
                .all(...snippetIds);
        }

        // return chunks?.map((chunk: any, index: number) => ({
        //     filepath: chunk.path,
        //     index,
        //     startLine: chunk.startLine,
        //     endLine: chunk.endLine,
        //     content: chunk.content,
        //     digest: chunk.cacheKey,
        // }));

        let totalTokens = 0;
        const tokenBudgetForFTS = stateManager.retrievalParams.ftsTokenBudget;
        const selectedChunks = [];
        for (const [index, chunk] of chunks.entries()) {
            const chunkTokens = await countTokensAsync(chunk.content);
            if (totalTokens + chunkTokens > tokenBudgetForFTS) break;
            selectedChunks.push({
                filepath: chunk.path,
                index,
                startLine: chunk.startLine,
                endLine: chunk.endLine,
                content: chunk.content,
                digest: chunk.cacheKey,
            });
            totalTokens += chunkTokens;
        }

        log.debug({ ftsTotalTokens: totalTokens });

        return selectedChunks;
    }
}
