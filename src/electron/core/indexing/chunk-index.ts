import path from "path";
import { Database } from "better-sqlite3"; 
import { getDb } from "../rds-db/db.js";
import { chunkDocument, shouldChunk } from "./chunk/code.js";
import { getUriPathBasename, tagToString } from "../utils.js";
import {
    Chunk,
    ChunkRow,
    IndexResultType,
    MarkCompleteFn,
    ProgressYield,
    ReadFileFn,
    ResultItem,
    Results,
    Tag,
} from "../types.js";
import { DELETE_CHUNK_ROW, SELECT_FTS_ROW } from "../queries.js";

export class ChunkCodebaseIndex {
    static artifactId = "chunks";
    artifactId = ChunkCodebaseIndex.artifactId;
    relativeExpectedTime = 1;

    private readFile: ReadFileFn;
    private maxChunkSize: number;

    constructor(readFile: ReadFileFn, maxChunkSize = 1000) {
        this.readFile = readFile;
        this.maxChunkSize = maxChunkSize;
    }

    async *update(
        tag: Tag,
        results: Results,
        markComplete: MarkCompleteFn,
        repoName: string
    ): AsyncGenerator<ProgressYield, void, unknown> {
        const db: Database = getDb();
        const tagString = tagToString(tag);
        let accumulatedProgress = 0;

        if (results.compute.length > 0) {
            const filepath = results.compute[0].path;
            const folderName = getUriPathBasename(path.dirname(filepath));
            yield {
                desc: `Chunking files in ${folderName}`,
                status: "indexing",
                progress: accumulatedProgress,
            };

            const chunks = await this.computeChunks(results.compute);
            await this.insertChunks(db, tagString, chunks);
            await markComplete(results.compute, IndexResultType.Compute);
        }

        for (const item of results.addTag) {
            const chunks = db
                .prepare("SELECT id FROM ain_chunks WHERE cacheKey = ?")
                .all(item.cacheKey);
            for (const chunk of chunks as ChunkRow[]) {
                db.prepare(
                    "INSERT INTO ain_chunk_tags (chunkId, tag) VALUES (?, ?)"
                ).run(chunk.id, tagString);
            }
            await markComplete([item], IndexResultType.AddTag);
            accumulatedProgress += 1 / results.addTag.length / 4;
            yield {
                progress: accumulatedProgress,
                desc: `Adding ${getUriPathBasename(item.path)}`,
                status: "indexing",
            };
        }

        for (const item of results.removeTag) {
            db.prepare(
                "DELETE FROM ain_chunk_tags WHERE tag = ? AND chunkId IN (SELECT id FROM ain_chunks WHERE cacheKey = ? AND path = ?)"
            ).run(tagString, item.cacheKey, item.path);
            await markComplete([item], IndexResultType.RemoveTag);
            accumulatedProgress += 1 / results.removeTag.length / 4;
            yield {
                progress: accumulatedProgress,
                desc: `Removing ${getUriPathBasename(item.path)}`,
                status: "indexing",
            };
        }

        for (const item of results.del) {
            const chunk = (await db
                .prepare("SELECT id FROM ain_chunks WHERE cacheKey = ?")
                .get(item.cacheKey)) as ChunkRow;
            try {
                if (chunk) {
                    // 🧼 Clean up FTS using fts_rowid from metadata
                    const ftsRows = db.prepare(SELECT_FTS_ROW).all(chunk.id);

                    if (ftsRows.length > 0) {
                        const rowIds = ftsRows.map((r: any) => r.fts_rowid);
                        const placeholders = rowIds.map(() => "?").join(",");
                        db.prepare(
                            `DELETE FROM ain_fts WHERE rowid IN (${placeholders})`
                        ).run(...rowIds);
                        console.log(
                            `[DEBUG] Deleted ${rowIds.length} row(s) from ain_fts`
                        );
                    }

                    db.prepare(DELETE_CHUNK_ROW).run(chunk.id);
                }
            } catch (error) {
                console.log(
                    "Error in deleting ain_chunks and ain_chunk_tags....",
                    error
                );
            }
            await markComplete([item], IndexResultType.Delete);
            accumulatedProgress += 1 / results.del.length / 4;
            yield {
                progress: accumulatedProgress,
                desc: `Removing ${getUriPathBasename(item.path)}`,
                status: "indexing",
            };
        }
    }

    async computeChunks(paths: ResultItem[]): Promise<Chunk[]> {
        const chunks: Chunk[] = [];
        for (const pack of paths) {
            const contents = await this.readFile(pack.path);
            const isChunkable = shouldChunk(pack.path, contents);
            if (!isChunkable) continue;
            for await (const chunk of chunkDocument({
                filepath: pack.path,
                contents,
                maxChunkSize: this.maxChunkSize,
                digest: pack.cacheKey,
            })) {
                chunks.push(chunk as Chunk);
            }
        }
        return chunks;
    }

    async insertChunks(
        db: Database,
        tagString: string,
        chunks: Chunk[]
    ): Promise<void> {
        try {
            const insertChunk = db.prepare(
                "INSERT INTO ain_chunks (cacheKey, path, idx, startLine, endLine, content) VALUES (?, ?, ?, ?, ?, ?)"
            );
            const insertTag = db.prepare(
                "INSERT INTO ain_chunk_tags (chunkId, tag) VALUES (?, ?)"
            );
            const transaction = db.transaction(() => {
                for (const chunk of chunks) {
                    const result = insertChunk.run(
                        chunk.digest,
                        chunk.filepath,
                        chunk.index,
                        chunk.startLine,
                        chunk.endLine,
                        chunk.content
                    );
                    const lastInsertRowid = (result as any).lastInsertRowid;
                    insertTag.run(lastInsertRowid, tagString);
                }
            });
            transaction();
        } catch (error) {
            console.log("Error:insertChunks", error);
        }
    }
}

export default ChunkCodebaseIndex;
