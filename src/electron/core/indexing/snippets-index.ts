import Parser from "tree-sitter";
import { getDb } from "../rds-db/db.js";
import {
    tagToString,
    getFullLanguageName,
    getUriPathBasename,
} from "../utils.js";
import { getParserForFile, getQueryForFile } from "../shared/tree-sitter.js";
import {
    IndexResultType,
    MarkCompleteFn,
    Match,
    ResultItem,
    Results,
    Snippet,
} from "../types.js";
import { loadNativeLanguage } from "../tree-sitter/tree-sitter-languages.js";

export class CodeSnippetsCodebaseIndex {
    static artifactId = "codeSnippets";
    artifactId = CodeSnippetsCodebaseIndex.artifactId;
    relativeExpectedTime = 4;
    readFile: (path: string) => Promise<string>;
    parsers: Record<string, any>;

    constructor(readFile: (path: string) => Promise<string>) {
        this.readFile = readFile;
        this.parsers = {};
    }

    async initParser(filepath: string): Promise<Parser | null> {
        const language = getFullLanguageName(filepath);
        if (!language) return null;

        if (!this.parsers[language]) {
            const langModule = await loadNativeLanguage(language);
            if (!langModule) return null;

            try {
                const parser = new Parser();
                parser.setLanguage(langModule);
                this.parsers[language] = parser;
            } catch (e: any) {
                console.warn(
                    `Failed to initialize parser for ${language}: ${e.message}`
                );
                return null;
            }
        }

        return this.parsers[language];
    }

    async getSnippets(filepath: string, content: string): Promise<Snippet[]> {
        const parser = await getParserForFile(filepath);
        if (!parser) return [];

        try {
            const tree = parser.parse(content);
            const language = getFullLanguageName(filepath);
            const query = await getQueryForFile(filepath, `${language}.scm`);
            const matches = query?.matches(tree.rootNode);

            if (!matches?.length) {
                return this.extractFunctionsAndClasses(tree, content, filepath);
                // return []
            }

            return matches.map(this.getSnippetsFromMatch);
        } catch (e: any) {
            console.warn(
                `Failed to parse snippets for ${filepath}: ${e.message}`
            );
            return [];
        }
    }

    extractFunctionsAndClasses(
        tree: any,
        content: string,
        filepath: string
    ): Snippet[] {
        const snippets: Snippet[] = [];
        const nodeTypes = [
            "function_declaration",
            "function_definition",
            "method_definition",
            "arrow_function",
            "function_expression",
            "class_declaration",
            "class_definition",
        ];

        const traverse = (node: any) => {
            if (nodeTypes.includes(node.type)) {
                const snippetContent = node.text;
                const title = snippetContent
                    .split("\n")[0]
                    .trim()
                    .substring(0, 50);
                const signature =
                    snippetContent.match(/^[^{]+/)?.[0].trim() || title;
                snippets.push({
                    title,
                    content: snippetContent,
                    signature,
                    startLine: node.startPosition.row,
                    endLine: node.endPosition.row,
                });
            }
            for (const child of node.children) traverse(child);
        };

        traverse(tree.rootNode);
        return snippets;
    }

    getSnippetsFromMatch(match: Match): Snippet {
        const bodyTypes = ["interface_declaration", "struct_item", "type_spec"];
        const bodyPrefixes = ["definition", "reference"];

        let title = "",
            content = "",
            signature = "",
            startLine = 0,
            endLine = 0,
            seenBody = false;

        for (const { name, node } of match.captures) {
            const prefix = name.split(".")[0];
            const text = node.text;
            if (bodyPrefixes.includes(prefix)) {
                if (bodyTypes.includes(node.type)) {
                    signature = text;
                    seenBody = true;
                }
                content = text;
                startLine = node.startPosition.row;
                endLine = node.endPosition.row;
            } else {
                if (prefix === "name") title = text;
                if (!seenBody)
                    signature += text + (prefix === "comment" ? "\n" : " ");
            }
        }

        return { title, content, signature, startLine, endLine };
    }

    async getSnippetsForFile(
        path: string,
        cacheKey: string
    ): Promise<Snippet[]> {
        try {
            const content = await this.readFile(path);
            const fileSnippets = await this.getSnippets(path, content);
            return fileSnippets.map((snippet) => ({
                ...snippet,
                cacheKey,
                path,
            }));
        } catch (e: any) {
            console.warn(`Skipping snippets for ${path}: ${e.message}`);
            return [];
        }
    }

    async insertSnippets(
        db: any,
        tagString: string,
        snippets: Snippet[]
    ): Promise<void> {
        const insertSnippet = db.prepare(`
            INSERT OR IGNORE INTO ain_code_snippets
            (path, cacheKey, content, title, signature, startLine, endLine)
            VALUES (?, ?, ?, ?, ?, ?, ?)`);

        const insertTag = db.prepare(`
            INSERT OR IGNORE INTO ain_code_snippets_tags (snippetId, tag)
            VALUES (?, ?)`);

        const transaction = db.transaction(() => {
            for (const snippet of snippets) {
                if (!snippet.path || !snippet.cacheKey || !snippet.content)
                    continue;
                const result = insertSnippet.run(
                    snippet.path,
                    snippet.cacheKey,
                    snippet.content,
                    snippet.title || "",
                    snippet.signature || "",
                    snippet.startLine,
                    snippet.endLine
                );
                if (result.changes > 0) {
                    insertTag.run(result.lastInsertRowid, tagString);
                }
            }
        });

        transaction();
    }

    async *update(
        tag: any,
        results: Results,
        markComplete: MarkCompleteFn,
        repoName: string
    ): AsyncGenerator<any, void, unknown> {
        const db = getDb();
        const tagString = tagToString(tag);
        let progress = 0;

        // Compute
        for (let i = 0; i < results.compute.length; i++) {
            const compute = results.compute[i];
            const snippets = await this.getSnippetsForFile(
                compute.path,
                compute.cacheKey
            );
            if (snippets.length)
                await this.insertSnippets(db, tagString, snippets);

            yield {
                desc: `Indexing ${getUriPathBasename(compute.path)}`,
                progress: i / results.compute.length,
                status: "indexing",
            };
            await markComplete([compute], IndexResultType.Compute);
        }

        // Delete
        for (const item of results.del) {
            db.prepare(
                "DELETE FROM ain_code_snippets_tags WHERE snippetId IN (SELECT id FROM ain_code_snippets WHERE cacheKey = ? AND path = ?)"
            ).run(item.cacheKey, item.path);
            db.prepare(
                "DELETE FROM ain_code_snippets WHERE cacheKey = ? AND path = ?"
            ).run(item.cacheKey, item.path);
            await markComplete([item], IndexResultType.Delete);
        }

        // Add tag
        for (const item of results.addTag) {
            const snippets = await this.getSnippetsForFile(
                item.path,
                item.cacheKey
            );
            if (snippets.length)
                await this.insertSnippets(db, tagString, snippets);
            await markComplete([item], IndexResultType.AddTag);
        }

        // Remove tag
        for (const item of results.removeTag) {
            db.prepare(
                "DELETE FROM ain_code_snippets_tags WHERE tag = ? AND snippetId IN (SELECT id FROM ain_code_snippets WHERE cacheKey = ? AND path = ?)"
            ).run(tagString, item.cacheKey, item.path);
            await markComplete([item], IndexResultType.RemoveTag);
        }
    }

    static async getAll(tags: any): Promise<any[]> {
        const db = getDb();
        const results: any[] = [];
        const seen = new Set();

        for (const tag of [tags]) {
            const tagString = tagToString(tag);
            const snippets = db
                .prepare(
                    `
                SELECT s.path, s.cacheKey, s.content, s.title, s.signature, s.startLine, s.endLine
                FROM ain_code_snippets s
                JOIN ain_code_snippets_tags t ON s.id = t.snippetId
                WHERE t.tag = ?`
                )
                .all(tagString);

            for (const snippet of snippets as Snippet[]) {
                const key = `${snippet.path}:${snippet.startLine}:${snippet.endLine}`;
                if (!seen.has(key)) {
                    seen.add(key);
                    results.push({
                        filepath: snippet.path,
                        digest: snippet.cacheKey,
                        startLine: snippet.startLine,
                        endLine: snippet.endLine,
                        content: snippet.content,
                        title: snippet.title,
                        signature: snippet.signature,
                    });
                }
            }
        }

        return results;
    }

    static getPathsAndSignatures(
        workspaceDirs: string[],
        uriOffset = 0,
        uriBatchSize = 100,
        snippetOffset = 0,
        snippetBatchSize = 100
    ): {
        groupedByUri: Record<string, string[]>;
        hasMoreUris: boolean;
        hasMoreSnippets: boolean;
    } {
        const db = getDb();
        const endIndex = uriOffset + uriBatchSize;
        const uriBatch = workspaceDirs.slice(uriOffset, endIndex);
        const likePatterns = uriBatch.map((dir) => `${dir}%`);
        const placeholders = likePatterns.map(() => "?").join(" OR path LIKE ");

        const query = `
            SELECT DISTINCT path, signature
            FROM ain_code_snippets
            WHERE path LIKE ${placeholders}
            ORDER BY path, signature
            LIMIT ? OFFSET ?`;

        const rows = db
            .prepare(query)
            .all(...likePatterns, snippetBatchSize, snippetOffset);

        const groupedByUri: Record<string, string[]> = {};
        for (const { path, signature } of rows as {
            path: string;
            signature: string;
        }[]) {
            if (!groupedByUri[path]) groupedByUri[path] = [];
            groupedByUri[path].push(signature);
        }

        return {
            groupedByUri,
            hasMoreUris: endIndex < workspaceDirs.length,
            hasMoreSnippets: rows.length === snippetBatchSize,
        };
    }
}
