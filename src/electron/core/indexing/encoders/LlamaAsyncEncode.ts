// import workerpool from "workerpool";
// import path from "path";

// import { fileURLToPath } from "url";

// const __filename = fileURLToPath(import.meta.url);
// const __dirname = path.dirname(__filename);

// export class LlamaAsyncEncoder {
//     public workerPool;
//     constructor() {
//         this.workerPool = workerpool.pool(
//             workerCodeFilePath("./tiktokenWorkerPool.js")
//         );
//     }
//     async encode(text: string): Promise<any> {
//         return this.workerPool.exec("encode", [text]);
//     }

//     async decode(tokens: any[]): Promise<string> {
//         return this.workerPool.exec("decode", [tokens]);
//     }

//     async close(): Promise<void> {
//         await this.workerPool.terminate();
//     }
// }

// function workerCodeFilePath(workerFileName: string): string {
//     return path.join(__dirname, workerFileName);
// }

import workerpool from "workerpool";
import path from "path";
import { fileURLToPath } from "url";
import log from "electron-log";

export class LlamaAsyncEncoder {
    public workerPool;

    constructor() {
        this.workerPool = workerpool.pool(
            getWorkerPath("tiktokenWorkerPool.js")
        );
    }

    async encode(text: string): Promise<any> {
        return this.workerPool.exec("encode", [text]);
    }

    async decode(tokens: any[]): Promise<string> {
        return this.workerPool.exec("decode", [tokens]);
    }

    async close(): Promise<void> {
        await this.workerPool.terminate();
    }
}

function getWorkerPath(workerFileName: string): string {
    const isProd = process.env.NODE_ENV === "production";

    if (isProd) {
        return path.join(process.resourcesPath, "workers", workerFileName);
    }

    const modulePath = fileURLToPath(new URL(".", import.meta.url));
    return path.join(modulePath, workerFileName);
}
