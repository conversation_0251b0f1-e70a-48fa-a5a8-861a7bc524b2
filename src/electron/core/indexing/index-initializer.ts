import fs from "fs-extra";
import path from "path";
import os from "os";
import { getDb, getSqliteDbPath } from "../rds-db/db.js";
import { Database as DatabaseType } from "better-sqlite3";
import { ChunkCodebaseIndex } from "./chunk-index.js";
import { FullTextSearchCodebaseIndex } from "./fts-index.js";
import { CodeSnippetsCodebaseIndex } from "./snippets-index.js";
import { LanceDbIndex } from "./lance-index.js";
import {
  walkDirAsync,
  getCacheKey,
  getUriPathBasename,
  getFullLanguageName,
} from "../utils.js";
import {
  AIN_INDEX_DIR,
  DEFAULT_BRANCH_NAME,
  ignoredFolders,
  RDS_DB_FILE,
} from "../../constants.js";
import ignore from "ignore";
import log from "electron-log";
import {
  AbortSignalLike,
  CodebaseIndex,
  EmbeddingsProvider,
  FileStatsMap,
  IndexingMeta,
  IndexResultType,
  MarkCompleteFn,
  PathAndCache<PERSON>ey,
  ProgressYield,
  Tag,
} from "../types.js";
import { getLanceDbPath } from "../vector-db/db.js";
import {
  SELECT_GLOBAL_CACHE_ROWS_COUNT,
  SELECT_GLOBAL_CACHE_ROW,
  INSERT_TAG_CATALOG,
  DELETE_TAG_CATALOG,
  UPDATE_TAG_CATALOG,
} from "../queries.js";
import stateManager from "../../global-state.js";
import {
  getIndexingMetaPath,
  loadMetaJson,
  updateProjectMetadata,
} from "../../util.js";
import { BrowserWindow } from "electron";

enum AddRemoveResultType {
  Add = "add",
  Remove = "remove",
  UpdateNewVersion = "updateNewVersion",
  UpdateOldVersion = "updateOldVersion",
  UpdateLastUpdated = "updateLastUpdated",
  Compute = "compute",
}

function mapIndexResultTypeToAddRemoveResultType(
  resultType: IndexResultType
): AddRemoveResultType {
  switch (resultType) {
    case "updateLastUpdated":
      return AddRemoveResultType.UpdateLastUpdated;
    case "compute":
      return AddRemoveResultType.Compute;
    case "addTag":
      return AddRemoveResultType.Add;
    case "del":
    case "removeTag":
      return AddRemoveResultType.Remove;
    default:
      throw new Error(`Unexpected result type: ${resultType}`);
  }
}

class PauseToken {
  private _paused: boolean;
  constructor(paused = false) {
    this._paused = paused;
  }
  set paused(value: boolean) {
    this._paused = value;
  }
  get paused() {
    return this._paused;
  }
}

class GlobalCacheCodeBaseIndex {
  db: DatabaseType;
  relativeExpectedTime: number;
  artifactId: string;
  constructor(db: DatabaseType) {
    this.db = db;
    this.relativeExpectedTime = 1;
    this.artifactId = "globalCache";
  }
  static async create(): Promise<GlobalCacheCodeBaseIndex> {
    return new GlobalCacheCodeBaseIndex(getDb());
  }
  async *update(
    tag: Tag,
    results: any,
    _: any,
    repoName: string
  ): AsyncGenerator<any> {
    try {
      const add = [...results.compute, ...results.addTag];
      const remove = [...results.del, ...results.removeTag];

      for (const { cacheKey } of remove) {
        this.db
          .prepare(
            "DELETE FROM ain_global_cache WHERE cacheKey = ? AND dir = ? AND branch = ? AND artifactId = ?"
          )
          .run(cacheKey, tag.directory, tag.branch, tag.artifactId);
      }
      for (const { cacheKey } of add) {
        this.db
          .prepare(
            "REPLACE INTO ain_global_cache (cacheKey, dir, branch, artifactId) VALUES (?, ?, ?, ?)"
          )
          .run(cacheKey, tag.directory, tag.branch, tag.artifactId);
      }
      yield {
        progress: 1,
        desc: "Done updating global cache",
        status: "done",
      };
    } catch (e: any) {
      console.error(`Global cache update failed: ${e.message}`);
      yield {
        progress: 0,
        desc: `Global cache update failed: ${e.message}`,
        status: "failed",
      };
    }
  }
}

const embeddingsProvider: EmbeddingsProvider = {
  embeddingId: "xenova-transformers",
  //TODO: Need to adjust
  maxEmbeddingChunkSize: 1000,
  embed: async (texts: string[]) => {
    try {
      const { env, pipeline } = await import("@xenova/transformers");

      const model = await pipeline(
        "feature-extraction",
        // "mixedbread-ai/mxbai-embed-large-v1"
        "Xenova/all-MiniLM-L6-v2"
        // "Xenova/jina-embeddings-v2-small-en"
        // "Xenova/bge-base-en-v1.5"
        // "Xenova/bge-large-en-v1.5"
      );
      const embeddings = await Promise.all(
        texts.map(async (text) => {
          const output = await model(text, {
            pooling: "mean",
            normalize: true,
          });
          return Array.from(output.data);
        })
      );
      return embeddings;
    } catch (e: any) {
      console.error(`Failed to generate embeddings: ${e.message}`);
      return [];
    }
  },
};

class CodebaseIndexer {
  //TODO: Need to adjust
  maxChunkSize: number;
  pauseToken: PauseToken;
  indexingCancellationController: AbortController | null;
  codebaseIndexingState: ProgressYield;
  filesPerBatch: number;
  MAX_FILE_SIZE_BYTES: number;
  processingDir: string = "";

  constructor(maxChunkSize = 500) {
    this.maxChunkSize = maxChunkSize;
    this.pauseToken = new PauseToken();
    this.indexingCancellationController = null;
    this.codebaseIndexingState = {
      status: "loading",
      desc: "loading",
      progress: 0,
    };
    this.filesPerBatch = 200;
    this.MAX_FILE_SIZE_BYTES = 5 * 1024 * 1024;
  }

  async readFile(filepath: string): Promise<string> {
    try {
      return await fs.promises.readFile(filepath, "utf-8");
    } catch (e: any) {
      console.warn(`Failed to read ${filepath}: ${e.message}`);
      return "";
    }
  }

  async getFileStats(filepaths: string[]): Promise<Record<string, any>> {
    const stats: Record<string, any> = {};
    let totalSize = 0;
    const techStack = new Set<string>();

    for (const filepath of filepaths) {
      try {
        const stat = await fs.stat(filepath);
        if (stat.size > this.MAX_FILE_SIZE_BYTES) continue;
        const content = await this.readFile(filepath);
        if (!content) continue;

        totalSize += stat.size;

        const tech = getFullLanguageName(filepath);
        tech && techStack.add(tech);

        stats[filepath] = {
          cacheKey: getCacheKey(content),
          lastModified: stat.mtimeMs,
          size: stat.size,
        };
      } catch (e: any) {
        console.warn(`Skipping ${filepath}: ${e.message}`);
      }
    }

    await this.updateIndexingMeta(this.processingDir, {
      totalSize: +(totalSize / (1024 * 1024)).toFixed(2),
      techStack: Array.from(techStack),
      filesCount: Object.keys(stats).length,
      name: getUriPathBasename(this.processingDir),
      status: "indexing",
      progress: 0.0,
    });

    return stats;
  }

  async getIndexesToBuild(): Promise<any[]> {
    try {
      // const indexes: CodebaseIndex[] = [
      // new ChunkCodebaseIndex(
      // this.readFile.bind(this),
      // this.maxChunkSize
      // ), // Chunking must come first
      // ];

      const indexes: CodebaseIndex[] = [
        new CodeSnippetsCodebaseIndex(this.readFile.bind(this)), // CodeSnippets must come first
      ];

      const lanceIndex = await LanceDbIndex.create(
        embeddingsProvider,
        this.readFile.bind(this)
      );

      if (lanceIndex) {
        indexes.push(lanceIndex);
      }

      indexes.push(new FullTextSearchCodebaseIndex());

      // return [
      // new ChunkCodebaseIndex(
      // this.readFile.bind(this),
      // this.maxChunkSize
      // ),
      // new FullTextSearchCodebaseIndex(),
      // new CodeSnippetsCodebaseIndex(this.readFile.bind(this)),
      // ...(lanceIndex ? [lanceIndex] : []),
      // await GlobalCacheCodeBaseIndex.create(),
      // ];
      return indexes;
    } catch (e: any) {
      console.error(
        `Failed to build indexes in getIndexesToBuild: ${e.message}`
      );
      return [];
    }
  }

  async getAddRemoveForTag(
    tag: Tag,
    currentFiles: FileStatsMap
  ): Promise<
    [PathAndCacheKey[], PathAndCacheKey[], PathAndCacheKey[], MarkCompleteFn]
  > {
    const db = getDb();
    const newLastUpdatedTimestamp = Date.now();
    const files = { ...currentFiles };

    const saved: any[] = [];
    try {
      const rows = db
        .prepare(
          "SELECT path, cacheKey, lastUpdated FROM ain_tag_catalog WHERE dir = ? AND branch = ? AND artifactId = ?"
        )
        .all(tag.directory, tag.branch, tag.artifactId);
      saved.push(...rows);
    } catch (e: any) {
      console.error(`Failed to fetch saved items: ${e.message}`);
    }

    const updateNewVersion: any[] = [];
    const updateOldVersion: any[] = [];
    const remove: any[] = [];
    const updateLastUpdated: any[] = [];
    const pathGroups = new Map<string, any>();

    for (const item of saved) {
      const { lastUpdated, path, cacheKey } = item;
      if (!pathGroups.has(path)) {
        pathGroups.set(path, {
          latest: { lastUpdated, cacheKey },
          allVersions: [{ cacheKey }],
        });
      } else {
        const group = pathGroups.get(path);
        group.allVersions.push({ cacheKey });
        if (lastUpdated > group.latest.lastUpdated) {
          group.latest = { lastUpdated, cacheKey };
        }
      }
    }

    // Now process each unique path
    for (const [p, group] of pathGroups) {
      if (files[p] === undefined) {
        // Was indexed, but no longer exists. Remove all versions
        for (const version of group.allVersions) {
          remove.push({ path: p, cacheKey: version.cacheKey });
        }
      } else {
        // Exists in old and new, so determine whether it was updated

        if (group.latest.lastUpdated < files[p].lastModified) {
          // Change was made after last update
          const newHash = getCacheKey(await this.readFile(p));
          if (group.latest.cacheKey !== newHash) {
            updateNewVersion.push({
              path: p,
              cacheKey: newHash,
            });
            for (const version of group.allVersions) {
              updateOldVersion.push({
                path: p,
                cacheKey: version.cacheKey,
              });
            }
          } else {
            // File contents did not change
            updateLastUpdated.push({
              path: p,
              cacheKey: group.latest.cacheKey,
            });
            for (const version of group.allVersions) {
              if (version.cacheKey !== group.latest.cacheKey) {
                updateOldVersion.push({
                  path: p,
                  cacheKey: version.cacheKey,
                });
              }
            }
          }
        }
        // Remove path, so that only newly created paths remain
        delete files[p];
      }
    }

    // const add: any[] = [];
    // for (const p of Object.keys(files)) {
    // const content = await this.readFile(p);
    // if (content) {
    // add.push({ path: p, cacheKey: getCacheKey(content) });
    // }
    // }

    // limit to only 10 concurrent file reads to avoid issues such as
    // "too many file handles". A large number here does not improve
    // throughput due to the nature of disk or network i/o -- huge
    // amounts of readers generally does not improve performance
    const plimit = await import("p-limit").then((mod) => mod.default);
    const limit = plimit(10);
    const promises = Object.keys(files).map(async (path) => {
      const fileContents = await limit(async () => await this.readFile(path));
      return { path, cacheKey: getCacheKey(fileContents) };
    });
    const add: any[] = await Promise.all(promises);

    const itemToAction: {
      [key in AddRemoveResultType]: PathAndCacheKey[];
    } = {
      [AddRemoveResultType.Add]: [],
      [AddRemoveResultType.Remove]: [],
      [AddRemoveResultType.UpdateNewVersion]: [],
      [AddRemoveResultType.UpdateOldVersion]: [],
      [AddRemoveResultType.UpdateLastUpdated]: [],
      [AddRemoveResultType.Compute]: [],
    };

    const markComplete = async (items: any[], resultType: IndexResultType) => {
      try {
        const addRemoveResultType =
          mapIndexResultTypeToAddRemoveResultType(resultType);

        const actionItems = itemToAction[addRemoveResultType];
        if (!actionItems) {
          console.warn(`No action items found for result type: ${resultType}`);
          return;
        }

        for (const item of items) {
          const { path, cacheKey } = item;
          switch (addRemoveResultType) {
            case AddRemoveResultType.Compute:
              db.prepare(INSERT_TAG_CATALOG).run(
                path,
                cacheKey,
                newLastUpdatedTimestamp,
                tag.directory,
                tag.branch,
                tag.artifactId
              );
              break;
            case AddRemoveResultType.Add:
              db.prepare(INSERT_TAG_CATALOG).run(
                path,
                cacheKey,
                newLastUpdatedTimestamp,
                tag.directory,
                tag.branch,
                tag.artifactId
              );
              break;
            case AddRemoveResultType.Remove:
              db.prepare(DELETE_TAG_CATALOG).run(
                cacheKey,
                path,
                tag.directory,
                tag.branch,
                tag.artifactId
              );
              break;
            case AddRemoveResultType.UpdateLastUpdated:
            case AddRemoveResultType.UpdateNewVersion:
              db.prepare(UPDATE_TAG_CATALOG).run(
                cacheKey,
                newLastUpdatedTimestamp,
                path,
                tag.directory,
                tag.branch,
                tag.artifactId
              );
              break;
            case AddRemoveResultType.UpdateOldVersion:
              break;
          }
        }
      } catch (e: any) {
        console.error(`markComplete failed: ${e.message}`);
      }
    };

    for (const item of updateNewVersion) {
      itemToAction[AddRemoveResultType.UpdateNewVersion].push(item);
    }
    for (const item of add) {
      itemToAction[AddRemoveResultType.Add].push(item);
    }
    for (const item of updateOldVersion) {
      itemToAction[AddRemoveResultType.UpdateOldVersion].push(item);
    }
    for (const item of remove) {
      itemToAction[AddRemoveResultType.Remove].push(item);
    }

    return [
      [...add, ...updateNewVersion],
      [...remove, ...updateOldVersion],
      updateLastUpdated,
      markComplete,
    ];
  }

  async getComputeDeleteAddRemove(
    tag: Tag,
    stats: Record<string, any>,
    repoName: string
  ): Promise<[any, any, any]> {
    const db = getDb();

    const [add, remove, updateLastUpdated, markComplete] =
      await this.getAddRemoveForTag(tag, stats);

    const compute: any[] = [];
    const del: any[] = [];
    const addTag: any[] = [];
    const removeTag: any[] = [];

    for (const { path: p, cacheKey } of add) {
      const existing = db
        .prepare(SELECT_GLOBAL_CACHE_ROWS_COUNT)
        .get(cacheKey, tag.artifactId) as { count: number };
      if (existing?.count > 0) {
        addTag.push({ path: p, cacheKey });
      } else {
        compute.push({ path: p, cacheKey });
      }
    }

    for (const { path: p, cacheKey } of remove) {
      const existing = db
        .prepare(SELECT_GLOBAL_CACHE_ROWS_COUNT)
        .get(cacheKey, tag.artifactId) as { count: number };
      if (existing?.count > 1) {
        removeTag.push({ path: p, cacheKey });
      } else {
        del.push({ path: p, cacheKey });
      }
    }

    const results = { compute, del, addTag, removeTag };

    const globalCacheIndex = await GlobalCacheCodeBaseIndex.create();

    return [
      results,
      updateLastUpdated,
      async (items: any, resultType: any) => {
        // Update tag catalog
        await markComplete(items, resultType);

        // Update the global cache
        const results: any = {
          compute: [],
          del: [],
          addTag: [],
          removeTag: [],
        };
        results[resultType] = items;
        for await (const _ of globalCacheIndex.update(
          tag,
          results,
          async () => {},
          repoName
        )) {
        }
      },
    ];
  }

  async *yieldUpdateAndPause(): AsyncGenerator<any> {
    yield {desc: "Indexing Paused", status: "paused" };
    while (this.pauseToken.paused) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  }

  totalIndexOps(results: any): number {
    return (
      results.compute.length +
      results.del.length +
      results.addTag.length +
      results.removeTag.length
    );
  }

  /*
   * Enables the indexing operation to be completed in batches, this is important in large
   * repositories where indexing can quickly use up all the memory available
   */

  *batchRefreshIndexResults(results: any): Generator<any> {
    let curPos = 0;
    while (
      curPos < results.compute.length ||
      curPos < results.del.length ||
      curPos < results.addTag.length ||
      curPos < results.removeTag.length
    ) {
      yield {
        compute: results.compute.slice(curPos, curPos + this.filesPerBatch),
        del: results.del.slice(curPos, curPos + this.filesPerBatch),
        addTag: results.addTag.slice(curPos, curPos + this.filesPerBatch),
        removeTag: results.removeTag.slice(curPos, curPos + this.filesPerBatch),
      };
      curPos += this.filesPerBatch;
    }
  }

  async *indexFiles(
    directory: string,
    files: string[],
    branch: string,
    repoName: string
  ): AsyncGenerator<any> {
    let stats;
    try {
      stats = await this.getFileStats(files);
    } catch (e: any) {
      console.error(`Failed to get file stats: ${e.message}`);
      yield this.handleErrorAndGetProgressUpdate(e);
      return;
    }
    let indexesToBuild;
    try {
      indexesToBuild = await this.getIndexesToBuild();
    } catch (e: any) {
      console.error(`Failed to build indexes in *indexFiles: ${e.message}`);
      yield this.handleErrorAndGetProgressUpdate(e);
      return;
    }
    let completedIndexCount = 0;
    let progress = this.codebaseIndexingState.progress || 0;

    for (const codebaseIndex of indexesToBuild) {
      const tag = {
        directory,
        branch,
        artifactId: codebaseIndex.artifactId,
      };

      yield {
        progress,
        desc: `Planning changes for ${codebaseIndex.artifactId} index...`,
        status: "indexing",
      };

      try {
        const [results, lastUpdated, markComplete] =
          await this.getComputeDeleteAddRemove(tag, stats, repoName);

        const totalOps = this.totalIndexOps(results);
        let completedOps = 0;

        if (totalOps > 0) {
          for (const subResult of this.batchRefreshIndexResults(results)) {
            for await (const { desc } of codebaseIndex.update(
              tag,
              subResult,
              markComplete,
              repoName
            )) {
              yield { progress, desc, status: "indexing" };
            }
            completedOps +=
              subResult.compute.length +
              subResult.del.length +
              subResult.addTag.length +
              subResult.removeTag.length;
            progress =
              (completedIndexCount + completedOps / totalOps) *
              (1 / indexesToBuild.length);
          }
        }

        await markComplete(lastUpdated, "updateLastUpdated");
        completedIndexCount += 1;
      } catch (e: any) {
        console.error(
          `Failed to update index ${codebaseIndex.artifactId}: ${e.message}`
        );
        yield this.handleErrorAndGetProgressUpdate(e);
      }
    }
  }

  async *refreshDirs(
    dirs: string[],
    abortSignal: AbortSignalLike
  ): AsyncGenerator<any> {
    let progress = this.codebaseIndexingState.progress || 0;
    if (dirs.length === 0) {
      yield { progress: 1, desc: "Nothing to index", status: "done" };
      return;
    }

    yield {
      progress,
      desc: "Starting indexing...",
      status: "loading",
    };

    const beginTime = Date.now();
    for (const directory of dirs) {
      this.processingDir = directory;
      const dirBasename = getUriPathBasename(directory);
      yield {
        progress,
        desc: `Discovering files in ${dirBasename}...`,
        status: "indexing",
      };

      const ig = ignore();

      const gitIgnoreFilePath = path.join(directory, ".gitignore");
      const intellectIgnoreFilePath = path.join(directory, ".ainignore");

      if (fs.existsSync(gitIgnoreFilePath)) {
        ig.add((await fs.promises.readFile(gitIgnoreFilePath)).toString());
      }

      if (fs.existsSync(intellectIgnoreFilePath)) {
        ig.add(
          (await fs.promises.readFile(intellectIgnoreFilePath)).toString()
        );
      }

      ig.add([
        ".git",
        ".gitignore",
        ".ainignore",
        ...Array.from(ignoredFolders),
      ]);

      const directoryFiles: string[] = [];
      try {
        for await (const filePath of walkDirAsync(directory, ig, directory)) {
          directoryFiles.push(filePath);
          if (abortSignal.aborted) {
            yield {
              progress: 0,
              desc: "Indexing cancelled",
              status: "cancelled",
            };
            return;
          }
          if (this.pauseToken.paused) {
            yield* this.yieldUpdateAndPause();
          }
        }
      } catch (e: any) {
        console.error(`Failed to walk directory ${directory}: ${e.message}`);
        continue;
      }

      console.log({ TotalFilesssss: directoryFiles.length });
      const branch = DEFAULT_BRANCH_NAME;
      const repoName = path.basename(directory);
      let nextLogThreshold = 0;

      try {
        for await (const updateDesc of this.indexFiles(
          directory,
          directoryFiles,
          branch,
          repoName
        )) {
          if (abortSignal.aborted) {
            yield {
              progress: 0,
              desc: "Indexing cancelled",
              status: "cancelled",
            };
            return;
          }
          if (this.pauseToken.paused) {
            yield* this.yieldUpdateAndPause();
          }
          yield updateDesc;
          if (updateDesc.progress >= nextLogThreshold) {
            nextLogThreshold += 0.025;
            this.logProgress(
              beginTime,
              Math.floor(directoryFiles.length * updateDesc.progress),
              updateDesc.progress
            );
          }
        }
      } catch (err: any) {
        yield this.handleErrorAndGetProgressUpdate(err);
      }
    }

    yield { progress: 1, desc: "Indexing Complete", status: "done" };
    this.logProgress(beginTime, 0, 1);
  }

  async refreshCodebaseIndex(paths: string[]): Promise<void> {
    if (this.indexingCancellationController) {
      this.indexingCancellationController.abort();
    }
    this.indexingCancellationController = new AbortController();
    const metaJson = await loadMetaJson(getIndexingMetaPath());
    console.log("paths[0]", paths[0]);
    const metaEntry = metaJson?.[paths[0]];
    console.log("metaEntry", metaEntry);
    const previousProgress =
      typeof metaEntry?.progress === "number" &&
      metaEntry?.progress > 0 &&
      metaEntry?.progress < 1
        ? metaEntry.progress
        : 0;

    if (
      typeof metaEntry?.progress === "number" &&
      metaEntry?.progress > 0 &&
      metaEntry?.progress < 1
    ) {
      this.codebaseIndexingState = {
        status: "indexing",
        desc: "Resuming indexing...",
        progress: previousProgress,
      };

      // Set initial session progress to resume point
      stateManager.indexingSessions[paths[0]] = {
        progress: previousProgress,
        desc: "Resuming indexing...",
        status: "indexing",
      };
    }else{
      if(metaEntry?.progress == undefined){
        await this.updateIndexingMeta(paths[0], {
          indexingStartedAt: Date.now(),
        });
      }
      else if(typeof metaEntry?.progress === "number"){
        await this.updateIndexingMeta(paths[0], {
          indexingStartedAt: metaEntry?.indexingStartedAt,
        });
      }
    }

    try {
      for await (const update of this.refreshDirs(
        paths,
        this.indexingCancellationController.signal as unknown as AbortSignalLike
      )) {
        await this.updateProgress(update);
        if (update.status === "failed") {
          await this.updateIndexingMeta(this.processingDir, {
            status: "failed",
          });
          console.error("Indexing failed:", update.desc);
        }
      }
    } catch (e: any) {
      console.error(`Failed refreshing codebase index: ${e.message}`);
    } finally {
      this.indexingCancellationController = null;
    }
  }

  async search(
    workspaceDir: string,
    query: string,
    n = 10,
    type: "fts" | "vector" | "snippets" = "fts"
  ): Promise<any> {
    try {
      if (type === "fts") {
        const ftsIndex = new FullTextSearchCodebaseIndex();
        const tags = [
          {
            directory: workspaceDir,
            branch: "main",
            artifactId: type,
          },
        ];
        const config = { text: query, n, tags };
        return await ftsIndex.retrieve(config);
      } else if (type === "vector") {
        const lanceIndex = await LanceDbIndex.create(
          embeddingsProvider,
          this.readFile.bind(this)
        );
        if (!lanceIndex) throw new Error("LanceDB not available");
        const tags = [
          {
            directory: workspaceDir,
            branch: "main",
            artifactId: type,
          },
        ];
        return await lanceIndex.retrieve(query, n, tags, undefined);
      } else if (type === "snippets") {
        const tags = {
          directory: workspaceDir,
          branch: "main",
          artifactId: "codeSnippets",
        };
        return await CodeSnippetsCodebaseIndex.getAll(tags);
      } else {
        throw new Error(`Unknown search type: ${type}`);
      }
    } catch (e: any) {
      console.error(`Search failed: ${e.message}`);
      return [];
    }
  }

  async clearIndexes(): Promise<void> {
    try {
      await fs.unlink(getSqliteDbPath());
      await fs.rm(getLanceDbPath(), { recursive: true, force: true });
    } catch (e: any) {
      console.error(`Failed to clear indexes: ${e.message}`);
    }
  }

  async clearDirIndexes(directory: string): Promise<void> {
    try {
      const db = getDb();
      const lance = await import("@lancedb/lancedb");
      const lanceDb = await lance.connect(getLanceDbPath());
      const tableName = new LanceDbIndex(
        embeddingsProvider,
        this.readFile
      ).tableNameForTag({
        directory,
        branch: DEFAULT_BRANCH_NAME,
        artifactId: `lancedb::${embeddingsProvider.embeddingId}`,
      });

      // Delete SQLite data for the specified directory
      const pathPattern = `${directory}%`;
      const dirPattern = directory;

      db.transaction(() => {
        try {
          // Delete from child tables first to avoid foreign key issues
          db.prepare(
            "DELETE FROM ain_code_snippets_tags WHERE snippetId IN (SELECT id FROM ain_code_snippets WHERE path LIKE ?)"
          ).run(pathPattern);

          db.prepare("DELETE FROM ain_fts_metadata WHERE path LIKE ?").run(
            pathPattern
          );

          // Then delete from parent table
          db.prepare("DELETE FROM ain_code_snippets WHERE path LIKE ?").run(
            pathPattern
          );

          // Delete from other tables
          db.prepare("DELETE FROM ain_fts WHERE path LIKE ?").run(pathPattern);
          db.prepare("DELETE FROM ain_vector_db_cache WHERE path LIKE ?").run(
            pathPattern
          );
          db.prepare("DELETE FROM ain_tag_catalog WHERE dir = ?").run(
            dirPattern
          );
          db.prepare("DELETE FROM ain_global_cache WHERE dir = ?").run(
            dirPattern
          );
        } catch (e: any) {
          console.error(
            `Failed to delete SQLite records for directory ${directory}: ${e.message}`
          );
          throw e; // Rethrow to catch in outer block
        }
      })();

      // Delete LanceDB table for the specified directory
      const existingTables = await lanceDb.tableNames();
      if (existingTables.includes(tableName)) {
        await lanceDb.dropTable(tableName);
      }
    } catch (e: any) {
      console.error(
        `Failed to clear indexes for directory ${directory}: ${e.message}`
      );
      throw e; // Rethrow to allow caller to handle
    }
  }

  async updateIndexingMeta(dirKey: string, patch: object) {
    const metaJsonPath = getIndexingMetaPath();
    try {
      let existingData = await loadMetaJson(metaJsonPath);
      // console.log({ dirKey, patch, existingData });
      existingData[dirKey] = {
        ...(existingData[dirKey] || {}),
        ...patch,
      };
      await fs.writeFile(
        metaJsonPath,
        JSON.stringify(existingData, null, 2),
        "utf-8"
      );

      const enriched = updateProjectMetadata(existingData);
      // Send metadata to renderer directly
      const win = BrowserWindow.getAllWindows()[0];
      if (win && win.webContents) {
        win.webContents.send("indexing-meta", enriched);
      }
      //   ipcRenderer.send('backend-meta-update', existingData);
    } catch (error) {
      console.error("💥 Failed to update meta.json:", error);
    }
  }

  async updateProgress(update: ProgressYield) {
    this.codebaseIndexingState = update;
    // if (
    //   stateManager.indexingSessions[this.processingDir] &&
    //   typeof stateManager.indexingSessions[this.processingDir].progress === 'number' &&
    //   update.progress === 0
    // ) {
    //   update.progress = stateManager.indexingSessions[this.processingDir].progress;
    // }

    // console.log("stateManager.indexingSessions at updateprogress",stateManager.indexingSessions, this.codebaseIndexingState.progress);
    stateManager.indexingSessions[this.processingDir] = update;

    let meta: IndexingMeta | null = null;

    if (update.status === "done") {
      meta = { status: "done", progress: 1, indexingFinishedAt: Date.now() };
    } else if (update.status === "cancelled") {
      meta = { status: "paused", indexingFinishedAt: Date.now() };
    } else if (update.status === "failed") {
      meta = { status: "failed", indexingFinishedAt: Date.now() };
    }

    if (meta) {
      await this.updateIndexingMeta(this.processingDir, meta);
      await this.resetAndGoForNextIndexing();
    }

    // Send metadata to renderer directly
    const win = BrowserWindow.getAllWindows()[0];
    if (win && win.webContents) {
      win.webContents.send("indexing-progress", {
        ...update,
        path: this.processingDir,
      });
    }
  }

  async resetAndGoForNextIndexing() {
    this.processingDir = "";
    stateManager.clearActive();

    const next = stateManager.dequeue();
    if (next) {
      stateManager.setActive(next);
      await this.refreshCodebaseIndex([next]);
    }
  }

  handleErrorAndGetProgressUpdate(err: any) {
    console.error("Indexing error:", err);
    return {
      progress: 0,
      desc: `Indexing failed: ${err.message}`,
      status: "failed",
      debugInfo: err.stack,
    };
  }

  logProgress(beginTime: number, completedFileCount: number, progress: number) {
    const timeTaken = Date.now() - beginTime;
    const seconds = Math.round(timeTaken / 1000);
    const progressPercentage = (progress * 100).toFixed(1);
    const filesPerSec = completedFileCount / seconds || 0;
    // console.debug(
    // `Indexing: ${progressPercentage}% complete, elapsed time: ${seconds}s, ${filesPerSec.toFixed(
    // 2
    // )} files/sec`
    // );
  }
}

export { CodebaseIndexer, PauseToken, embeddingsProvider };
