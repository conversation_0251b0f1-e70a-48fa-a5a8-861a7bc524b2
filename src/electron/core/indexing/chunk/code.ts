import Parser from "tree-sitter";
import path from "path";
import {
    getUriFileExtension,
    supportedLanguages,
    basicChunker,
    getUriPathBasename,
    countTokensAsync,
} from "../../utils.js";
import { Chunk, ChunkDocumentParams, TreeSitterNode } from "../../types.js";
import { loadNativeLanguage } from "../../tree-sitter/tree-sitter-languages.js";

const FUNCTION_BLOCK_NODE_TYPES = ["block", "statement_block"];
const FUNCTION_DECLARATION_NODE_TYPES = [
    "method_definition",
    "function_definition",
    "function_item",
    "function_declaration",
    "method_declaration",
];

const CONTAINER_NODE_TYPES = [
    "statement_block",
    "block",
    "program",
    "module",
    "source_file",
    "class_body",
    "declaration_list",
    "export_statement",
    "lexical_declaration",
    "variable_declaration",
];

const CHUNKABLE_NODE_TYPES = [
    ...FUNCTION_DECLARATION_NODE_TYPES,
    "class_definition",
    "class_declaration",
    "impl_item",
    "interface_declaration",
    "type_alias_declaration",
    "enum_declaration",
    "namespace_declaration",
    "export_statement",
    "import_statement",
    "variable_declaration",
    "lexical_declaration",
];

// async function initParser(
//     filepath: string
// ): Promise<Parser | null | undefined> {
//     try {
//         await Parser.init();
//         const parser = new Parser();
//         const language = await getLanguageForFile(filepath);
//         if (!language) {
//             return undefined;
//         }
//         parser.setLanguage(language);
//         return parser;
//     } catch (e: any) {
//         console.error(`Failed to initialize Tree-sitter parser: ${e.message}`);
//         return null;
//     }
// }

export async function initParser(
    filepath: string
): Promise<Parser | null | undefined> {
    try {
        const extension = getUriFileExtension(filepath);
        const languageName = supportedLanguages[extension];

        if (!languageName) {
            return undefined;
        }
        const parser = new Parser();
        const langModule = await loadNativeLanguage(languageName);
        if (!langModule) return null;
        parser.setLanguage(langModule);

        return parser;
    } catch (e: any) {
        console.error(`Failed to initialize Tree-sitter parser: ${e.message}`);
        return null;
    }
}

function collapsedReplacement(node: TreeSitterNode): string {
    if (node.type === "statement_block") return "{ ... }";
    return "...";
}

function containsFunctions(node: TreeSitterNode): boolean {
    if (FUNCTION_DECLARATION_NODE_TYPES.includes(node.type)) {
        return true;
    }
    return node.children.some((child) => containsFunctions(child));
}

function getFunctionDeclarations(node: TreeSitterNode): TreeSitterNode[] {
    const functions: TreeSitterNode[] = [];
    if (FUNCTION_DECLARATION_NODE_TYPES.includes(node.type)) {
        functions.push(node);
    }
    node.children.forEach((child) => {
        functions.push(...getFunctionDeclarations(child));
    });
    return functions;
}

function firstChild(
    node: TreeSitterNode,
    grammarName: string | string[]
): TreeSitterNode | null {
    if (Array.isArray(grammarName)) {
        return (
            node.children.find((child) => grammarName.includes(child.type)) ||
            null
        );
    }
    return node.children.find((child) => child.type === grammarName) || null;
}

async function collapseChildren(
    node: TreeSitterNode,
    code: string,
    blockTypes: string[],
    collapseTypes: string[],
    collapseBlockTypes: string[],
    maxChunkSize: number
): Promise<string> {
    code = code.slice(0, node.endIndex);
    const block = firstChild(node, blockTypes);
    const collapsedChildren: string[] = [];

    if (block) {
        const childrenToCollapse = block.children.filter((child) =>
            collapseTypes.includes(child.type)
        );
        for (const child of childrenToCollapse.reverse()) {
            const grandChild = firstChild(child, collapseBlockTypes);
            if (grandChild) {
                const start = grandChild.startIndex;
                const end = grandChild.endIndex;
                const collapsedChild =
                    code.slice(child.startIndex, start) +
                    collapsedReplacement(grandChild);
                code =
                    code.slice(0, start) +
                    collapsedReplacement(grandChild) +
                    code.slice(end);
                collapsedChildren.unshift(collapsedChild);
            }
        }
    }
    code = code.slice(node.startIndex);
    let removedChild = false;
    while (
        (await countTokensAsync(code.trim())) > maxChunkSize &&
        collapsedChildren.length > 0
    ) {
        removedChild = true;
        const childCode = collapsedChildren.pop()!;
        const index = code.lastIndexOf(childCode);
        if (index > 0) {
            code = code.slice(0, index) + code.slice(index + childCode.length);
        }
    }

    if (removedChild) {
        // Remove the extra blank lines
        let lines = code.split("\n");
        let firstWhiteSpaceInGroup = -1;
        for (let i = lines.length - 1; i >= 0; i--) {
            if (lines[i].trim() === "") {
                if (firstWhiteSpaceInGroup < 0) {
                    firstWhiteSpaceInGroup = i;
                }
            } else {
                if (firstWhiteSpaceInGroup - i > 1) {
                    // Remove the lines
                    lines = [
                        ...lines.slice(0, i + 1),
                        ...lines.slice(firstWhiteSpaceInGroup + 1),
                    ];
                }
                firstWhiteSpaceInGroup = -1;
            }
        }

        code = lines.join("\n");
    }
    return code;
}

async function constructClassDefinitionChunk(
    node: TreeSitterNode,
    code: string,
    maxChunkSize: number
): Promise<string> {
    return collapseChildren(
        node,
        code,
        ["block", "class_body", "declaration_list"],
        FUNCTION_DECLARATION_NODE_TYPES,
        FUNCTION_BLOCK_NODE_TYPES,
        maxChunkSize
    );
}

async function constructContainerChunk(
    node: TreeSitterNode,
    code: string,
    maxChunkSize: number
): Promise<string> {
    const functions = getFunctionDeclarations(node);

    if (functions.length === 0) {
        let nodeText = code.slice(node.startIndex, node.endIndex);
        if ((await countTokensAsync(nodeText)) > maxChunkSize) {
            const lines = nodeText.split("\n");
            let truncatedText = "";
            for (const line of lines) {
                const testText = truncatedText + line + "\n";
                if ((await countTokensAsync(testText)) > maxChunkSize) {
                    truncatedText += "// ... truncated\n";
                    break;
                }
                truncatedText = testText;
            }
            return truncatedText.trim();
        }
        return nodeText;
    }

    return collapseChildren(
        node,
        code,
        ["statement_block", "block", "program", "module", "source_file"],
        FUNCTION_DECLARATION_NODE_TYPES,
        FUNCTION_BLOCK_NODE_TYPES,
        maxChunkSize
    );
}

async function constructStatementChunk(
    node: TreeSitterNode,
    code: string,
    maxChunkSize: number
): Promise<string> {
    return code.slice(node.startIndex, node.endIndex);
}

async function constructFunctionDefinitionChunk(
    node: TreeSitterNode,
    code: string,
    maxChunkSize: number
): Promise<string> {
    const bodyNode = node.children[node.children.length - 1];
    const funcText =
        code.slice(node.startIndex, bodyNode.startIndex) +
        collapsedReplacement(bodyNode);
    if (
        node.parent &&
        ["block", "declaration_list"].includes(node.parent.type) &&
        node.parent.parent &&
        ["class_definition", "impl_item"].includes(node.parent.parent.type)
    ) {
        const classNode = node.parent.parent;
        const classBlock = node.parent;
        return `${code.slice(
            classNode.startIndex,
            classBlock.startIndex
        )}...\n\n${" ".repeat(node.startPosition.column)}${funcText}`;
    }
    return funcText;
}

const collapsedNodeConstructors: Record<
    string,
    (
        node: TreeSitterNode,
        code: string,
        maxChunkSize: number
    ) => Promise<string>
> = {
    // Classes, structs, etc
    class_definition: constructClassDefinitionChunk,
    class_declaration: constructClassDefinitionChunk,
    impl_item: constructClassDefinitionChunk,
    // Functions
    function_definition: constructFunctionDefinitionChunk,
    function_declaration: constructFunctionDefinitionChunk,
    function_item: constructFunctionDefinitionChunk,
    // Methods
    method_declaration: constructFunctionDefinitionChunk,
    // Properties
    // arrow_function: constructFunctionDefinitionChunk,
    // function_expression: constructFunctionDefinitionChunk,
    // statement_block: constructContainerChunk,
    // block: constructContainerChunk,
    // program: constructContainerChunk,
    // module: constructContainerChunk,
    // source_file: constructContainerChunk,
    // interface_declaration: constructStatementChunk,
    // type_alias_declaration: constructStatementChunk,
    // enum_declaration: constructStatementChunk,
    // namespace_declaration: constructStatementChunk,
    // export_statement: constructStatementChunk,
    // import_statement: constructStatementChunk,
    // variable_declaration: constructStatementChunk,
    // lexical_declaration: constructStatementChunk,
};

async function maybeYieldChunk(
    node: TreeSitterNode,
    code: string,
    maxChunkSize: number,
    root = true
): Promise<Chunk | undefined> {
    // For root nodes or nodes that have constructors, check if they fit in maxChunkSize
    if (root || node.type in collapsedNodeConstructors) {
        const tokenCount = await countTokensAsync(node.text);
        if (tokenCount < maxChunkSize) {
            return {
                content: node.text,
                startLine: node.startPosition.row,
                endLine: node.endPosition.row,
                // nodeType: node.type,
                // tokenCount: tokenCount,
                // containsFunctions: containsFunctions(node),
            };
        }
    }
    return undefined;
}

async function* getSmartCollapsedChunks(
    node: TreeSitterNode,
    code: string,
    maxChunkSize: number,
    root = true
): AsyncGenerator<Chunk, void, unknown> {
    const chunk = await maybeYieldChunk(node, code, maxChunkSize, root);

    if (chunk) {
        yield chunk;
        return;
    }

    // If we have a constructor for this node type, use it
    if (node.type in collapsedNodeConstructors) {
        const constructedContent = await collapsedNodeConstructors[node.type](
            node,
            code,
            maxChunkSize
        );
        yield {
            content: constructedContent,
            startLine: node.startPosition.row,
            endLine: node.endPosition.row,
            // nodeType: node.type,
            // tokenCount: await countTokensAsync(constructedContent),
            // containsFunctions: containsFunctions(node),
            // isCollapsed: true,
        };
    }

    const generators = node.children.map((child) => {
        return getSmartCollapsedChunks(child, code, maxChunkSize, false);
    });
    for (const generator of generators) {
        yield* generator;
    }
}

async function* codeChunker(
    filepath: string,
    contents: string,
    maxChunkSize: number
): AsyncGenerator<Chunk, void, unknown> {
    if (contents.trim().length === 0) return;
    const parser = await initParser(filepath);
    if (!parser || parser === undefined) {
        console.warn(
            `Skipping ${filepath}: Tree-sitter parser initialization failed`
        );
        return;
    }
    try {
        const tree = parser.parse(contents);
        yield* getSmartCollapsedChunks(
            tree.rootNode as TreeSitterNode,
            contents,
            maxChunkSize
        );
    } catch (e: any) {
        console.warn(`Failed to parse ${filepath}: ${e.message}`);
    }
}

async function* chunkDocumentWithoutId(
    fileUri: string,
    contents: string,
    maxChunkSize: number
): AsyncGenerator<Chunk, void, unknown> {
    if (contents.trim() === "") {
        return;
    }
    const extension = getUriFileExtension(fileUri);
    if (extension in supportedLanguages) {
        try {
            for await (const chunk of codeChunker(
                fileUri,
                contents,
                maxChunkSize
            )) {
                yield chunk;
            }
            return;
        } catch (e: any) {
            console.log(
                "Error in chunkDocumentWithoutId",
                fileUri,
                extension,
                e
            );
        }
    }
    yield* basicChunker(contents, maxChunkSize);
}

async function* chunkDocument({
    filepath,
    contents,
    maxChunkSize,
    digest,
}: ChunkDocumentParams): AsyncGenerator<Chunk, void, unknown> {
    let index = 0;
    const chunkPromises: Promise<Chunk | undefined>[] = [];
    for await (const chunkWithoutId of chunkDocumentWithoutId(
        filepath,
        contents,
        maxChunkSize
    )) {
        // chunkPromises.push(
        //     (async () => {
        //         const tokenCount = await countTokensAsync(
        //             chunkWithoutId.content!
        //         );
        //         if (tokenCount > 1000) {
        //             console.debug(
        //                 `Chunk with more than ${maxChunkSize} tokens constructed: `,
        //                 filepath,
        //                 tokenCount
        //             );
        //             return undefined;
        //         }
        //         return {
        //             ...chunkWithoutId,
        //             digest,
        //             index,
        //             filepath,
        //             tokenCount,
        //         };
        //     })()
        // );
        chunkPromises.push(
            new Promise((resolve) => {
                void (async () => {
                    if (
                        (await countTokensAsync(chunkWithoutId.content!)) >
                        maxChunkSize
                    ) {
                        // console.debug(
                        //   `Chunk with more than ${maxChunkSize} tokens constructed: `,
                        //   filepath,
                        //   countTokens(chunkWithoutId.content),
                        // );
                        return resolve(undefined);
                    }
                    resolve({
                        ...chunkWithoutId,
                        digest,
                        index,
                        filepath,
                    });
                })();
            })
        );
        index++;
    }
    for await (const chunk of chunkPromises) {
        if (!chunk) {
            continue;
        }
        yield chunk;
    }
}

function shouldChunk(fileUri: string, contents: string): boolean {
    if (contents.length > 1000000) {
        console.log(
            "Skipping large file: ",
            fileUri,
            " (size: ",
            contents.length,
            " bytes)  "
        );
        return false;
    }
    if (contents.length === 0) {
        console.log("Skipping empty file: ", fileUri);
        return false;
    }
    const baseName = getUriPathBasename(fileUri);
    return baseName.includes(".");
}

export { chunkDocument, shouldChunk, codeChunker };
