import { v4 as uuidv4 } from "uuid";
import pLimit from "p-limit";

import { getDb } from "../rds-db/db.js";
import { chunkDocument, shouldChunk } from "./chunk/code.js";
import {
    basicChunker,
    countTokensAsync,
    getCache<PERSON>ey,
    getUriPathBasename,
    tagToString,
} from "../utils.js";
import {
    Chunk,
    ChunkMapEntry,
    EmbeddingData,
    EmbeddingsProvider,
    EmbeddingsRetrieveResult,
    IndexResultType,
    LanceRow,
    MarkCompleteFn,
    PathAndCacheKey,
    ReadFileFn,
    ResultItem,
    Tag,
    UpdateResults,
    VectorDBCacheType,
} from "../types.js";
import { getLanceDbPath } from "../vector-db/db.js";
import stateManager from "../../global-state.js";
import log from "electron-log";

export class LanceDbIndex {
    static lance: any = null;
    relativeExpectedTime = 13;

    embeddingsProvider: EmbeddingsProvider;
    readFile: ReadFileFn;
    //TODO : Need to adjust
    maxEmbeddingsChunkSize: number = 200;
    maxEmbeddingsBatchSize: number = 5;

    constructor(embeddingsProvider: EmbeddingsProvider, readFile: ReadFileFn) {
        this.embeddingsProvider = embeddingsProvider;
        this.readFile = readFile;
    }

    get artifactId(): string {
        return `lancedb::${this.embeddingsProvider.embeddingId}`;
    }

    static async create(
        embeddingsProvider: EmbeddingsProvider,
        readFile: ReadFileFn
    ): Promise<LanceDbIndex | null> {
        try {
            this.lance = await import("@lancedb/lancedb");
            return new LanceDbIndex(embeddingsProvider, readFile);
        } catch (err: any) {
            console.error("Failed to load LanceDB:", err);
            return null;
        }
    }

    tableNameForTag(tag: Tag): string {
        return tagToString(tag).replace(/[^\w-_.]/g, "");
    }

    async computeRows(items: ResultItem[]): Promise<LanceRow[]> {
        const chunkMap = await this.collectChunks(items);
        const allChunks = Array.from(chunkMap.values()).flatMap(
            ({ chunks }) => chunks
        );
        const embeddings = await this.getEmbeddings(allChunks);
        for (let i = embeddings.length - 1; i >= 0; i--) {
            if (embeddings[i] === undefined) {
                const chunk = allChunks[i];
                const chunks = chunkMap.get(chunk.filepath as string)?.chunks;
                if (chunks) {
                    const index = chunks.findIndex((c) => c === chunk);
                    if (index !== -1) {
                        chunks.splice(index, 1);
                    }
                }

                embeddings.splice(i, 1);
            }
        }
        return this.createLanceDbRows(chunkMap, embeddings);
    }

    async collectChunks(
        items: ResultItem[]
    ): Promise<Map<string, ChunkMapEntry>> {
        const chunkMap = new Map<string, ChunkMapEntry>();
        for (const item of items) {
            try {
                const content = await this.readFile(item.path);
                if (!shouldChunk(item.path, content)) continue;
                const chunks: Chunk[] = [];
                for await (const chunk of chunkDocument({
                    filepath: item.path,
                    contents: content,
                    maxChunkSize: this.maxEmbeddingsChunkSize,
                    digest: item.cacheKey,
                })) {
                    if ((chunk.content as string).length === 0) {
                        // throw new Error("did not chunk properly");
                        console.debug(
                            `Did not chunk properly in lance-indexing ${item.path}`
                        );
                    }
                    chunks.push({ ...chunk, isPath: false });
                }
                //For File path embedding
                chunks.push({
                    content: item.path,
                    startLine: 0,
                    endLine: 0,
                    digest: getCacheKey(item.path),
                    index: chunks.length + 1,
                    filepath: item.path,
                    cacheKey: getCacheKey(item.path),
                    tokenCount: await countTokensAsync(item.path),
                    isPath: true,
                });
                chunkMap.set(item.path, { item, chunks });
            } catch (err: any) {
                console.warn(`Skipping ${item.path}: ${err.message}`);
            }
        }
        return chunkMap;
    }

    async getEmbeddings(chunks: Chunk[]): Promise<number[][]> {
        const results: number[][] = [];

        for (let i = 0; i < chunks.length; i += this.maxEmbeddingsBatchSize) {
            const batch = chunks
                .slice(i, i + this.maxEmbeddingsBatchSize)
                .map((c) => c.content!);
            const embeddings = await this.embeddingsProvider.embed(batch);
            results.push(...embeddings);
        }

        return results;
    }

    createLanceDbRows(
        chunkMap: Map<string, ChunkMapEntry>,
        embeddings: number[][]
    ): LanceRow[] {
        const results: LanceRow[] = [];
        let embeddingIndex = 0;
        for (const [path, { item, chunks }] of chunkMap) {
            for (const chunk of chunks) {
                const generatedUuid = uuidv4();
                results.push({
                    path,
                    cachekey: item.cacheKey,
                    uuid: chunk.isPath
                        ? generatedUuid + "-filepath"
                        : generatedUuid + "-code",
                    type: chunk.isPath ? "filepath" : "code",
                    vector: embeddings[embeddingIndex] || [],
                    startLine: chunk.startLine,
                    endLine: chunk.endLine,
                    contents: chunk.content!,
                });
                embeddingIndex++;
            }
        }
        return results;
    }

    async insertRows(db: any, rows: LanceRow[]): Promise<void> {
        const insert = db.prepare(
            "INSERT INTO ain_vector_db_cache (uuid, cacheKey, path, artifact_id, vector, startLine, endLine, contents) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
        );
        const transaction = db.transaction(() => {
            for (const row of rows) {
                insert.run(
                    row.uuid,
                    row.cachekey,
                    row.path,
                    this.artifactId,
                    JSON.stringify(row.vector),
                    row.startLine,
                    row.endLine,
                    row.contents
                );
            }
        });
        transaction();
    }

    async *update(
        tag: Tag,
        results: UpdateResults,
        markComplete: MarkCompleteFn,
        repoName: string
    ): AsyncGenerator<
        { progress: number; desc: string; status: string },
        void,
        unknown
    > {
        const lance = LanceDbIndex.lance;
        const sqliteDb = getDb();
        const lanceTableName = this.tableNameForTag(tag);
        const lanceDb = await lance.connect(getLanceDbPath());
        let lanceTable: any;
        const existingTables: string[] = await lanceDb.tableNames();
        let needToCreateTable = !existingTables.includes(lanceTableName);

        const addComputedLanceDbRows = async (
            pathAndCacheKeys: PathAndCacheKey[],
            computedRows: any
        ) => {
            if (lanceTable) {
                if (computedRows.length > 0) {
                    await lanceTable.add(computedRows);
                }
            } else if (existingTables.includes(lanceTableName)) {
                lanceTable = await lanceDb.openTable(lanceTableName);
                needToCreateTable = false;
                if (computedRows.length > 0) {
                    await lanceTable.add(computedRows);
                }
            } else if (computedRows.length > 0) {
                lanceTable = await lanceDb.createTable(
                    lanceTableName,
                    computedRows
                );
                needToCreateTable = false;
            }

            await markComplete(pathAndCacheKeys, IndexResultType.Compute);
        };

        yield {
            progress: 0,
            desc: `Computing embeddings for ${results.compute.length} file${
                results.compute.length > 1 ? "s" : ""
            }`,
            status: "indexing",
        };

        const dbRows = await this.computeRows(results.compute);
        await this.insertRows(sqliteDb, dbRows);
        await addComputedLanceDbRows(results.compute, dbRows);

        let accumulatedProgress = 0;

        for (const { path, cacheKey } of results.addTag) {
            const cachedItems: any[] = sqliteDb
                .prepare(
                    "SELECT * FROM ain_vector_db_cache WHERE cacheKey = ? AND artifact_id = ?"
                )
                .all(cacheKey, this.artifactId);

            const lanceRows: any[] = [];
            for (const item of cachedItems) {
                try {
                    const vector = JSON.parse(item.vector);
                    const { uuid, startLine, endLine, contents } = item as any;

                    lanceRows.push({
                        path,
                        uuid,
                        startLine,
                        endLine,
                        contents,
                        cachekey: cacheKey,
                        vector,
                    });
                } catch (err) {
                    console.warn(
                        `LanceDBIndex, skipping due to invalid vector JSON:\n\n\nError: ${err}`
                    );
                }
            }

            if (lanceRows.length > 0) {
                if (needToCreateTable) {
                    lanceTable = await lanceDb.createTable(
                        lanceTableName,
                        lanceRows
                    );
                    needToCreateTable = false;
                } else if (!lanceTable) {
                    lanceTable = await lanceDb.openTable(lanceTableName);
                    needToCreateTable = false;
                    await lanceTable.add(lanceRows);
                } else {
                    await lanceTable?.add(lanceRows);
                }
            }
            await markComplete([{ path, cacheKey }], IndexResultType.AddTag);
            accumulatedProgress += 1 / results.addTag.length / 3;
            yield {
                progress: accumulatedProgress,
                desc: `Indexing ${getUriPathBasename(path)}`,
                status: "indexing",
            };
        }

        if (!needToCreateTable) {
            const toDel = [...results.removeTag, ...results.del];

            if (!lanceTable) {
                lanceTable = await lanceDb.openTable(lanceTableName);
            }

            for (const { path, cacheKey } of toDel) {
                await lanceTable.delete(
                    `cachekey = '${cacheKey}' AND path = '${path}'`
                );

                accumulatedProgress += 1 / toDel.length / 3;
                yield {
                    progress: accumulatedProgress,
                    desc: `Stashing ${getUriPathBasename(path)}`,
                    status: "indexing",
                };
            }
        }

        await markComplete(results.removeTag, IndexResultType.RemoveTag);

        for (const { path, cacheKey } of results.del) {
            sqliteDb
                .prepare(
                    "DELETE FROM ain_vector_db_cache WHERE cacheKey = ? AND path = ? AND artifact_id = ?"
                )
                .run(cacheKey, path, this.artifactId);
            accumulatedProgress += 1 / results.del.length / 3;
            yield {
                progress: accumulatedProgress,
                desc: `Removing ${getUriPathBasename(path)}`,
                status: "indexing",
            };
        }

        await markComplete(results.del, IndexResultType.Delete);

        yield {
            progress: 1,
            desc: "Completed Calculating Embeddings",
            status: "done",
        };
    }

    private async _retrieveForTag(
        tag: any,
        n: number,
        directory: string | undefined,
        vector: number[],
        db: any,
        filterType: "filepath" | "code" | undefined = undefined
    ): Promise<any> {
        try {
            const tableName = this.tableNameForTag(tag);
            const tableNames = await db.tableNames();
            if (!tableNames.includes(tableName)) {
                console.warn("Table not found in LanceDB", tableName);
                return [];
            }

            const table = await db.openTable(tableName);

            let query = table.search(vector);

            const conditions: string[] = [];

            if (directory) {
                conditions.push(`path LIKE '${directory}%'`);
            }

            if (filterType) {
                conditions.push(`type = '${filterType}'`);
            }

            if (conditions.length > 0) {
                query = query.where(conditions.join(" AND ")).limit(300);
            } else {
                query = query.limit(n);
            }

            const results = await query.toArray();
            return results.slice(0, n);
        } catch (error) {
            console.log("Error in_retrieveForTag", error);
        }
    }

    // async retrieve(
    //     query: string,
    //     n: number,
    //     tags: Tag[],
    //     filterDirectory?: string | undefined
    // ): Promise<EmbeddingsRetrieveResult> {
    //     const lance = LanceDbIndex.lance;

    //     const chunks = [];
    //     for await (const chunk of basicChunker(query, 1000)) {
    //         chunks.push(chunk);
    //     }

    //     let vector = null;
    //     try {
    //         [vector] = await this.embeddingsProvider.embed(
    //             chunks.map((c) => c.content)
    //         );
    //     } catch (err) {
    //         [vector] = await this.embeddingsProvider.embed([query]);
    //     }

    //     const db = await lance.connect(getLanceDbPath());

    //     let allCodeResults = [];
    //     let allFilePathResults = [];
    //     for (const tag of tags) {
    //         const results = await this._retrieveForTag(
    //             { ...tag, artifactId: this.artifactId },
    //             n,
    //             filterDirectory,
    //             vector,
    //             db,
    //             "code"
    //         );
    //         allCodeResults.push(...results);

    //         const filePathResults = await this._retrieveForTag(
    //             { ...tag, artifactId: this.artifactId },
    //             n,
    //             filterDirectory,
    //             vector,
    //             db,
    //             "filepath"
    //         );
    //         allFilePathResults.push(...filePathResults);
    //     }

    //     allCodeResults = allCodeResults
    //         .sort((a, b) => a._distance - b._distance)
    //         .slice(0, n);

    //     allFilePathResults = allFilePathResults
    //         .sort((a, b) => a._distance - b._distance)
    //         .slice(0, n);

    //     const sqliteDb = getDb();
    //     const codeData = sqliteDb
    //         .prepare(
    //             `SELECT * FROM ain_vector_db_cache WHERE uuid in (${allCodeResults
    //                 .map((r) => `'${r.uuid}'`)
    //                 .join(",")})`
    //         )
    //         .all() as VectorDBCacheType[];

    //     const filePathData = sqliteDb
    //         .prepare(
    //             `SELECT * FROM ain_vector_db_cache WHERE uuid in (${allFilePathResults
    //                 .map((r) => `'${r.uuid}'`)
    //                 .join(",")})`
    //         )
    //         .all() as VectorDBCacheType[];

    //     const filePathEmbeddingsData: EmbeddingData[] = [];
    //     const codeEmbeddingsData: EmbeddingData[] = [];

    //     const codeTokenBudget =
    //         stateManager.retrievalParams.embeddingsTokenBudget;
    //     let codeTokenCount = 0;

    //     for (const d of codeData) {
    //         const tokens = await countTokensAsync(d.contents);
    //         if (codeTokenCount + tokens <= codeTokenBudget) {
    //             codeEmbeddingsData.push({
    //                 digest: d.cacheKey,
    //                 filepath: d.path,
    //                 startLine: d.startLine,
    //                 endLine: d.endLine,
    //                 index: codeData.indexOf(d),
    //                 content: d.contents,
    //             });
    //             codeTokenCount += tokens;
    //         } else {
    //             // Stop adding more code chunks if budget exceeded
    //             break;
    //         }
    //     }

    //     for (const d of filePathData) {
    //         filePathEmbeddingsData.push({
    //             digest: d.cacheKey,
    //             filepath: d.path,
    //             startLine: d.startLine,
    //             endLine: d.endLine,
    //             index: filePathData.indexOf(d),
    //             content: d.contents,
    //         });
    //     }

    //     // for (const d of data) {
    //     //     if (d.uuid.includes("filepath")) {
    //     //         filePathEmbeddingsData.push({
    //     //             digest: d.cacheKey,
    //     //             filepath: d.path,
    //     //             startLine: d.startLine,
    //     //             endLine: d.endLine,
    //     //             index: data.indexOf(d),
    //     //             content: d.contents,
    //     //         });
    //     //     } else if (d.uuid.includes("code")) {
    //     //         codeEmbeddingsData.push({
    //     //             digest: d.cacheKey,
    //     //             filepath: d.path,
    //     //             startLine: d.startLine,
    //     //             endLine: d.endLine,
    //     //             index: data.indexOf(d),
    //     //             content: d.contents,
    //     //         });
    //     //     }
    //     // }

    //     // console.log({ filePathEmbeddingsData, codeEmbeddingsData });

    //     log.debug({
    //         vdbCacheCodeData: codeData,
    //         vdbCacheFilePathData: filePathData,
    //     });

    //     // for (const [i, d] of codeData.entries()) {
    //     //     if (d.uuid.includes("filepath")) {
    //     //         filePathEmbeddingsData.push({
    //     //             digest: d.cacheKey,
    //     //             filepath: d.path,
    //     //             startLine: d.startLine,
    //     //             endLine: d.endLine,
    //     //             index: i,
    //     //             content: d.contents,
    //     //         });
    //     //     } else if (d.uuid.includes("code")) {
    //     //         const tokens = await countTokensAsync(d.contents);
    //     //         if (codeTokenCount + tokens <= codeTokenBudget) {
    //     //             codeEmbeddingsData.push({
    //     //                 digest: d.cacheKey,
    //     //                 filepath: d.path,
    //     //                 startLine: d.startLine,
    //     //                 endLine: d.endLine,
    //     //                 index: i,
    //     //                 content: d.contents,
    //     //             });
    //     //             codeTokenCount += tokens;
    //     //         } else {
    //     //             // Stop adding more code chunks if budget exceeded
    //     //             continue;
    //     //         }
    //     //     }
    //     // }
    //     log.debug({
    //         embeddingsTokenCount: codeTokenCount,
    //         codeEmbeddingsData,
    //         filePathEmbeddingsData,
    //     });
    //     return { filePathEmbeddingsData, codeEmbeddingsData };
    // }

    async retrieve(
        query: string,
        n: number,
        tags: Tag[],
        filterDirectory?: string
    ): Promise<EmbeddingsRetrieveResult> {
        const lance = LanceDbIndex.lance;
        const sqliteDb = getDb();

        const chunks = [];
        for await (const chunk of basicChunker(query, 1000)) {
            chunks.push(chunk);
        }

        let vector = null;
        try {
            [vector] = await this.embeddingsProvider.embed(
                chunks.map((c) => c.content)
            );
        } catch (err) {
            [vector] = await this.embeddingsProvider.embed([query]);
        }

        const db = await lance.connect(getLanceDbPath());

        const allCodeResults: any[] = [];
        const allFilePathResults: any[] = [];

        for (const tag of tags) {
            const baseTag = { ...tag, artifactId: this.artifactId };

            const [codeResults, filepathResults] = await Promise.all([
                this._retrieveForTag(
                    baseTag,
                    n,
                    filterDirectory,
                    vector,
                    db,
                    "code"
                ),
                this._retrieveForTag(
                    baseTag,
                    n,
                    filterDirectory,
                    vector,
                    db,
                    "filepath"
                ),
            ]);

            allCodeResults.push(...codeResults);
            allFilePathResults.push(...filepathResults);
        }

        const sortedCodeResults = allCodeResults
            .sort((a, b) => a._distance - b._distance)
            .slice(0, n);

        const sortedFilePathResults = allFilePathResults
            .sort((a, b) => a._distance - b._distance)
            .slice(0, n);

        const allUuids = [...sortedCodeResults, ...sortedFilePathResults].map(
            (r) => `'${r.uuid}'`
        );
        const cachedData = sqliteDb
            .prepare(
                `SELECT * FROM ain_vector_db_cache WHERE uuid IN (${allUuids.join(
                    ","
                )})`
            )
            .all() as VectorDBCacheType[];

        const codeEmbeddingsData: EmbeddingData[] = [];
        const filePathEmbeddingsData: EmbeddingData[] = [];

        const codeTokenBudget =
            stateManager.retrievalParams.embeddingsTokenBudget;
        let codeTokenCount = 0;

        for (const [i, d] of cachedData.entries()) {
            if (d.uuid.includes("filepath")) {
                filePathEmbeddingsData.push({
                    digest: d.cacheKey,
                    filepath: d.path,
                    startLine: d.startLine,
                    endLine: d.endLine,
                    index: i,
                    content: d.contents,
                });
            } else if (d.uuid.includes("code")) {
                const tokens = await countTokensAsync(d.contents);
                if (codeTokenCount + tokens <= codeTokenBudget) {
                    codeEmbeddingsData.push({
                        digest: d.cacheKey,
                        filepath: d.path,
                        startLine: d.startLine,
                        endLine: d.endLine,
                        index: i,
                        content: d.contents,
                    });
                    codeTokenCount += tokens;
                } else {
                    continue; // Budget exceeded (filepath embeddings are not limited by token count)
                }
            }
        }

        log.debug({
            embeddingsTokenCount: codeTokenCount,
            // vdbCacheCodeData: codeEmbeddingsData,
            // vdbCacheFilePathData: filePathEmbeddingsData,
        });

        return { filePathEmbeddingsData, codeEmbeddingsData };
    }
}
