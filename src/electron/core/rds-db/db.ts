import Database, { Database as DatabaseType } from "better-sqlite3";
import * as path from "path";
import os from "os";
import fs from "fs-extra";
import { AIN_INDEX_DIR, RDS_DB_FILE } from "../../constants.js";

// Singleton instance
let dbInstance: DatabaseType | null = null;

export function getSqliteDbPath(): string {
    return path.join(os.homedir(), AIN_INDEX_DIR, RDS_DB_FILE);
}

export function getDb(): DatabaseType {
    const dbPath = getSqliteDbPath();

    if (dbInstance && !fs.existsSync(dbPath)) {
        dbInstance.close();
        dbInstance = null;
    }

    if (!dbInstance) {
        dbInstance = new Database(dbPath);
        dbInstance.pragma("journal_mode = WAL");
        // dbInstance.exec(`DROP TABLE IF EXISTS ain_fts_metadata`);

        dbInstance.exec(`

      CREATE TABLE IF NOT EXISTS ain_code_snippets (
        id INTEGER PRIMARY KEY,
        path TEXT NOT NULL,
        cacheKey TEXT NOT NULL,
        content TEXT NOT NULL,
        title TEXT NOT NULL,
        signature TEXT,
        startLine INTEGER NOT NULL,
        endLine INTEGER NOT NULL
      );

      CREATE TABLE IF NOT EXISTS ain_code_snippets_tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tag TEXT NOT NULL,
        snippetId INTEGER NOT NULL,
        FOREIGN KEY (snippetId) REFERENCES ain_code_snippets (id)
      );

      CREATE VIRTUAL TABLE IF NOT EXISTS ain_fts USING fts5(
        path,
        content,
        tokenize = 'trigram'
      );

      CREATE TABLE IF NOT EXISTS ain_fts_metadata (
        fts_rowid INTEGER PRIMARY KEY, 
        path TEXT NOT NULL,
        cacheKey TEXT NOT NULL,
        snippetId INTEGER NOT NULL,
        FOREIGN KEY (snippetId) REFERENCES ain_code_snippets (id) ON DELETE CASCADE
      );

      CREATE TABLE IF NOT EXISTS ain_vector_db_cache (
        uuid TEXT PRIMARY KEY,
        cacheKey TEXT NOT NULL,
        path TEXT NOT NULL,
        artifact_id TEXT NOT NULL,
        vector TEXT NOT NULL,
        startLine INTEGER NOT NULL,
        endLine INTEGER NOT NULL,
        contents TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS ain_tag_catalog (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        dir STRING NOT NULL,
        branch STRING NOT NULL,
        artifactId STRING NOT NULL,
        path STRING NOT NULL,
        cacheKey STRING NOT NULL,
        lastUpdated INTEGER NOT NULL
      );

       CREATE TABLE IF NOT EXISTS ain_global_cache (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          cacheKey STRING NOT NULL,
          dir STRING NOT NULL,
          branch STRING NOT NULL,
          artifactId STRING NOT NULL
      );
    `);

        // Add unique indexes
        dbInstance.exec(`
      CREATE UNIQUE INDEX IF NOT EXISTS idx_code_snippets_unique
      ON ain_code_snippets (path, cacheKey, content, title, startLine, endLine);

      CREATE UNIQUE INDEX IF NOT EXISTS idx_snippetId_tag
      ON ain_code_snippets_tags (snippetId, tag);
    `);
    }
    return dbInstance;
}

export function closeDb(): void {
    if (dbInstance) {
        dbInstance.close();
        dbInstance = null;
    }
}


// CREATE TABLE IF NOT EXISTS ain_chunks (
//     id INTEGER PRIMARY KEY AUTOINCREMENT,
//     cacheKey TEXT NOT NULL,
//     path TEXT NOT NULL,
//     idx INTEGER NOT NULL,
//     startLine INTEGER NOT NULL,
//     endLine INTEGER NOT NULL,
//     content TEXT NOT NULL
//   );

//   CREATE TABLE IF NOT EXISTS ain_chunk_tags (
//     id INTEGER PRIMARY KEY AUTOINCREMENT,
//     tag TEXT NOT NULL,
//     chunkId INTEGER NOT NULL,
//     FOREIGN KEY (chunkId) REFERENCES ain_chunks (id) ON DELETE CASCADE,
//     UNIQUE (tag, chunkId)
//   );