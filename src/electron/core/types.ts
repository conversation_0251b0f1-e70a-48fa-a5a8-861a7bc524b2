// Type for symbol range
export interface SymbolRange {
    start: { character: number; line: number };
    end: { character: number; line: number };
}

// Type for symbol
export interface SymbolInfo {
    filepath: string;
    type: string;
    name: string;
    range: SymbolRange;
    content: string;
}

// IDE interface
export interface IDE {
    readFile: (uri: string) => Promise<string>;
}

export interface TreeSitterNode {
    type: string;
    text: string;
    startIndex: number;
    endIndex: number;
    startPosition: { row: number; column: number };
    endPosition: { row: number; column: number };
    children: TreeSitterNode[];
    parent?: TreeSitterNode;
}

export interface Chunk {
    id?: string;
    content?: string;
    startLine: number;
    endLine: number;
    nodeType?: string;
    tokenCount?: number;
    containsFunctions?: boolean;
    isCollapsed?: boolean;
    digest?: string;
    index?: number;
    filepath?: string;
    path?: string;
    cacheKey?: string;
    idx?: string;
    isPath?: boolean;
}

export interface ChunkDocumentParams {
    filepath: string;
    contents: string;
    maxChunkSize: number;
    digest?: string;
}

export interface ResultItem {
    path: string;
    cacheKey: string;
}

export interface Results {
    compute: ResultItem[];
    addTag: ResultItem[];
    removeTag: ResultItem[];
    del: ResultItem[];
}

export type MarkCompleteFn = (
    items: ResultItem[],
    type: IndexResultType
) => Promise<void>;
export type ReadFileFn = (path: string) => Promise<string>;

export interface PathAndCacheKey {
    path: string;
    cacheKey: string;
}

export interface ProgressYield {
    desc: string;
    status: string;
    progress: number;
}

export interface ChunkRow {
    id: number;
}

export interface BranchAndDir {
    branch: string;
    directory: string;
}

export interface Tag extends BranchAndDir {
    artifactId: string;
}

export interface RetrieveConfig {
    text: string;
    n: number;
    tags: Tag[];
    filterPaths?: string[];
    filterDirectory?: string;
}

export interface UpdateResultItem {
    path: string;
    cacheKey: string;
}

export interface UpdateResults {
    compute: UpdateResultItem[];
    addTag: UpdateResultItem[];
    removeTag: UpdateResultItem[];
    del: UpdateResultItem[];
}

export interface Snippet {
    title: string;
    content: string;
    signature: string;
    startLine: number;
    endLine: number;
    cacheKey?: string;
    path?: string;
}

export interface Match {
    captures: Array<{
        name: string;
        node: any; // Tree-sitter node, could be typed more specifically if available
    }>;
}

export interface EmbeddingsProvider {
    embeddingId: string;
    maxEmbeddingChunkSize?: number;
    embed: (contents: string[]) => Promise<number[][]>;
}

export interface LanceRow {
    path: string;
    cachekey: string;
    uuid: string;
    vector: number[];
    startLine: number;
    endLine: number;
    contents: string;
    type?: string;
}

export interface ChunkMapEntry {
    item: ResultItem;
    chunks: Chunk[];
}

export interface AbortSignalLike {
    aborted: boolean;
}

export interface SearchResult {
    filepath: string;
    startLine: number;
    endLine: number;
    content: string;
}

export interface SnippetResult {
    title: string;
    description: string;
    id: string | number;
}

export interface CodebaseIndex {
    artifactId: string;
    relativeExpectedTime: number;
    update(
        tag: Tag,
        results: any,
        markComplete: MarkCompleteFn,
        repoName: string | undefined
    ): AsyncGenerator<ProgressYield>;
}

export enum IndexResultType {
    Compute = "compute",
    Delete = "del",
    AddTag = "addTag",
    RemoveTag = "removeTag",
    UpdateLastUpdated = "updateLastUpdated",
}

export interface PathAndCacheKey {
    path: string;
    cacheKey: string;
}

export interface FileStats {
    size: number;
    lastModified: number;
}
export interface FileStatsMap {
    [path: string]: FileStats;
}

export interface NoRerankerRetrievalPipelineOptions extends RetrieveConfig {
    input?: string;
    workspaceDir: string;
    filterDirectory?: string;
    nFinal?: number;
    nRetrieve?: number;
    config?: any;
    llm?: any;
    ide?: any;
    modelId?: string;
}

export interface RerankerRetrievalPipelineOptions extends RetrieveConfig {
    input: string;
    workspaceDir: string;
    filterDirectory?: string;
    nFinal: number;
    nRetrieve: number;
    config: any;
    llm: any;
    ide: any;
}

export interface RetrievalParams {
    rerankThreshold: number;
    nFinal: number;
    nRetrieve: number;
    bm25Threshold: number;
    nResultsToExpandWithEmbeddings: number;
    nEmbeddingsExpandTo: number;
}

export interface EmbeddingData {
    digest: string;
    filepath: string;
    startLine: number;
    endLine: number;
    index: number;
    content: string;
}

export interface EmbeddingsRetrieveResult {
    filePathEmbeddingsData: EmbeddingData[];
    codeEmbeddingsData: EmbeddingData[];
}

export interface VectorDBCacheType {
    uuid: string;
    cacheKey: string;
    digest: string;
    filepath: string;
    startLine: number;
    endLine: number;
    index: number;
    contents: string;
    path: string;
}

export interface ReferenceType {
    [key: string]: { startLine: number; endLine: number }[];
}

export interface ContextType {
    contextText: string;
    usedReferences: ReferenceType;
}

export interface IndexingMeta{
    status: "done" | "paused" | "failed";
    indexingFinishedAt?: number;
    progress?: number;
};