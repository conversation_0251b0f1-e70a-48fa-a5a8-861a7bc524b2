import {
    BedrockRuntimeClient,
    InvokeModelCommand,
} from "@aws-sdk/client-bedrock-runtime";
import { fromNodeProviderChain } from "@aws-sdk/credential-providers";

interface Chunk {
    content: string;
}

class BedrockReranker {
    static providerName = "bedrock";
    static defaultOptions = {
        region: "us-west-2",
        model: "amazon.rerank-v1:0",
        profile: "default",
    };

    private region: string;
    private model: string;
    private profile: string;
    private apiBase: string;

    constructor(options: {
        region?: string;
        model?: string;
        profile?: string;
        apiBase?: string;
    }) {
        this.region = options.region || BedrockReranker.defaultOptions.region;
        this.model = options.model || BedrockReranker.defaultOptions.model;
        this.profile =
            options.profile || BedrockReranker.defaultOptions.profile;
        this.apiBase =
            options.apiBase ||
            `https://bedrock-runtime.${this.region}.amazonaws.com`;
    }

    private async _getCredentials() {
        try {
            return await fromNodeProviderChain({
                profile: this.profile,
                ignoreCache: true,
            })();
        } catch (e) {
            console.warn(
                `AWS profile with name ${this.profile} not found in ~/.aws/credentials, using default profile`
            );
            return await fromNodeProviderChain()();
        }
    }

    async rerank(query: string, chunks: any): Promise<number[]> {
        if (!query || !chunks.length) {
            throw new Error("Query and chunks must not be empty");
        }

        try {
            const credentials = await this._getCredentials();
            const client = new BedrockRuntimeClient({
                region: this.region,
                credentials: {
                    accessKeyId: credentials.accessKeyId,
                    secretAccessKey: credentials.secretAccessKey,
                    sessionToken: credentials.sessionToken || "",
                },
            });

            const payload: any = {
                query: query,
                documents: chunks.map((chunk: any) => chunk.content),
                top_n: chunks.length,
            };

            if (this.model.startsWith("cohere.rerank")) {
                payload.api_version = 2;
            }

            const input = {
                body: JSON.stringify(payload),
                modelId: this.model,
                accept: "*/*",
                contentType: "application/json",
            };

            const command = new InvokeModelCommand(input);
            const response = await client.send(command);

            if (!response.body) {
                throw new Error("Empty response received from Bedrock");
            }

            const decoder = new TextDecoder();
            const decoded = decoder.decode(response.body);
            try {
                const responseBody = JSON.parse(decoded);
                return responseBody.results
                    .sort((a: any, b: any) => a.index - b.index)
                    .map((result: any) => result.relevance_score);
            } catch (e) {
                throw new Error(
                    `Error parsing JSON from Bedrock response body:\n${decoded}, ${JSON.stringify(
                        e
                    )}`
                );
            }
        } catch (error: unknown) {
            if (error instanceof Error) {
                if ("code" in error) {
                    throw new Error(
                        `AWS Bedrock rerank error (${(error as any).code}): ${
                            error.message
                        }`
                    );
                }
                throw new Error(
                    `Error in BedrockReranker.rerank: ${error.message}`
                );
            }
            throw new Error(
                "Error in BedrockReranker.rerank: Unknown error occurred"
            );
        }
    }
}

export default BedrockReranker;
