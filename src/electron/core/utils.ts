// utils.ts

import fs from "fs-extra";
import path from "path";
import { pathToFileURL, fileURLToPath } from "url";
import CryptoJS from "crypto-js";
import ignore, { Ignore } from "ignore";
import * as URI from "uri-js";
import { encoding_for_model } from "tiktoken";

// Import project-specific utilities and constants
import {
    ignoredFileExtensions,
    ignoredFileNames,
    ignoredFolders,
} from "../constants.js";
import { Chunk } from "./types.js";
import { LlamaAsyncEncoder } from "./indexing/encoders/LlamaAsyncEncode.js";

const llamaAsyncEncoder = new LlamaAsyncEncoder();

// --- LanguageName and Supported Languages ---
export enum LanguageName {
    CPP = "cpp",
    C_SHARP = "c_sharp",
    C = "c",
    CSS = "css",
    PHP = "php",
    BASH = "bash",
    JSON = "json",
    TYPESCRIPT = "typescript",
    TSX = "tsx",
    ELM = "elm",
    JAVASCRIPT = "javascript",
    PYTHON = "python",
    ELISP = "elisp",
    ELIXIR = "elixir",
    GO = "go",
    EMBEDDED_TEMPLATE = "embedded_template",
    HTML = "html",
    JAVA = "java",
    LUA = "lua",
    OCAML = "ocaml",
    QL = "ql",
    RESCRIPT = "rescript",
    RUBY = "ruby",
    RUST = "rust",
    SYSTEMRDL = "systemrdl",
    TOML = "toml",
    SOLIDITY = "solidity",
}

export const supportedLanguages: Record<string, LanguageName> = {
    cpp: LanguageName.CPP,
    hpp: LanguageName.CPP,
    cc: LanguageName.CPP,
    cxx: LanguageName.CPP,
    hxx: LanguageName.CPP,
    cp: LanguageName.CPP,
    hh: LanguageName.CPP,
    inc: LanguageName.CPP,
    cs: LanguageName.C_SHARP,
    c: LanguageName.C,
    h: LanguageName.C,
    css: LanguageName.CSS,
    php: LanguageName.PHP,
    phtml: LanguageName.PHP,
    php3: LanguageName.PHP,
    php4: LanguageName.PHP,
    php5: LanguageName.PHP,
    php7: LanguageName.PHP,
    phps: LanguageName.PHP,
    "php-s": LanguageName.PHP,
    bash: LanguageName.BASH,
    sh: LanguageName.BASH,
    json: LanguageName.JSON,
    ts: LanguageName.TYPESCRIPT,
    mts: LanguageName.TYPESCRIPT,
    cts: LanguageName.TYPESCRIPT,
    tsx: LanguageName.TSX,
    elm: LanguageName.ELM,
    js: LanguageName.JAVASCRIPT,
    jsx: LanguageName.JAVASCRIPT,
    mjs: LanguageName.JAVASCRIPT,
    cjs: LanguageName.JAVASCRIPT,
    py: LanguageName.PYTHON,
    pyw: LanguageName.PYTHON,
    pyi: LanguageName.PYTHON,
    el: LanguageName.ELISP,
    emacs: LanguageName.ELISP,
    ex: LanguageName.ELIXIR,
    exs: LanguageName.ELIXIR,
    go: LanguageName.GO,
    eex: LanguageName.EMBEDDED_TEMPLATE,
    heex: LanguageName.EMBEDDED_TEMPLATE,
    leex: LanguageName.EMBEDDED_TEMPLATE,
    html: LanguageName.HTML,
    htm: LanguageName.HTML,
    java: LanguageName.JAVA,
    lua: LanguageName.LUA,
    luau: LanguageName.LUA,
    ocaml: LanguageName.OCAML,
    ml: LanguageName.OCAML,
    mli: LanguageName.OCAML,
    ql: LanguageName.QL,
    res: LanguageName.RESCRIPT,
    resi: LanguageName.RESCRIPT,
    rb: LanguageName.RUBY,
    erb: LanguageName.RUBY,
    rs: LanguageName.RUST,
    rdl: LanguageName.SYSTEMRDL,
    toml: LanguageName.TOML,
    sol: LanguageName.SOLIDITY,
};

// --- Utility Functions ---

export function getCleanUriPath(uri: string): string {
    const parsed = URI.parse(uri).path ?? "";
    let clean = parsed.replace(/^\//, "");
    clean = clean.replace(/\/$/, "");
    return clean;
}

export function getFileExtensionFromBasename(basename: string): string {
    const parts = basename.split(".");
    if (parts.length < 2) return "";
    return (parts.slice(-1)[0] || "").toLowerCase();
}

export function getUriPathBasename(filePath: string): string {
    const cleanPath = getCleanUriPath(filePath);
    const basename = cleanPath.split("/").pop() || "";
    return decodeURIComponent(basename);
}

export function getUriFileExtension(uri: string): string {
    const baseName = getUriPathBasename(uri);
    return getFileExtensionFromBasename(baseName);
}

export function getCacheKey(content: string): string {
    return CryptoJS.SHA256(content).toString(CryptoJS.enc.Hex);
}

export function tagToString(tag: {
    directory: string;
    branch: string;
    artifactId: string;
}): string {
    return `${tag.directory}:${tag.branch}:${tag.artifactId}`;
}

export function getLastNPathParts(filePath: string, n: number): string {
    const parts = filePath.split(path.sep).filter((p) => p);
    return parts.slice(-n).join(path.sep);
}

export function getFullLanguageName(
    filepath: string
): LanguageName | undefined {
    const extension = getUriFileExtension(filepath);
    return supportedLanguages[extension];
}

// --- Directory Walking Utilities ---

export async function* walkDirAsync(
    dir: string,
    ig: Ignore,
    rootPath: string
): AsyncGenerator<string> {
    const entries = await fs.readdir(dir, { withFileTypes: true });
    for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        const relativePath = path.relative(rootPath, fullPath);

        if (ig.ignores(relativePath)) continue;

        if (entry.isDirectory()) {
            yield* walkDirAsync(fullPath, ig, rootPath);
        } else if (entry.isFile()) {
            const ext = path.extname(entry.name).toLowerCase();
            if (
                !ignoredFileExtensions.includes(ext) &&
                !ignoredFileNames.has(entry.name)
            ) {
                yield fullPath;
            }
        }
    }
}

// --- Array and Chunk Utilities ---

export function deduplicateArray<T>(
    array: T[],
    equal: (a: T, b: T) => boolean
): T[] {
    const result: T[] = [];
    for (const item of array) {
        if (!result.some((existingItem) => equal(existingItem, item))) {
            result.push(item);
        }
    }
    return result;
}

export function deduplicateChunks(chunks: Chunk[]): Chunk[] {
    return deduplicateArray(chunks, (a, b) => {
        return (
            a.filepath === b.filepath &&
            a.startLine === b.startLine &&
            a.endLine === b.endLine
        );
    });
}

// --- Path and URI Utilities ---

export function findUriInDirs(
    uriInput: string,
    dirUriCandidatesInput: string[]
): {
    uri: string;
    localPath: string;
    relativePathOrBasename: string;
    foundInDir: string | null;
} {
    const uri = uriInput.startsWith("file://")
        ? uriInput
        : pathToFileURL(uriInput).href;
    const dirUriCandidates = dirUriCandidatesInput.map((dir) =>
        dir.startsWith("file://") ? dir : pathToFileURL(dir).href
    );

    const uriComps = URI.parse(uri);
    if (!uriComps.scheme) throw new Error(`Invalid uri: ${uri}`);

    const uriPathParts = getCleanUriPath(uri).split("/");

    for (const dir of dirUriCandidates) {
        const dirComps = URI.parse(dir);
        if (!dirComps.scheme) throw new Error(`Invalid uri: ${dir}`);
        if (uriComps.scheme !== dirComps.scheme) continue;

        const dirPathParts = getCleanUriPath(dir).split("/");
        if (uriPathParts.length < dirPathParts.length) continue;

        let allDirPartsMatch = true;
        for (let i = 0; i < dirPathParts.length; i++) {
            if (dirPathParts[i] !== uriPathParts[i]) {
                allDirPartsMatch = false;
                break;
            }
        }

        if (allDirPartsMatch) {
            const relativePath = uriPathParts
                .slice(dirPathParts.length)
                .map(decodeURIComponent)
                .join("/");
            return {
                uri,
                localPath: fileURLToPath(uri),
                relativePathOrBasename: relativePath,
                foundInDir: dir,
            };
        }
    }

    return {
        uri,
        localPath: fileURLToPath(uri),
        relativePathOrBasename: getUriPathBasename(uri),
        foundInDir: null,
    };
}

export function getUriDescription(
    uri: string,
    dirUriCandidates: string[]
): {
    uri: string;
    relativePathOrBasename: string;
    foundInDir: string | null;
    last2Parts: string;
    baseName: string;
} {
    const { relativePathOrBasename, foundInDir } = findUriInDirs(
        uri,
        dirUriCandidates
    );
    const baseName = getUriPathBasename(uri);
    const last2Parts = getLastNPathParts(relativePathOrBasename, 2);
    return {
        uri,
        relativePathOrBasename,
        foundInDir,
        last2Parts,
        baseName,
    };
}

// --- Ignore Utilities ---

export function createIgnoreInstance(directory: string): Ignore {
    const ig = ignore();
    ig.add([".git", ".gitignore", ".ainignore", ...Array.from(ignoredFolders)]);
    return ig;
}

// --- Workspace Directory Utilities ---

export async function getWorkspaceDirectories(): Promise<string[]> {
    try {
        const currentDir =
            "/Users/<USER>/Documents/8800/8800-code-gen-ai/code-gen/sp-node-apis";
        const ig = createIgnoreInstance(currentDir);

        const entries = fs.readdirSync(currentDir, { withFileTypes: true });

        const folders = entries
            .filter((entry) => entry.isDirectory())
            .map((entry) => ({
                absolute: path.join(currentDir, entry.name),
                relative: entry.name,
            }))
            .filter(({ relative }) => !ig.ignores(relative))
            .map(({ absolute }) => absolute);

        return [currentDir, ...folders];
    } catch (err) {
        console.error(
            "Failed to read directories in getWorkspaceDirectories:",
            err
        );
        return [];
    }
}

export async function getWorkspaceDirs(): Promise<string[]> {
    const workspaceFolders = await getWorkspaceDirectories();
    return workspaceFolders.map((dir) => dir.toString());
}

export async function walkDirs(workspaceDir: string): Promise<string[]> {
    // const workspaceDirs = await getWorkspaceDirs();
    const workspaceDirs = [workspaceDir];
    const ig = createIgnoreInstance(workspaceDirs[0]);
    const results: string[] = [];
    for (const dir of workspaceDirs) {
        const files = await walkDir(dir, ig, workspaceDirs[0]);
        results.push(...files);
    }
    return results;
}

export async function walkDir(
    dir: string,
    ig: Ignore,
    rootPath: string
): Promise<string[]> {
    const results: string[] = [];
    let entries: fs.Dirent[];
    try {
        entries = await fs.readdir(dir, { withFileTypes: true });
    } catch (err: any) {
        console.warn(`Failed to read directory ${dir}: ${err.message}`);
        return results;
    }

    for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        const relativePath = path.relative(rootPath, fullPath);

        if (ig.ignores(relativePath)) continue;

        if (entry.isDirectory()) {
            const subResults = await walkDir(fullPath, ig, rootPath);
            results.push(...subResults);
        } else if (entry.isFile()) {
            results.push(fullPath);
        }
    }
    return results;
}

// --- Token and Chunk Utilities ---

export const getTokenCountOfText = async (text: string): Promise<number> => {
    const enc = encoding_for_model("gpt-4");
    const tokenCount = enc.encode(text).length;
    enc.free();
    return tokenCount;
};

export async function* basicChunker(
    contents: string,
    maxChunkSize: number
): AsyncGenerator<{ content: string; startLine: number; endLine: number }> {
    if (contents.trim().length === 0) return;

    let chunkContent = "";
    let chunkTokens = 0;
    let startLine = 0;
    let currLine = 0;

    const lineTokens = await Promise.all(
        contents.split("\n").map(async (l) => {
            return {
                line: l,
                tokenCount: await countTokensAsync(l),
            };
        })
    );

    for (const lt of lineTokens) {
        if (chunkTokens + lt.tokenCount > maxChunkSize - 5) {
            yield { content: chunkContent, startLine, endLine: currLine - 1 };
            chunkContent = "";
            chunkTokens = 0;
            startLine = currLine;
        }
        if (lt.tokenCount < maxChunkSize) {
            chunkContent += `${lt.line}\n`;
            chunkTokens += lt.tokenCount + 1;
        }
        currLine++;
    }
    yield {
        content: chunkContent,
        startLine,
        endLine: currLine - 1,
    };
}

// --- Token Counting Async ---

// export async function countTokensAsync(
//   content: string | { text?: string }[],
//   modelName = "llama2"
// ): Promise<number> {
//   // Dummy encoder for demonstration; replace with actual logic as needed
//   const enc = encoding_for_model("gpt-4");
//   if (Array.isArray(content)) {
//     const promises = content.map(async (part) => {
//       return enc.encode(part.text ?? "").length;
//     });
//     const counts = await Promise.all(promises);
//     enc.free();
//     return counts.reduce((sum, val) => sum + val, 0);
//   }
//   const count = enc.encode(content ?? "").length;
//   enc.free();
//   return count;
// }

function autodetectTemplateType(model: string): string | undefined {
    const lower = model.toLowerCase();

    if (lower.includes("codellama") && lower.includes("70b")) {
        return "codellama-70b";
    }

    if (
        lower.includes("gpt") ||
        lower.includes("command") ||
        lower.includes("chat-bison") ||
        lower.includes("pplx") ||
        lower.includes("gemini") ||
        lower.includes("grok") ||
        lower.includes("moonshot") ||
        lower.includes("mercury")
    ) {
        return undefined;
    }
    if (lower.includes("llama3") || lower.includes("llama-3")) {
        return "llama3";
    }

    if (lower.includes("llava")) {
        return "llava";
    }

    if (lower.includes("tinyllama")) {
        return "zephyr";
    }

    if (lower.includes("xwin")) {
        return "xwin-coder";
    }

    if (lower.includes("dolphin")) {
        return "chatml";
    }

    if (lower.includes("gemma")) {
        return "gemma";
    }

    if (lower.includes("phi2")) {
        return "phi2";
    }

    if (lower.includes("phind")) {
        return "phind";
    }

    if (lower.includes("llama")) {
        return "llama2";
    }

    if (lower.includes("zephyr")) {
        return "zephyr";
    }

    if (lower.includes("claude")) {
        return "none";
    }

    if (lower.includes("codestral")) {
        return "none";
    }

    if (lower.includes("alpaca") || lower.includes("wizard")) {
        return "alpaca";
    }

    if (lower.includes("mistral") || lower.includes("mixtral")) {
        return "llama2";
    }

    if (lower.includes("deepseek")) {
        return "deepseek";
    }

    if (lower.includes("ninja") || lower.includes("openchat")) {
        return "openchat";
    }

    if (lower.includes("neural-chat")) {
        return "neural-chat";
    }

    if (lower.includes("granite")) {
        return "granite";
    }

    return "chatml";
}

function asyncEncoderForModel(modelName: string): any {
    // const modelType = autodetectTemplateType(modelName);
    // if (!modelType || modelType === "none") {
    //     return Promise.resolve(gptAsyncEncoder);
    // }
    // return Promise.resolve(llamaAsyncEncoder);
    return llamaAsyncEncoder;
}

// function encodingForModel(modelName: string): any {
//     const modelType = autodetectTemplateType(modelName);

//     // if (!modelType || modelType === "none") {
//     //     return gptEncoding;
//     // }

//     return llamaEncoding;
// }

export async function countTokensAsync(
    content: string | { text?: string }[],
    modelName: string = "llama2"
): Promise<number> {
    const encoding = await asyncEncoderForModel(modelName);
    if (Array.isArray(content)) {
        const promises = content.map(async (part) => {
            return (await encoding.encode(part.text ?? "")).length;
        });
        return (await Promise.all(promises)).reduce((sum, val) => sum + val, 0);
    }
    return (await encoding.encode(content ?? "")).length;
}

// --- Pruning and String Utilities ---

// export function pruneLinesFromTop(prompt: string, maxTokens: number): string {
//     const lines = prompt.split("\n");
//     let totalTokens = 0;
//     const lineTokens = lines.map((line) => {
//         const enc = encoding_for_model("gpt-4");
//         const count = enc.encode(line).length;
//         enc.free();
//         return count;
//     });
//     totalTokens = lineTokens.reduce((sum, tokens) => sum + tokens, 0);
//     let start = 0;
//     let currentLines = lines.length;
//     totalTokens += Math.max(0, currentLines - 1);

//     while (totalTokens > maxTokens && start < currentLines) {
//         totalTokens -= lineTokens[start];
//         if (currentLines - start > 1) {
//             totalTokens--;
//         }
//         start++;
//     }
//     return lines.slice(start).join("\n");
// }

export async function pruneLinesFromTop(
    prompt: string,
    maxTokens: number
): Promise<string> {
    const lines = prompt.split("\n");

    const lineTokens = await Promise.all(
        lines.map((line) => countTokensAsync(line))
    );

    let totalTokens = lineTokens.reduce((sum, tokens) => sum + tokens, 0);
    totalTokens += Math.max(0, lines.length - 1);

    let start = 0;
    while (totalTokens > maxTokens && start < lines.length) {
        totalTokens -= lineTokens[start];
        if (lines.length - start > 1) {
            totalTokens--;
        }
        start++;
    }
    const finalPrompt = lines.slice(start).join("\n");
    return finalPrompt;
}

export function pruneLinesFromBottom(
    prompt: string,
    maxTokens: number
): string {
    const lines = prompt.split("\n");
    let totalTokens = 0;
    const lineTokens = lines.map((line) => {
        const enc = encoding_for_model("gpt-4");
        const count = enc.encode(line).length;
        enc.free();
        return count;
    });
    totalTokens = lineTokens.reduce((sum, tokens) => sum + tokens, 0);
    let end = lines.length;
    totalTokens += Math.max(0, end - 1);
    while (totalTokens > maxTokens && end > 0) {
        end--;
        totalTokens -= lineTokens[end];
        if (end > 0) {
            totalTokens--;
        }
    }
    return lines.slice(0, end).join("\n");
}

// --- Miscellaneous Utilities ---

export function getAinUtilsPath(): string {
    const utilsPath = path.join(getAinGlobalPath(), ".utils");
    if (!fs.existsSync(utilsPath)) {
        fs.mkdirSync(utilsPath);
    }
    return utilsPath;
}

export function getAinGlobalPath(): string {
    const ainPath = process.cwd();
    if (!fs.existsSync(ainPath)) {
        fs.mkdirSync(ainPath);
    }
    return ainPath;
}

export function getRepoMapFilePath(): string {
    return path.join(getAinUtilsPath(), "repo_map.txt");
}

export function pathToUriPathSegment(p: string): string {
    let clean = p.replace(/[\\]/g, "/");
    clean = clean.replace(/^\//, "");
    clean = clean.replace(/\/$/, "");
    return clean
        .split("/")
        .map((part) => encodeURIComponent(part))
        .join("/");
}

export function joinPathsToUri(uri: string, ...pathSegments: string[]): string {
    let baseUri = uri;
    if (baseUri.at(-1) !== "/") {
        baseUri += "/";
    }
    const segments = pathSegments.map((segment) =>
        pathToUriPathSegment(segment)
    );
    return URI.resolve(baseUri, segments.join("/"));
}

export async function resolveRelativePathInDir(
    p: string
): Promise<string | undefined> {
    const dirs = await getWorkspaceDirs();
    for (const dirUri of dirs) {
        const fullUri = joinPathsToUri(dirUri, p);
        if (fs.existsSync(fullUri)) {
            return fullUri;
        }
    }
    return undefined;
}

export async function resolveRelativePathInWorkspaceDir(
    workspaceDirs: string[],
    p: string
): Promise<string | undefined> {
    for (const dirUri of workspaceDirs) {
        const fullUri = joinPathsToUri(dirUri, p);
        if (fs.existsSync(fullUri)) {
            return fullUri;
        }
    }
    return undefined;
}

export async function resolveFullFilePath(
    p: string
): Promise<string | undefined> {
    if (fs.existsSync(p)) {
        return p;
    }
    return undefined;
}