import JavaScript from "tree-sitter-javascript";
import TypeScript from "tree-sitter-typescript";
import PHP from "tree-sitter-php";
import Python from "tree-sitter-python";
import Go from "tree-sitter-go";
import Rust from "tree-sitter-rust";
import Java from "tree-sitter-java";
import C from "tree-sitter-c";
import Cpp from "tree-sitter-cpp";
import CSharp from "tree-sitter-c-sharp";
import Ruby from "tree-sitter-ruby";
import Bash from "tree-sitter-bash";
import JSON from "tree-sitter-json";
import HTML from "tree-sitter-html";
import CSS from "tree-sitter-css";
import SCSS from "tree-sitter-scss";
// @ts-ignore
// import TOML from "tree-sitter-toml";
// // @ts-ignore
// import YAML from "tree-sitter-yaml";
// // @ts-ignore
// import Markdown from "tree-sitter-markdown";
// @ts-ignore
// import Dockerfile from "tree-sitter-dockerfile";
// import GraphQL from "tree-sitter-graphql";

export async function loadNativeLanguage(
    language: string
): Promise<any | null> {
    try {
        switch (language.toLowerCase()) {
            case "javascript":
            case "js":
                return JavaScript;
            case "typescript":
            case "ts":
                return TypeScript.typescript;
            case "tsx":
                return TypeScript.tsx;
            case "php":
                return PHP.php;
            case "python":
                return Python;
            case "go":
                return Go;
            case "rust":
                return Rust;
            case "java":
                return Java;
            case "c":
                return C;
            case "cpp":
            case "c++":
                return Cpp;
            case "csharp":
            case "c_sharp":
            case "c#":
                return CSharp;
            case "ruby":
                return Ruby;
            case "bash":
            case "sh":
                return Bash;
            case "json":
                return JSON;
            case "htm":
            case "html":
                return HTML;
            case "css":
                return CSS;
            case "scss":
                return SCSS;
            // case "toml":
            //     return TOML;
            // case "yaml":
            //     return YAML;
            // case "markdown":
            // case "md":
            //     return Markdown;
            // case "dockerfile":
            //     return Dockerfile;
            // case "graphql":
            //     return GraphQL;
            default:
                console.warn(`Unsupported language: ${language}`);
                return null;
        }
    } catch (e: any) {
        console.warn(
            `Failed to load native parser for ${language}: ${e.message}`
        );
        return null;
    }
}
