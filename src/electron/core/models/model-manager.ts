// Model Configuration & Management System
import path from 'path';
import fs from 'fs-extra';
import os from 'os';
import { AIN_INDEX_DIR } from '../../constants.js';

export interface LLMModel {
  id: string;
  label: string;
  provider: 'openai' | 'anthropic' | 'bedrock' | 'local' | 'google' | 'huggingface';
  enabled: boolean;
  tokenBudget: number;
  usage: 'chat' | 'code-assistant' | 'both';
  apiEndpoint?: string;
  modelFamily?: string;
}

export interface EmbeddingModel {
  id: string;
  label: string;
  provider: 'openai' | 'local' | 'huggingface' | 'bedrock';
  enabled: boolean;
  usage: 'indexing' | 'retrieval' | 'both';
  size?: string;
  status: 'running' | 'stopped' | 'downloading' | 'error';
  downloadProgress?: number;
  localPath?: string;
}

export interface ModelConfiguration {
  llmModels: LLMModel[];
  embeddingModels: EmbeddingModel[];
  preferences: {
    defaultChatModel: string;
    defaultIndexingModel: string;
    // defaultCodeAssistantModel: string;
  };
  indexingOptions: {
    maxChunkSize: number;
    minChunkSize: number;
    overlap: number;
  };
  retrievalOptions: {
    rerankThreshold: number;
    relevantCodeSnippets: number;
    relevantFilePaths: number;
  };
  apiSettings: {
    genaiUrl: string;
    authToken?: string;
    timeout: number;
  };
}

// Default LLM Models (matching VS Code extension)
export const DEFAULT_LLM_OPTIONS: LLMModel[] = [
  {
    id: 'us.deepseek.r1-v1:0',
    label: 'DeepSeek-R1',
    provider: 'bedrock',
    enabled: true,
    tokenBudget: 100_000,
    usage: 'both',
    modelFamily: 'deepseek'
  },
  {
    id: 'amazon.nova-pro-v1:0',
    label: 'Amazon Nova Pro',
    provider: 'bedrock',
    enabled: true,
    tokenBudget: 150_000,
    usage: 'both',
    modelFamily: 'nova'
  },
  {
    id: 'gpt-4o-mini',
    label: 'GPT-4o Mini',
    provider: 'openai',
    enabled: true,
    tokenBudget: 16_000,
    usage: 'both',
    modelFamily: 'gpt-4'
  },
  {
    id: 'gpt-4o',
    label: 'GPT-4o',
    provider: 'openai',
    enabled: true,
    tokenBudget: 32_000,
    usage: 'both',
    modelFamily: 'gpt-4'
  },
  {
    id: 'claude-3-5-sonnet-20241022',
    label: 'Claude 3.5 Sonnet',
    provider: 'anthropic',
    enabled: true,
    tokenBudget: 200_000,
    usage: 'both',
    modelFamily: 'claude-3'
  },
  {
    id: 'llama-3.1-8b-instruct',
    label: 'Llama 3.1 8B (Local)',
    provider: 'local',
    enabled: false,
    tokenBudget: 32_000,
    usage: 'both',
    modelFamily: 'llama'
  }
];

// Default Embedding Models
export const DEFAULT_EMBEDDING_OPTIONS: EmbeddingModel[] = [
  {
    id: 'text-embedding-3-small',
    label: 'OpenAI Text Embedding 3 Small',
    provider: 'openai',
    enabled: true,
    usage: 'both',
    status: 'running'
  },
  {
    id: 'text-embedding-3-large',
    label: 'OpenAI Text Embedding 3 Large',
    provider: 'openai',
    enabled: false,
    usage: 'both',
    status: 'stopped'
  },
  {
    id: 'xenova-all-minilm-l6-v2',
    label: 'All-MiniLM-L6-v2 (Local)',
    provider: 'local',
    enabled: true,
    usage: 'both',
    size: '90MB',
    status: 'running',
    localPath: 'Xenova/all-MiniLM-L6-v2'
  },
  {
    id: 'xenova-bge-m3',
    label: 'BGE-M3 (Local)',
    provider: 'local',
    enabled: false,
    usage: 'both',
    size: '2.2GB',
    status: 'stopped',
    localPath: 'Xenova/bge-m3'
  },
  {
    id: 'amazon.titan-embed-text-v2:0',
    label: 'Amazon Titan Embeddings V2',
    provider: 'bedrock',
    enabled: false,
    usage: 'both',
    status: 'stopped'
  }
];

export const TOKEN_BUDGETS: Record<string, number> = {
  'us.deepseek.r1-v1:0': 100_000,
  'amazon.nova-pro-v1:0': 150_000,
  'gpt-4o-mini': 16_000,
  'gpt-4o': 32_000,
  'claude-3-5-sonnet-20241022': 200_000,
  'llama-3.1-8b-instruct': 32_000,
};

export class ModelConfigManager {
  private configPath: string;
  private sharedConfigPath: string;

  constructor() {
    this.configPath = path.join(os.homedir(), AIN_INDEX_DIR, 'model-config.json');
    this.sharedConfigPath = path.join(os.homedir(), AIN_INDEX_DIR, 'shared-model-config.json');
  }

  async loadConfig(): Promise<ModelConfiguration> {
    try {
      const data = await fs.readFile(this.configPath, 'utf-8');
      const config = JSON.parse(data);
      
      // Merge with any new default models that might have been added
      return this.mergeWithDefaults(config);
    } catch (error) {
      console.log('No existing config found, creating default configuration');
      return this.getDefaultConfig();
    }
  }

  async saveConfig(config: ModelConfiguration): Promise<void> {
    try {
      // Ensure directory exists
      await fs.ensureDir(path.dirname(this.configPath));
      
      // Save main config
      await fs.writeFile(this.configPath, JSON.stringify(config, null, 2));
      
      // Create shared config for plugins
      await this.createSharedConfig(config);
      
      console.log('Model configuration saved successfully');
    } catch (error) {
      console.error('Failed to save model configuration:', error);
      throw error;
    }
  }

  private getDefaultConfig(): ModelConfiguration {
    return {
      llmModels: [...DEFAULT_LLM_OPTIONS],
      embeddingModels: [...DEFAULT_EMBEDDING_OPTIONS],
      preferences: {
        defaultChatModel: 'gpt-4o-mini',
        defaultIndexingModel: 'xenova-all-minilm-l6-v2',
        // defaultCodeAssistantModel: 'gpt-4o-mini'
      },
      indexingOptions: {
        maxChunkSize: 1000,
        minChunkSize: 100,
        overlap: 40
      },
      retrievalOptions: {
        rerankThreshold: 0.17,
        relevantCodeSnippets: 5,
        relevantFilePaths: 10
      },
      apiSettings: {
        genaiUrl: process.env.GENAI_URL || 'https://api.example.com',
        authToken: process.env.AUTH_TOKEN,
        timeout: 30000
      }
    };
  }

  private mergeWithDefaults(existingConfig: Partial<ModelConfiguration>): ModelConfiguration {
    const defaultConfig = this.getDefaultConfig();
    
    return {
      llmModels: this.mergeModels(existingConfig.llmModels || [], DEFAULT_LLM_OPTIONS),
      embeddingModels: this.mergeModels(existingConfig.embeddingModels || [], DEFAULT_EMBEDDING_OPTIONS),
      preferences: { ...defaultConfig.preferences, ...existingConfig.preferences },
      indexingOptions: { ...defaultConfig.indexingOptions, ...existingConfig.indexingOptions },
      retrievalOptions: { ...defaultConfig.retrievalOptions, ...existingConfig.retrievalOptions },
      apiSettings: { ...defaultConfig.apiSettings, ...existingConfig.apiSettings }
    };
  }

  private mergeModels<T extends { id: string; enabled: boolean }>(
    existing: T[], 
    defaults: T[]
  ): T[] {
    const existingMap = new Map(existing.map(model => [model.id, model]));
    
    return defaults.map(defaultModel => {
      const existingModel = existingMap.get(defaultModel.id);
      return existingModel ? { ...defaultModel, ...existingModel } : defaultModel;
    });
  }

  private async createSharedConfig(config: ModelConfiguration): Promise<void> {
    // Create simplified config for VS Code extension compatibility
    const sharedConfig = {
      llmOptions: config.llmModels.filter(m => m.enabled).map(model => ({
        id: model.id,
        label: model.label,
        enabled: model.enabled,
        tokenBudget: model.tokenBudget,
        provider: model.provider
      })),
      embeddingOptions: config.embeddingModels.filter(m => m.enabled).map(model => ({
        id: model.id,
        label: model.label,
        enabled: model.enabled,
        provider: model.provider,
        status: model.status
      })),
      preferences: config.preferences,
      indexingOptions: config.indexingOptions,
      retrievalOptions: config.retrievalOptions,
      lastUpdated: new Date().toISOString()
    };

    await fs.writeFile(this.sharedConfigPath, JSON.stringify(sharedConfig, null, 2));
  }

  async getEnabledLLMModels(): Promise<LLMModel[]> {
    const config = await this.loadConfig();
    return config.llmModels.filter(model => model.enabled);
  }

  async getEnabledEmbeddingModels(): Promise<EmbeddingModel[]> {
    const config = await this.loadConfig();
    return config.embeddingModels.filter(model => model.enabled);
  }

  async updateModelStatus(modelId: string, status: EmbeddingModel['status'], progress?: number): Promise<void> {
    const config = await this.loadConfig();
    const model = config.embeddingModels.find(m => m.id === modelId);
    
    if (model) {
      model.status = status;
      if (progress !== undefined) {
        model.downloadProgress = progress;
      }
      await this.saveConfig(config);
    }
  }
}
