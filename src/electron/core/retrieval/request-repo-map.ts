import generateRepoMap from "./generate-repo-map.js";
import { OpenAI, ClientOptions } from "openai";
import fs from "node:fs";
import {
    countTokensAsync,
    resolveRelativePathInDir,
    walkDirs,
} from "../utils.js";
import { Chunk } from "../types.js";
import {
    ContextLimits,
    IntelligentFileReader,
} from "./intelligent-file-reader.js";
import stateManager from "../../global-state.js";
import log from "electron-log";

// Define types for config and ide as per your implementation
interface ModelRolesConfig {
    repoMapFileSelection?: boolean;
}

interface ExperimentalConfig {
    modelRoles?: ModelRolesConfig;
}

interface Config {
    experimental?: ExperimentalConfig;
}

interface Ide {
    // Define the properties/methods you use from your IDE instance
}

const openai = new OpenAI({
    apiKey: "********************************************************************************************************************************************************************",
} as ClientOptions);

const SUPPORTED_MODEL_TITLE_FAMILIES = [
    "claude-3",
    "llama3.1",
    "llama3.2",
    "gemini-1.5",
    "gpt-4",
];

function isSupportedModel(config: Config, modelTitle?: string): boolean {
    if (config.experimental?.modelRoles?.repoMapFileSelection) {
        return true;
    }

    if (!modelTitle) {
        return false;
    }

    const lowercaseModelTitle = modelTitle.toLowerCase();

    return SUPPORTED_MODEL_TITLE_FAMILIES.some((title) =>
        lowercaseModelTitle.includes(title)
    );
}

async function getOpenAIResponse(prompt: string): Promise<string> {
    const controller = new AbortController();
    const signal = controller.signal;

    const response = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
            { role: "user", content: prompt },
            { role: "assistant", content: "<reasoning>" },
        ],
        signal,
    } as any);

    const content = response.choices?.[0]?.message?.content ?? "";
    return renderChatMessage(content);
}

// You need to implement or import renderChatMessage
function renderChatMessage(content: string): string {
    // Dummy implementation, replace with your actual logic
    return content;
}

async function requestFilesFromRepoMapByLLM(
    workspaceDir: string,
    defaultLlm: string,
    config: Config,
    ide: Ide,
    input: string,
    filterDirUri?: string
): Promise<any> {
    try {
        const repoMap = await walkDirs(workspaceDir);

        const prompt = `${repoMap}

Given the above repo map, your task is to decide which files are most likely to be relevant in answering a question. Before giving your answer, you should write your reasoning about which files/folders are most important. This thinking should start with a <reasoning> tag, followed by a paragraph explaining your reasoning, and then a closing </reasoning> tag on the last line.

After this, your response should begin with a <results> tag, followed by a list of each file, one per line, and then a closing </results> tag on the last line. You should select between 5 and 10 files. The names that you list should be the exact relative path that you saw in the repo map, not just the basename of the file.

This is the question that you should select relevant files for: "${input}"`;

        const response = await openai.chat.completions.create({
            model: "gpt-4.1",
            messages: [
                { role: "user", content: prompt },
                { role: "assistant", content: "<reasoning>" },
            ],
        } as any);

        const content = response.choices?.[0]?.message?.content ?? "";

        if (!content.includes("\n")) {
            return [];
        }

        const filepaths = content
            .split("<results>")[1]
            ?.split("</results>")[0]
            ?.split("\n")
            .filter(Boolean)
            .map((filepath) => filepath.trim());

        // Configure context limits based on your model
        const contextLimits: ContextLimits = {
            maxTokens: 100000, // Adjust based on your model's context window
            maxFiles: 12,
            maxFileSize: 30000, // Max characters per file
            priorityFiles: [
                /\.(ts|js|tsx|jsx)$/,
                /\.(py)$/,
                /README/i,
                /package\.json$/,
                /config/i,
            ],
        };

        const fileReader = new IntelligentFileReader(contextLimits);

        // Read files intelligently
        const fileContents = await fileReader.readFilesIntelligently(
            filepaths,
            input,
            workspaceDir
        );

        return fileContents;
    } catch (e) {
        console.debug("Error requesting files from repo map", e);
        return [];
    }
}

async function requestFilesFromRepoMapByEmbeddings(
    input: string,
    workspaceDir: string,
    filepaths: string[]
): Promise<any> {
    try {
        // console.log({ input, workspaceDir, filepaths });

        const fileReader = new IntelligentFileReader({
            ...stateManager.repoMapRetrievalOptions,
        });

        // Read files intelligently
        const fileContents = await fileReader.readFilesIntelligently(
            filepaths,
            input,
            workspaceDir
        );

        const repoMapContext = fileContents
            .map((c: any) => c.content)
            .join("\n\n");

        const repoMapTokenCount = await countTokensAsync(repoMapContext);

        log.debug({
            repoMapTokenCount,
        });

        return fileContents;
    } catch (e) {
        console.debug("Error requesting files from repo map", e);
        return [];
    }
}

export {
    requestFilesFromRepoMapByLLM,
    requestFilesFromRepoMapByEmbeddings,
    isSupportedModel,
    getOpenAIResponse,
};
