import * as fs from "node:fs";
import { CodeSnippetsCodebaseIndex } from "../indexing/snippets-index.js";
import {
    findUriInDirs,
    pruneLinesFromTop,
    getRepoMapFilePath,
    getWorkspaceDirs,
    walkDirs,
    countTokensAsync,
} from "../utils.js";

interface Options {
    // Define any known options properties here
}

class RepoMapGenerator {
    ide: any;
    options: Options;
    maxRepoMapTokens: number;
    repoMapPath: string;
    writeStream: fs.WriteStream;
    contentTokens: number;
    dirs: string[];
    allUris: string[];
    pathsInDirsWithSnippets: Set<string>;
    SNIPPETS_BATCH_SIZE: number;
    URI_BATCH_SIZE: number;
    REPO_MAX_CONTEXT_LENGTH_RATIO: number;
    PREAMBLE: string;
    workspaceDir: string;

    constructor(ide: any, options: Options, workspaceDir: string) {
        this.ide = ide;
        this.options = options;
        this.maxRepoMapTokens = 8000 * 0.5;
        this.repoMapPath = getRepoMapFilePath();
        this.writeStream = fs.createWriteStream(this.repoMapPath);
        this.contentTokens = 0;
        this.dirs = [];
        this.allUris = [];
        this.pathsInDirsWithSnippets = new Set();
        this.SNIPPETS_BATCH_SIZE = 100;
        this.URI_BATCH_SIZE = 100;
        this.REPO_MAX_CONTEXT_LENGTH_RATIO = 0.5;
        this.PREAMBLE =
            "Below is a repository map. \n" +
            "For each file in the codebase, " +
            "this map contains the name of the file, and the signature for any " +
            "classes, methods, or functions in the file.\n\n";
        this.workspaceDir = workspaceDir;
    }

    getUriForWrite(uri: string): string {
        return findUriInDirs(uri, this.dirs).relativePathOrBasename;
    }

    async readFile(filepath: string): Promise<string> {
        try {
            return await fs.promises.readFile(filepath, "utf-8");
        } catch (e: any) {
            console.warn(`Failed to read ${filepath}: ${e.message}`);
            return "";
        }
    }

    async writeToStream(content: string): Promise<void> {
        const tokens = await countTokensAsync(content);
        // console.log({ contentTokens: this.contentTokens, tokens });

        if (this.contentTokens + tokens > this.maxRepoMapTokens) {
            console.log("Came into prune");
            content = await pruneLinesFromTop(
                content,
                this.maxRepoMapTokens - this.contentTokens
            );
        }

        this.contentTokens += await countTokensAsync(content);

        await new Promise((resolve) =>
            this.writeStream.write(content, resolve)
        );
    }

    async generate(): Promise<string | undefined> {
        try {
            // this.dirs = await getWorkspaceDirs();
            this.allUris = await walkDirs(this.workspaceDir);

            await this.writeToStream(this.PREAMBLE);

            if (true) {
                let snippetOffset = 0;
                let uriOffset = 0;
                while (true) {
                    const { groupedByUri, hasMoreSnippets, hasMoreUris } =
                        CodeSnippetsCodebaseIndex.getPathsAndSignatures(
                            this.allUris,
                            uriOffset,
                            this.URI_BATCH_SIZE,
                            snippetOffset,
                            this.SNIPPETS_BATCH_SIZE
                        );
                    for (const [uri, signatures] of Object.entries(
                        groupedByUri
                    )) {
                        let fileContent: string;

                        try {
                            fileContent = await this.readFile(uri);
                        } catch (err: any) {
                            console.error(
                                "Failed to read file:\n" +
                                    `  Uri: ${uri}\n` +
                                    `  Error: ${
                                        err instanceof Error
                                            ? err.message
                                            : String(err)
                                    }`
                            );
                            continue;
                        }

                        const filteredSignatures = signatures.filter(
                            (signature) =>
                                signature.trim() !== fileContent.trim()
                        );

                        if (filteredSignatures.length > 0) {
                            this.pathsInDirsWithSnippets.add(uri);
                        }

                        let content = `${this.getUriForWrite(uri)}:\n`;

                        for (const signature of signatures.slice(0, -1)) {
                            content += `${this.indentMultilineString(
                                signature
                            )}\n\t...\n`;
                        }

                        content += `${this.indentMultilineString(
                            signatures[signatures.length - 1]
                        )}\n\n`;

                        if (content) {
                            await this.writeToStream(content);
                        }
                    }
                    if (this.contentTokens >= this.maxRepoMapTokens) {
                        break;
                    }
                    if (hasMoreSnippets) {
                        snippetOffset += this.SNIPPETS_BATCH_SIZE;
                    }
                    if (hasMoreUris) {
                        uriOffset += this.URI_BATCH_SIZE;
                    } else {
                        break;
                    }
                }

                const urisWithoutSnippets = this.allUris.filter(
                    (uri) => !this.pathsInDirsWithSnippets.has(uri)
                );

                if (urisWithoutSnippets.length > 0) {
                    await this.writeToStream(
                        urisWithoutSnippets
                            .map((uri) => this.getUriForWrite(uri))
                            .join("\n")
                    );
                }
            } else {
                console.log({ allUris: this.allUris });
                await this.writeToStream(
                    this.allUris
                        .map((uri) => this.getUriForWrite(uri))
                        .join("\n")
                );
            }

            this.writeStream.end();

            if (this.contentTokens >= this.maxRepoMapTokens) {
                console.debug(
                    "Full repo map was unable to be generated due to context window limitations"
                );
            }

            return await fs.promises.readFile(this.repoMapPath, "utf-8");
        } catch (error) {
            console.log("Error while generating repo map", error);
        }
    }

    indentMultilineString(str: string): string {
        return str
            .split("\n")
            .map((line) => "\t" + line)
            .join("\n");
    }
}

async function generateRepoMap(
    ide: any,
    options: Options,
    workspaceDir: string
) {
    const generator = new RepoMapGenerator(ide, options, workspaceDir);
    return generator.generate();
}

export default generateRepoMap;
