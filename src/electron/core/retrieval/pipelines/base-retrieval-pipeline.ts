// @ts-ignore
import nlp from "wink-nlp-utils";
import fs from "fs-extra";
import { FullTextSearchCodebaseIndex } from "../../indexing/fts-index.js";
import { LanceDbIndex } from "../../indexing/lance-index.js";
import { embeddingsProvider } from "../../indexing/index-initializer.js";
import { EmbeddingsRetrieveResult, RetrieveConfig, Tag } from "../../types.js";

class BaseRetrievalPipeline {
    options: RetrieveConfig;
    ftsIndex: FullTextSearchCodebaseIndex;
    lanceDbIndex: LanceDbIndex | null;

    constructor(options: RetrieveConfig) {
        this.options = options;
        this.ftsIndex = new FullTextSearchCodebaseIndex();
        this.lanceDbIndex = null;
        this.initLanceDb();
    }

    async readFile(filepath: string): Promise<string> {
        try {
            return await fs.promises.readFile(filepath, "utf-8");
        } catch (e: any) {
            console.warn(`Failed to read ${filepath}: ${e.message}`);
            return "";
        }
    }

    async initLanceDb(): Promise<void> {
        try {
            this.lanceDbIndex = await LanceDbIndex.create(
                embeddingsProvider,
                this.readFile.bind(this)
            );
        } catch (error) {
            console.log("Error while init lanace....", error);
        }
    }

    getCleanedTrigrams(query: string): string[] {
        let text = nlp.string.removeExtraSpaces(query);
        text = nlp.string.stem(text);

        let tokens = nlp.string
            .tokenize(text, true)
            .filter((token: any) => token.tag === "word")
            .map((token: any) => token.value);

        tokens = nlp.tokens.removeWords(tokens);
        tokens = nlp.tokens.setOfWords(tokens);

        const cleanedTokens = [...tokens].join(" ");
        const trigrams = nlp.string.ngram(cleanedTokens, 3);

        return trigrams.map(this.escapeFtsQueryString.bind(this));
    }

    escapeFtsQueryString(query: string): string {
        const escapedDoubleQuotes = query.replace(/"/g, '""');
        return `"${escapedDoubleQuotes}"`;
    }

    async retrieveFts(
        args: { query: string; tags: Tag[]; filterDirectory?: string },
        n: number
    ): Promise<any[]> {
        if (args.query.trim() === "") {
            return [];
        }

        const tokens = this.getCleanedTrigrams(args.query).join(" OR ");

        return await this.ftsIndex.retrieve({
            n,
            text: tokens,
            tags: args.tags,
            filterPaths: args.filterDirectory ? [args.filterDirectory] : [],
        });
    }

    async retrieveEmbeddings(
        input: string,
        workspaceDir: string,
        n: number
    ): Promise<EmbeddingsRetrieveResult> {
        const tags = [
            {
                directory: workspaceDir,
                branch: "main",
                artifactId: "fts",
            },
        ];
        await this.initLanceDb();
        if (!this.lanceDbIndex) {
            console.warn(
                "LanceDB index not available, skipping embeddings retrieval"
            );
            return { filePathEmbeddingsData: [], codeEmbeddingsData: [] };
        }

        const { filePathEmbeddingsData, codeEmbeddingsData } =
            await this.lanceDbIndex.retrieve(
                input,
                n,
                tags,
                this.options.filterDirectory
            );

        return { filePathEmbeddingsData, codeEmbeddingsData };
    }

    async run(args: any): Promise<any> {
        throw new Error("Not implemented");
    }
}

export default BaseRetrievalPipeline;
