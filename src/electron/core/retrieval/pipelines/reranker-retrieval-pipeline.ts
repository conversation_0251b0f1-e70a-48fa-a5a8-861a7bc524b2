import { findUriInDirs, deduplicateChunks } from "../../utils.js";
import BaseRetrievalPipeline from "./base-retrieval-pipeline.js";
import { Chunk, RerankerRetrievalPipelineOptions } from "../../types.js";
import BedrockReranker from "../../llms/bedrock.js";

const reRankBedrockModel = new BedrockReranker({
    region: "",
    model: "",
    profile: "",
    apiBase: "",
});

class RerankerRetrievalPipeline extends BaseRetrievalPipeline {
    options: RerankerRetrievalPipelineOptions;

    constructor(options: RerankerRetrievalPipelineOptions) {
        super(options);
        this.options = options;
    }

    async _retrieveInitial(args: any): Promise<Chunk[]> {
        const { input, nRetrieve, filterDirectory, config } = this.options;

        let retrievalResults: Chunk[] = [];

        let ftsChunks: Chunk[] = [];
        try {
            ftsChunks = await this.retrieveFts(
                {
                    ...args,
                    query: this.options.input,
                },
                nRetrieve
            );
        } catch (error) {
            console.error("Error retrieving FTS chunks:", error);
        }

        let embeddingsChunks: any[] = [];
        try {
            // embeddingsChunks = config.selectedModelByRole.embed
            //     ? await this.retrieveEmbeddings(input, nRetrieve)
            //     : [];
            const { filePathEmbeddingsData, codeEmbeddingsData } =
                await this.retrieveEmbeddings(
                    input,
                    this.options.workspaceDir,
                    nRetrieve
                );
            embeddingsChunks = codeEmbeddingsData;
        } catch (error) {
            console.error("Error retrieving embeddings chunks:", error);
        }

        let repoMapChunks: Chunk[] = [];
        // try {
        //     repoMapChunks = await requestFilesFromRepoMap(
        //         this.options.llm,
        //         this.options.config,
        //         this.options.ide,
        //         input,
        //         filterDirectory
        //     );
        // } catch (error) {
        //     console.error("Error retrieving repo map chunks:", error);
        // }

        retrievalResults.push(
            ...ftsChunks,
            ...embeddingsChunks,
            ...repoMapChunks
        );

        if (filterDirectory) {
            retrievalResults = retrievalResults.filter(
                (chunk) =>
                    !!findUriInDirs(chunk.filepath!, [filterDirectory])
                        .foundInDir
            );
        }

        const deduplicatedRetrievalResults =
            deduplicateChunks(retrievalResults);

        return deduplicatedRetrievalResults;
    }

    async _rerank(input: string, chunks: Chunk[]): Promise<any> {
        // Filter out chunks with undefined or empty content
        chunks = chunks.filter(
            (chunk) => chunk.content && chunk.content.trim() !== ""
        );

        // console.log({ _rerank: input, chunks });

        if (!chunks.length) {
            return [];
        }

        try {
            // Get relevance scores from BedrockReranker
            let scores = await reRankBedrockModel.rerank(input, chunks);

            // Map chunks to their original indices
            const chunkIndexMap = new Map<Chunk, number>();
            chunks.forEach((chunk, idx) => chunkIndexMap.set(chunk, idx));

            // Sort chunks by scores in descending order (higher score = more relevant)
            let results = chunks.slice();
            results.sort(
                (a, b) =>
                    scores[chunkIndexMap.get(b)!] -
                    scores[chunkIndexMap.get(a)!]
            );

            // Return top nFinal chunks
            return results.slice(0, this.options.nFinal);
        } catch (e) {
            // this.options.ide.showToast(
            //     "warning",
            //     `Failed to rerank retrieval results\n${e}`
            // );
            console.log("Error in reranking", e);
            return chunks.slice(0, this.options.nFinal);
        }
    }

    // async _expandWithEmbeddings(chunks: Chunk[]): Promise<Chunk[]> {
    //     const topResults = chunks.slice(
    //         -RETRIEVAL_PARAMS.nResultsToExpandWithEmbeddings
    //     );

    //     const expanded = await Promise.all(
    //         topResults.map(async (chunk) => {
    //             const results = await this.retrieveEmbeddings(
    //                 chunk.content!,
    //                 this.options.workspaceDir,
    //                 RETRIEVAL_PARAMS.nEmbeddingsExpandTo
    //             );
    //             return results;
    //         })
    //     );
    //     return expanded.flat();
    // }

    // async _expandRankedResults(chunks: Chunk[]): Promise<Chunk[]> {
    //     let results: Chunk[] = [];

    //     const embeddingsResults = await this._expandWithEmbeddings(chunks);
    //     results.push(...embeddingsResults);

    //     return results;
    // }

    async run(args: any): Promise<Chunk[]> {
        let results = await this._retrieveInitial(args);
        results = await this._rerank(args.query, results);

        return results;
    }
}

export default RerankerRetrievalPipeline;
