import {
    requestFilesFromRepoMapByEmbeddings,
    requestFilesFromRepoMapByLLM,
} from "../request-repo-map.js";
import { deduplicateChunks, findUriInDirs } from "../../utils.js";
import BaseRetrievalPipeline from "./base-retrieval-pipeline.js";
import { NoRerankerRetrievalPipelineOptions } from "../../types.js";
import stateManager from "../../../global-state.js";

class NoRerankerRetrievalPipeline extends BaseRetrievalPipeline {
    options: NoRerankerRetrievalPipelineOptions;

    constructor(options: NoRerankerRetrievalPipelineOptions) {
        super(options);
        this.options = options;
    }

    async run(args: any) {
        const { input, filterDirectory = undefined } = this.options;

        if (!input) {
            return [];
        }

        let retrievalResults: any[] = [];

        let ftsChunks: any[] = [];
        try {
            ftsChunks = await this.retrieveFts(
                { ...args, query: input },
                stateManager.retrievalParams.ftsNFinal
            );
        } catch (error) {
            console.error("Error retrieving FTS chunks:", error);
        }

        let embeddingsChunks: any[] = [];
        let filepathChunks: any[] = [];
        try {
            const { filePathEmbeddingsData, codeEmbeddingsData } =
                await this.retrieveEmbeddings(
                    input,
                    this.options.workspaceDir,
                    stateManager.retrievalParams.embeddingsNFinal
                );
            embeddingsChunks = codeEmbeddingsData;
            filepathChunks = filePathEmbeddingsData;
        } catch (error) {
            console.error("Error retrieving embeddings:", error);
        }

        let repoMapChunks: any[] = [];

        try {
            repoMapChunks = await requestFilesFromRepoMapByEmbeddings(
                input,
                this.options.workspaceDir,
                filepathChunks.map((d) => d.content)
            );
        } catch (error) {
            console.error("Error retrieving repo map chunks:", error);
        }

        retrievalResults.push(
            ...ftsChunks,
            ...embeddingsChunks,
            ...repoMapChunks
        );

        if (filterDirectory) {
            retrievalResults = retrievalResults.filter(
                (chunk) =>
                    !!findUriInDirs(chunk.filepath, [filterDirectory])
                        .foundInDir
            );
        }

        const deduplicatedRetrievalResults =
            deduplicateChunks(retrievalResults);

        return deduplicatedRetrievalResults;
    }
}

export default NoRerankerRetrievalPipeline;
