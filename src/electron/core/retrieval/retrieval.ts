import { OpenAI } from "openai";

import NoRerankerRetrievalPipeline from "./pipelines/no-reranker-retrieval-pipeline.js";
import { countTokensAsync, getUriDescription } from "../utils.js";
import { ContextType, RerankerRetrievalPipelineOptions } from "../types.js";
import stateManager from "../../global-state.js";
import log from "electron-log";

const INSTRUCTIONS_BASE_ITEM = {
    name: "Instructions",
    description: "Instructions",
    content: "",
    hidden: true,
};

async function callLLM(finalContext: string, userQuery: string) {
    const openai = new OpenAI({
        apiKey: "********************************************************************************************************************************************************************",
    });

    const messages: any = [
        {
            role: "system",
            content:
                "You are a helpful Software Developer who understands source code and provides explanations based on provided context.",
        },
        {
            role: "user",
            content: `Here are the most relevant code chunks:\n\n${finalContext}\n\nNow answer this: ${userQuery}`,
        },
    ];

    try {
        const response = await openai.chat.completions.create({
            model: "gpt-4.1", // or "gpt-4o" / "gpt-3.5-turbo"
            messages,
            temperature: 0.4,
        });

        const llmResponse = response.choices[0]?.message?.content;
        console.log("\n💬 LLM Response:\n", llmResponse);
    } catch (err: any) {
        console.error("LLM call failed:", err.message);
    }
}

export function mergeLineRanges(
    ranges: { startLine: number; endLine: number }[]
) {
    if (ranges.length === 0) return [];

    // Sort by startLine
    ranges.sort((a, b) => a.startLine - b.startLine);

    const merged = [ranges[0]];
    for (let i = 1; i < ranges.length; i++) {
        const prev = merged[merged.length - 1];
        const current = ranges[i];

        if (current.startLine <= prev.endLine + 1) {
            // Overlapping or contiguous, merge
            prev.endLine = Math.max(prev.endLine, current.endLine);
        } else {
            // No overlap, add new
            merged.push({ ...current });
        }
    }

    return merged;
}

export async function retrieveContextItemsFromEmbeddings(
    query: string,
    workspaceDir: string,
    modelId: string,
    availableTokens: number
): Promise<ContextType> {
    try {
        log.debug({ availableTokens });
        stateManager.setModelContext("alpine-intellect-1.0:0", availableTokens);

        const fileLineRangesMap = new Map<
            string,
            { startLine: number; endLine: number }[]
        >();

        const includeEmbeddings = true;

        const tags = [
            {
                directory: workspaceDir,
                branch: "main",
                artifactId: "codeSnippets",
            },
        ];

        const pipelineType = NoRerankerRetrievalPipeline;

        const pipelineOptions = {
            filterDirectory: "",
            input: query,
            workspaceDir,
        };

        const pipeline = new pipelineType(
            pipelineOptions as RerankerRetrievalPipelineOptions
        );
        const results = await pipeline.run({
            tags,
            filterDirectory: "",
            includeEmbeddings,
            query,
        });

        if (results.length === 0) {
            console.log("No results found...");
            return { contextText: "", usedReferences: {} };
        }

        const finalContext = [
            {
                ...INSTRUCTIONS_BASE_ITEM,
                content:
                    "Use the above code to answer the following question. You should not reference any files outside of what is shown, unless they are commonly known files, like a .gitignore or package.json. Reference the filenames whenever possible. If there isn't enough information to answer the question, suggest where the user might look to learn more.",
            },
            ...results
                .sort((a: any, b: any) => a.filepath.localeCompare(b.filepath))
                .map((r: any) => {
                    const { relativePathOrBasename, last2Parts, baseName } =
                        getUriDescription(r.filepath, [workspaceDir]);

                    if (baseName === "package.json") {
                        console.warn(
                            "Retrieval pipeline: package.json detected"
                        );
                    }

                    if (!fileLineRangesMap.has(r.filepath)) {
                        fileLineRangesMap.set(r.filepath, []);
                    }
                    fileLineRangesMap.get(r.filepath)!.push({
                        startLine: r.startLine,
                        endLine: r.endLine,
                    });

                    return {
                        name: `${baseName} (${r.startLine + 1}-${
                            r.endLine + 1
                        })`,
                        description: last2Parts,
                        content: `\`\`\`${relativePathOrBasename}\n${r.content}\n\`\`\``,
                        uri: {
                            type: "file",
                            value: r.filepath,
                        },
                    };
                }),
        ];

        // console.log({ finalContext });
        // console.log(
        //     util.inspect(finalContext, { depth: null, breakLength: Infinity })
        // );

        const contextText = finalContext
            .map((c: any) => c.content)
            .join("\n\n");

        const finalTokenCount = await countTokensAsync(contextText);

        const usedReferences: Record<
            string,
            { startLine: number; endLine: number }[]
        > = {};

        for (const [filepath, ranges] of fileLineRangesMap.entries()) {
            usedReferences[filepath] = mergeLineRanges(ranges);
        }

        // console.log({ usedReferences });

        log.debug({
            finalTokenCount,
        });

        return { contextText, usedReferences };
    } catch (error) {
        console.log("Error while retrieving context...", error);
        return { contextText: "", usedReferences: {} };
    }
}

// const query =
//     "How can I create a new kafka consumer for integrating new azure devops provider?";
// retrieveContextItemsFromEmbeddings(query).then((res) => {
//     console.log({ finalContext: res });
//     callLLM(res, query);
// });
