import fs from "fs-extra";
import { resolveFullFilePath } from "../utils.js";

interface FileContent {
    filepath: string;
    content: string;
    size: number;
    lineCount: number;
    truncated: boolean;
    startLine: number;
    endLine: number;
    totalLines: number;
}

export interface ContextLimits {
    maxTokens: number;
    maxFiles: number;
    maxFileSize: number;
    priorityFiles?: any;
}

const STOPWORDS = new Set([
    "a",
    "an",
    "the",
    "to",
    "in",
    "on",
    "for",
    "by",
    "from",
    "of",
    "and",
    "or",
    "at",
    "with",
    "that",
    "which",
    "do",
    "does",
    "did",
    "doing",
    "is",
    "are",
    "was",
    "were",
    "be",
    "been",
    "have",
    "has",
    "had",
    "it",
    "this",
    "these",
    "those",
    "as",
    "if",
    "then",
    "so",
    "such",
    "but",
    "can",
    "will",
    "would",
    "our",
    "we",
    "you",
    "your",
    "how",
    "what",
    "when",
    "where",
    "why",
]);

function tokenize(text: string): string[] {
    return text
        .toLowerCase()
        .replace(/[^a-z0-9\s\-_/]/g, "") // remove special characters
        .split(/\s+/)
        .filter(Boolean); // remove empty tokens
}

function extractKeywords(query: string): string[] {
    const tokens = tokenize(query);
    const keywords: Set<string> = new Set();

    for (const token of tokens) {
        if (!STOPWORDS.has(token) && token.length > 2) {
            keywords.add(token);
        }
    }

    for (let n = 2; n <= 3; n++) {
        for (let i = 0; i <= tokens.length - n; i++) {
            const group = tokens.slice(i, i + n);
            if (group.every((w) => !STOPWORDS.has(w))) {
                keywords.add(group.join(" "));
            }
        }
    }

    return [...keywords];
}

export class IntelligentFileReader {
    private readonly contextLimits: ContextLimits;

    constructor(contextLimits: ContextLimits) {
        this.contextLimits = contextLimits;
    }

    private estimateTokens(text: string): number {
        return Math.ceil(text.length / 4);
    }

    private prioritizeFiles(filepaths: string[], query: string): string[] {
        const keywords = extractKeywords(query);
        const ngrams = this.generateNgramsFromKeywords(keywords);

        // console.log({ filepaths });
        // console.dir(ngrams);
        console.log("Ngrams Size:", ngrams.size);

        const prioritized = filepaths.sort((a, b) => {
            const scoreA = this.calculateRelevanceScore(a, query, ngrams);
            const scoreB = this.calculateRelevanceScore(b, query, ngrams);
            return scoreB - scoreA;
        });

        return prioritized;
    }

    private generateNgramsFromKeywords(keywords: string[]): Set<string> {
        const ngrams = new Set<string>();

        for (const keyword of keywords) {
            const words = keyword.trim().split(/\s+/);
            const dash = words.join("-");
            const underscore = words.join("_");
            const noSpace = words.join("");
            const pascal = words
                .map((w) => w.charAt(0).toUpperCase() + w.slice(1))
                .join("");
            const camel = words
                .map((w, i) =>
                    i === 0 ? w : w.charAt(0).toUpperCase() + w.slice(1)
                )
                .join("");

            ngrams.add(dash); // custom-dashboard
            ngrams.add(underscore); // custom_dashboard
            ngrams.add(noSpace); // customdashboard
            ngrams.add(pascal); // CustomDashboard
            ngrams.add(camel); // customDashboard
        }

        return ngrams;
    }

    private calculateRelevanceScore(
        filepath: string,
        query: string,
        ngrams: Set<string>
    ): number {
        let score = 0;
        const normalizedPath = filepath.toLowerCase();
        const queryLower = query.toLowerCase().trim();

        const pathParts = normalizedPath.split(/[\\/]/);
        const filename = pathParts[pathParts.length - 1];
        const folderParts = pathParts.slice(0, -1);

        const priorityExtensions = new Set([
            "ts",
            "tsx",
            "js",
            "jsx",
            "py",
            "java",
            "cpp",
            "c",
            "json",
            "go",
            "rs",
            "html",
        ]);

        const ext = filename.split(".").pop() || "";
        if (priorityExtensions.has(ext)) score += 10;

        if (filename.includes("readme")) score += 8;
        if (filename === "package.json") score += 8;
        if (filename.startsWith(".env")) score += 5;

        // const queryWords = queryLower.split(/\s+/);
        // const ngrams = new Set<string>();

        // for (let i = 0; i < queryWords.length; i++) {
        //     ngrams.add(queryWords[i]);
        //     if (i < queryWords.length - 1)
        //         ngrams.add(`${queryWords[i]}-${queryWords[i + 1]}`);
        //     if (i < queryWords.length - 2)
        //         ngrams.add(
        //             `${queryWords[i]}-${queryWords[i + 1]}-${queryWords[i + 2]}`
        //         );
        // }

        // console.log({ ngrams });

        for (const ngram of ngrams) {
            if (filename.includes(ngram)) {
                score += ngram.split("-").length * 5;
            }
            folderParts.forEach((part, depth) => {
                if (part.includes(ngram)) {
                    score += ngram.split("-").length * (6 - Math.min(depth, 4));
                }
            });
        }

        if (filename.includes("test") && !queryLower.includes("test")) {
            score -= 3;
        }

        score -= Math.max(0, pathParts.length - 6);
        return score;
    }

    private truncateContent(
        content: string,
        maxSize: number,
        filepath: string
    ) {
        const totalLines = content.split("\n").length;
        if (content.length <= maxSize) {
            return {
                content,
                truncated: false,
                startLine: 1,
                endLine: totalLines,
                totalLines,
            };
        }

        const isCodeFile = /\.(ts|js|tsx|jsx|py|java|cpp|c|h)$/.test(filepath);
        return isCodeFile
            ? this.truncateCodeFile(content, maxSize, totalLines)
            : this.truncateGenericFile(content, maxSize, totalLines);
    }

    private truncateCodeFile(
        content: string,
        maxSize: number,
        totalLines: number
    ) {
        const lines = content.split("\n");
        const importantLines: { line: string; index: number }[] = [];
        const regularLines: { line: string; index: number }[] = [];

        lines.forEach((line, index) => {
            const trimmed = line.trim();
            if (
                trimmed.startsWith("import ") ||
                trimmed.startsWith("export ") ||
                trimmed.startsWith("class ") ||
                trimmed.startsWith("function ") ||
                trimmed.startsWith("const ") ||
                trimmed.startsWith("let ") ||
                trimmed.startsWith("var ") ||
                trimmed.includes("interface ") ||
                trimmed.includes("type ") ||
                trimmed.startsWith("//") ||
                trimmed.startsWith("/*") ||
                trimmed.startsWith("*")
            ) {
                importantLines.push({ line, index });
            } else {
                regularLines.push({ line, index });
            }
        });

        let resultLines: { line: string; index: number }[] = [
            ...importantLines,
        ];
        let result = importantLines.map((item) => item.line).join("\n");

        for (const lineItem of regularLines) {
            const potential = result + "\n" + lineItem.line;
            if (potential.length > maxSize) break;
            result = potential;
            resultLines.push(lineItem);
        }

        resultLines.sort((a, b) => a.index - b.index);
        const startLine = Math.min(...resultLines.map((i) => i.index)) + 1;
        const endLine = Math.max(...resultLines.map((i) => i.index)) + 1;
        const finalContent = resultLines.map((i) => i.line).join("\n");

        return {
            content:
                finalContent +
                (resultLines.length < lines.length
                    ? "\n\n// ... [Content truncated] ..."
                    : ""),
            truncated: resultLines.length < lines.length,
            startLine,
            endLine,
            totalLines,
        };
    }

    private truncateGenericFile(
        content: string,
        maxSize: number,
        totalLines: number
    ) {
        const lines = content.split("\n");
        const halfSize = Math.floor(maxSize / 2) - 50;

        let startContent = "",
            startLineCount = 0;
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i] + (i < lines.length - 1 ? "\n" : "");
            if (startContent.length + line.length > halfSize) break;
            startContent += line;
            startLineCount++;
        }

        let endContent = "",
            endLineCount = 0;
        for (let i = lines.length - 1; i >= startLineCount; i--) {
            const line = (i > 0 ? "\n" : "") + lines[i];
            if (endContent.length + line.length > halfSize) break;
            endContent = line + endContent;
            endLineCount++;
        }

        const truncationMessage =
            "\n\n... [Content truncated - showing first and last parts] ...\n\n";
        const finalContent = startContent + truncationMessage + endContent;

        return {
            content: finalContent,
            truncated: true,
            startLine: 1,
            endLine: totalLines,
            totalLines,
        };
    }

    async readFilesIntelligently(
        filepaths: string[],
        query: string,
        workspaceDir: string
    ): Promise<FileContent[]> {
        try {
            const prioritizedFiles = this.prioritizeFiles(filepaths, query);
            // console.log({ prioritizedFiles });
            const selectedFiles = prioritizedFiles.slice(
                0,
                this.contextLimits.maxFiles
            );

            const fileContents: FileContent[] = [];
            let totalTokens = 0;

            for (const filepath of selectedFiles) {
                try {
                    const uri = await resolveFullFilePath(filepath);
                    if (!uri) continue;

                    const rawContent = await fs.promises.readFile(uri, "utf-8");
                    const fileTokens = this.estimateTokens(rawContent);

                    if (totalTokens + fileTokens > this.contextLimits.maxTokens)
                        break;

                    const {
                        content,
                        truncated,
                        startLine,
                        endLine,
                        totalLines,
                    } = this.truncateContent(
                        rawContent,
                        this.contextLimits.maxFileSize,
                        filepath
                    );

                    fileContents.push({
                        filepath,
                        content,
                        size: content.length,
                        lineCount: content.split("\n").length,
                        truncated,
                        startLine,
                        endLine,
                        totalLines,
                    });

                    totalTokens += this.estimateTokens(content);
                } catch (error) {
                    console.error(`Error reading file ${filepath}:`, error);
                }
            }

            return fileContents;
        } catch (error) {
            console.error("Error in readFilesIntelligently:", error);
            return [];
        }
    }

    formatForLLM(fileContents: FileContent[]): string {
        let context = "# Repository Files Context\n\n";
        fileContents.forEach((file, index) => {
            context += `## File ${index + 1}: ${file.filepath}\n`;
            context += file.truncated
                ? `*Note: Showing lines ${file.startLine}-${file.endLine} of ${file.totalLines} total lines*\n`
                : `*Complete file (lines 1-${file.totalLines})*\n`;
            context += `\`\`\`\n${file.content}\n\`\`\`\n\n`;
        });
        return context;
    }
}
