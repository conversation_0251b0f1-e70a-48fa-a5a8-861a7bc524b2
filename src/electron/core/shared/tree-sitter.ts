import * as fs from "node:fs";
import * as path from "path";
import Parser, { Language, SyntaxNode, Tree } from "tree-sitter";
import { getUriFileExtension, supportedLanguages } from "../utils.js";
import { IDE, SymbolInfo } from "../types.js";

import { fileURLToPath } from "url";
import { loadNativeLanguage } from "../tree-sitter/tree-sitter-languages.js";
import log from "electron-log";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const basePath =
    process.env.NODE_ENV === "development"
        ? path.join(__dirname, "../", "tree-sitter", "code-snippet-queries")
        : path.join(
              process.resourcesPath,
              "tree-sitter",
              "code-snippet-queries"
          );

export async function getParserForFile(
    filepath: string
): Promise<Parser | undefined> {
    try {
        const language = getFullLanguageName(filepath);

        if (!language) {
            return undefined;
        }
        const langModule = await loadNativeLanguage(language);
        if (!langModule) return undefined;

        const parser = new Parser();
        parser.setLanguage(langModule);

        return parser;
    } catch (e) {
        console.debug("Unable to load parser for file", filepath, e);
        return undefined;
    }
}

export function getFullLanguageName(filepath: string): string | undefined {
    const extension = getUriFileExtension(filepath);
    return supportedLanguages[extension];
}

export async function getQueryForFile(
    filepath: string,
    queryPath: string
): Promise<Parser.Query | undefined> {
    const language = getFullLanguageName(filepath);

    if (!language) {
        return undefined;
    }

    const langModule = await loadNativeLanguage(language);
    if (!langModule) return undefined;

    const sourcePath = path.join(basePath, queryPath);
    if (!fs.existsSync(sourcePath)) {
        return undefined;
    }
    const querySource = (await fs.promises.readFile(sourcePath)).toString();

    const query = new Parser.Query(langModule, querySource);
    return query;
}


const GET_SYMBOLS_FOR_NODE_TYPES: string[] = [
    "class_declaration",
    "class_definition",
    "function_item",
    "function_definition",
    "method_declaration",
    "method_definition",
    "generator_function_declaration",
];

export async function getSymbolsForFile(
    filepath: string,
    contents: string
): Promise<SymbolInfo[] | undefined> {
    const parser = await getParserForFile(filepath);
    if (!parser) {
        return;
    }
    let tree: Tree;
    try {
        tree = parser.parse(contents);
    } catch (e) {
        console.log(`Error parsing file: ${filepath}`);
        return;
    }
    const symbols: SymbolInfo[] = [];
    function findNamedNodesRecursive(node: SyntaxNode): void {
        if (GET_SYMBOLS_FOR_NODE_TYPES.includes(node.type)) {
            let identifier: SyntaxNode | undefined;
            for (let i = node.children.length - 1; i >= 0; i--) {
                if (
                    node.children[i].type === "identifier" ||
                    node.children[i].type === "property_identifier"
                ) {
                    identifier = node.children[i];
                    break;
                }
            }
            if (identifier?.text) {
                symbols.push({
                    filepath,
                    type: node.type,
                    name: identifier.text,
                    range: {
                        start: {
                            character: node.startPosition.column,
                            line: node.startPosition.row,
                        },
                        end: {
                            character: node.endPosition.column + 1,
                            line: node.endPosition.row + 1,
                        },
                    },
                    content: node.text,
                });
            }
        }
        node.children.forEach(findNamedNodesRecursive);
    }
    findNamedNodesRecursive(tree.rootNode);
    return symbols;
}

export async function getSymbolsForManyFiles(
    uris: string[],
    ide: IDE
): Promise<Record<string, SymbolInfo[]>> {
    const filesAndSymbols = await Promise.all(
        uris.map(async (uri) => {
            const contents = await ide.readFile(uri);
            let symbols: SymbolInfo[] | undefined;
            try {
                symbols = await getSymbolsForFile(uri, contents);
            } catch (e) {
                console.error(`Failed to get symbols for ${uri}:`, e);
            }
            return [uri, symbols ?? []] as [string, SymbolInfo[]];
        })
    );
    return Object.fromEntries(filesAndSymbols);
}
