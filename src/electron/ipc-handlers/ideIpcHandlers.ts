import { ipcMain } from 'electron';
import { resolveIDEExecutable } from '../ide-detector/resolveExecutable.js';
import { getIDEVersion } from '../ide-detector/versionChecker.js';
import { detectInstalledIDEs, IDEName } from '../ide-detector/detectIdeInstalled.js';
import { isPluginInstalled } from '../ide-detector/pluginDetector.js';


ipcMain.handle('get-ide-status', (_, ide: IDEName) => {
  return {
    path: resolveIDEExecutable(ide as 'vscode' | 'cursor'),
    version: getIDEVersion(ide),
    installed: detectInstalledIDEs([ide])[ide],
    pluginInstalled: isPluginInstalled(ide as 'vscode' | 'cursor', 'thealpinecode.alpineintellect'),
  };
});
