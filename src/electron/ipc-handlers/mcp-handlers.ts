// MCP IPC Handlers - Handles IPC communication for MCP operations
import { ipc<PERSON>ain, BrowserWindow } from 'electron';
import log from 'electron-log';
import { MCPManager } from '../mcp/core/mcp-manager.js';
import { GitHubPATService } from '../mcp/auth/github-pat-service.js';

let mcpManager: MCPManager | null = null;
let githubPATService: GitHubPATService | null = null;

/**
 * Initializes MCP IPC handlers
 */
export function initializeMCPHandlers(): void {
  log.info('Initializing MCP IPC handlers');

  // Initialize services
  mcpManager = new MCPManager();
  githubPATService = new GitHubPATService();

  // Set up event forwarding from MCP manager to renderer
  mcpManager.on('server-status-changed', (status) => {
    // Forward to all renderer processes
    const windows = BrowserWindow.getAllWindows();
    windows.forEach((window: Electron.BrowserWindow) => {
      window.webContents.send('mcp-server-status-changed', status);
    });
  });

  mcpManager.on('server-error', (data) => {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach((window: Electron.BrowserWindow) => {
      window.webContents.send('mcp-server-error', data);
    });
  });

  mcpManager.on('tools-updated', (data) => {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach((window: Electron.BrowserWindow) => {
      window.webContents.send('mcp-tools-updated', data);
    });
  });

  // Register IPC handlers
  registerMCPManagementHandlers();
  registerGitHubSpecificHandlers();

  log.info('MCP IPC handlers initialized successfully');
}

/**
 * Registers general MCP management handlers
 */
function registerMCPManagementHandlers(): void {
  // Get list of available MCP servers
  ipcMain.handle('mcp-get-servers', async () => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      return mcpManager.getAllServerStatuses();
    } catch (error) {
      log.error('Error getting MCP servers:', error);
      throw error;
    }
  });

  // Get specific server status
  ipcMain.handle('mcp-get-server-status', async (event, serverId: string) => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      const status = mcpManager.getServerStatus(serverId);
      if (!status) {
        throw new Error(`Server ${serverId} not found`);
      }

      return status;
    } catch (error) {
      log.error(`Error getting server status for ${serverId}:`, error);
      throw error;
    }
  });

  // Connect to MCP server
  ipcMain.handle('mcp-connect-server', async (event, serverId: string, credentials?: Record<string, string>) => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      log.info(`Connecting to MCP server: ${serverId}`);
      await mcpManager.connectServer(serverId, credentials);
      
      return { success: true };
    } catch (error) {
      log.error(`Error connecting to server ${serverId}:`, error);
      throw error;
    }
  });

  // Disconnect from MCP server
  ipcMain.handle('mcp-disconnect-server', async (event, serverId: string) => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      log.info(`Disconnecting from MCP server: ${serverId}`);
      await mcpManager.disconnectServer(serverId);
      
      return { success: true };
    } catch (error) {
      log.error(`Error disconnecting from server ${serverId}:`, error);
      throw error;
    }
  });

  // Get available tools for a server
  ipcMain.handle('mcp-get-tools', async (event, serverId: string) => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      return mcpManager.getAvailableTools(serverId);
    } catch (error) {
      log.error(`Error getting tools for server ${serverId}:`, error);
      throw error;
    }
  });

  // Execute a tool
  ipcMain.handle('mcp-execute-tool', async (event, request: {
    serverId: string;
    toolName: string;
    parameters: Record<string, any>;
  }) => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      log.info(`Executing tool ${request.toolName} on server ${request.serverId}`);
      
      const result = await mcpManager.executeTool({
        serverId: request.serverId,
        toolName: request.toolName,
        parameters: request.parameters
      });

      return result;
    } catch (error) {
      log.error(`Error executing tool ${request.toolName}:`, error);
      throw error;
    }
  });

  // Restart a server
  ipcMain.handle('mcp-restart-server', async (event, serverId: string) => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      log.info(`Restarting MCP server: ${serverId}`);
      await mcpManager.restartServer(serverId);
      
      return { success: true };
    } catch (error) {
      log.error(`Error restarting server ${serverId}:`, error);
      throw error;
    }
  });

  // Get all available tools across all servers
  ipcMain.handle('mcp-get-all-tools', async () => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      return mcpManager.getAllAvailableTools();
    } catch (error) {
      log.error('Error getting all tools:', error);
      throw error;
    }
  });

  // Additional handlers for the new MCP management interface

  // Get connected servers list
  ipcMain.handle('mcp-get-connected-servers', async () => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      return mcpManager.getConnectedServers();
    } catch (error) {
      log.error('Error getting connected servers:', error);
      throw error;
    }
  });

  // Install MCP server
  ipcMain.handle('mcp-install-server', async (event, serverId: string, config?: any) => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      log.info(`Installing MCP server: ${serverId}`);
      await mcpManager.installServer(serverId, config);

      return { success: true };
    } catch (error) {
      log.error(`Error installing server ${serverId}:`, error);
      throw error;
    }
  });

  // Configure MCP server
  ipcMain.handle('mcp-configure-server', async (event, serverId: string, config: any) => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      log.info(`Configuring MCP server: ${serverId}`);
      await mcpManager.configureServer(serverId, config);

      return { success: true };
    } catch (error) {
      log.error(`Error configuring server ${serverId}:`, error);
      throw error;
    }
  });

  // Start MCP server
  ipcMain.handle('mcp-start-server', async (event, serverId: string) => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      log.info(`Starting MCP server: ${serverId}`);
      await mcpManager.startServer(serverId);

      return { success: true };
    } catch (error) {
      log.error(`Error starting server ${serverId}:`, error);
      throw error;
    }
  });

  // Stop MCP server
  ipcMain.handle('mcp-stop-server', async (event, serverId: string) => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      log.info(`Stopping MCP server: ${serverId}`);
      await mcpManager.stopServer(serverId);

      return { success: true };
    } catch (error) {
      log.error(`Error stopping server ${serverId}:`, error);
      throw error;
    }
  });

  // Remove MCP server
  ipcMain.handle('mcp-remove-server', async (event, serverId: string) => {
    try {
      if (!mcpManager) {
        throw new Error('MCP Manager not initialized');
      }

      log.info(`Removing MCP server: ${serverId}`);
      await mcpManager.removeServer(serverId);

      return { success: true };
    } catch (error) {
      log.error(`Error removing server ${serverId}:`, error);
      throw error;
    }
  });
}

/**
 * Registers GitHub-specific handlers
 */
function registerGitHubSpecificHandlers(): void {
  // Configure GitHub PAT
  ipcMain.handle('github-mcp-configure-pat', async (event, pat: string) => {
    try {
      if (!githubPATService) {
        throw new Error('GitHub PAT Service not initialized');
      }

      log.info('Configuring GitHub PAT');
      const result = await githubPATService.storePAT(pat);
      
      if (!result.valid) {
        throw new Error(result.error || 'PAT validation failed');
      }

      return result;
    } catch (error) {
      log.error('Error configuring GitHub PAT:', error);
      throw error;
    }
  });

  // Validate GitHub PAT
  ipcMain.handle('github-mcp-validate-pat', async (event, pat?: string) => {
    try {
      if (!githubPATService) {
        throw new Error('GitHub PAT Service not initialized');
      }

      if (pat) {
        // Validate provided PAT without storing
        const validation = await githubPATService.storePAT(pat);
        return validation;
      } else {
        // Validate stored PAT
        return await githubPATService.validateStoredPAT();
      }
    } catch (error) {
      log.error('Error validating GitHub PAT:', error);
      throw error;
    }
  });

  // Get authenticated GitHub user
  ipcMain.handle('github-mcp-get-user', async () => {
    try {
      if (!githubPATService) {
        throw new Error('GitHub PAT Service not initialized');
      }

      return await githubPATService.getUser();
    } catch (error) {
      log.error('Error getting GitHub user:', error);
      throw error;
    }
  });

  // Get GitHub authentication status
  ipcMain.handle('github-mcp-get-auth-status', async () => {
    try {
      if (!githubPATService) {
        throw new Error('GitHub PAT Service not initialized');
      }

      return await githubPATService.getAuthStatus();
    } catch (error) {
      log.error('Error getting GitHub auth status:', error);
      throw error;
    }
  });

  // Clear GitHub credentials
  ipcMain.handle('github-mcp-clear-credentials', async () => {
    try {
      if (!githubPATService) {
        throw new Error('GitHub PAT Service not initialized');
      }

      log.info('Clearing GitHub credentials');
      await githubPATService.clearCredentials();
      
      return { success: true };
    } catch (error) {
      log.error('Error clearing GitHub credentials:', error);
      throw error;
    }
  });

  // Refresh GitHub user info
  ipcMain.handle('github-mcp-refresh-user', async () => {
    try {
      if (!githubPATService) {
        throw new Error('GitHub PAT Service not initialized');
      }

      return await githubPATService.refreshUserInfo();
    } catch (error) {
      log.error('Error refreshing GitHub user info:', error);
      throw error;
    }
  });

  // Check if GitHub credentials are stored
  ipcMain.handle('github-mcp-has-credentials', async () => {
    try {
      if (!githubPATService) {
        throw new Error('GitHub PAT Service not initialized');
      }

      return await githubPATService.hasStoredCredentials();
    } catch (error) {
      log.error('Error checking GitHub credentials:', error);
      throw error;
    }
  });
}

/**
 * Shuts down MCP handlers and cleans up resources
 */
export async function shutdownMCPHandlers(): Promise<void> {
  try {
    log.info('Shutting down MCP handlers');

    // Remove all MCP-related IPC handlers
    const mcpHandlers = [
      'mcp-get-servers',
      'mcp-get-server-status',
      'mcp-connect-server',
      'mcp-disconnect-server',
      'mcp-get-tools',
      'mcp-execute-tool',
      'mcp-restart-server',
      'mcp-get-all-tools',
      'github-mcp-configure-pat',
      'github-mcp-validate-pat',
      'github-mcp-get-user',
      'github-mcp-get-auth-status',
      'github-mcp-clear-credentials',
      'github-mcp-refresh-user',
      'github-mcp-has-credentials'
    ];

    mcpHandlers.forEach(handler => {
      ipcMain.removeAllListeners(handler);
    });

    // Shutdown MCP manager
    if (mcpManager) {
      await mcpManager.shutdown();
      mcpManager = null;
    }

    // Clear PAT service
    githubPATService = null;

    log.info('MCP handlers shutdown complete');

  } catch (error) {
    log.error('Error shutting down MCP handlers:', error);
  }
}

/**
 * Gets the current MCP manager instance
 */
export function getMCPManager(): MCPManager | null {
  return mcpManager;
}

/**
 * Gets the current GitHub PAT service instance
 */
export function getGitHubPATService(): GitHubPATService | null {
  return githubPATService;
}
