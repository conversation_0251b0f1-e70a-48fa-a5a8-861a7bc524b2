import { BrowserWindow, <PERSON>u, Tray, app } from "electron";
import { getAssetPath } from "./path-resolver.js";
import path from "path";

export function createTray(mainWindow: BrowserWindow) {
    const tray = new Tray(
        path.join(
            getAssetPath(),
            process.platform === "darwin" ? "ain-tray.png" : "ain-tray.png"
        )
    );

    tray.setContextMenu(
        Menu.buildFromTemplate([
            {
                label: "Show",
                click: () => {
                    if (mainWindow) {
                        mainWindow.show();
                        if (app.dock) {
                            app.dock.show();
                        }
                    } else {
                        app.emit("activate"); // fallback to recreate
                    }
                },
            },
            {
                label: "Quit",
                click: () => app.quit(),
            },
        ])
    );
}
