// import { ProgressYield } from "./core/types.js";

// export type GlobalState = {
//     indexingSessions: Record<string, ProgressYield>;
//     activeFolder?: string;
//     queue: string[];

//     // Queue control
//     enqueue(folder: string): void;
//     dequeue(): string | undefined;
//     removeFromQueue(folder: string): boolean;
//     getNextFolder(): string | undefined;

//     // Active control
//     setActive(folder: string): void;
//     clearActive(): void;
//     hasActive(): boolean;
//     cancelActive(): void;

//     // Utilities
//     isInQueue(folder: string): boolean;
//     reset(): void;
// };

// const stateManager: GlobalState = {
//     indexingSessions: {},
//     activeFolder: undefined,
//     queue: [],

//     enqueue(folder: string) {
//         this.queue.push(folder);
//     },

//     dequeue() {
//         return this.queue.shift();
//     },

//     removeFromQueue(folder: string) {
//         const index = this.queue.indexOf(folder);
//         if (index !== -1) {
//             this.queue.splice(index, 1);
//             return true;
//         }
//         return false;
//     },

//     getNextFolder() {
//         return this.queue.length > 0 ? this.queue[0] : undefined;
//     },

//     setActive(folder: string) {
//         this.activeFolder = folder;
//     },

//     clearActive() {
//         this.activeFolder = undefined;
//     },

//     hasActive() {
//         return !!this.activeFolder;
//     },

//     cancelActive() {
//         if (this.activeFolder) {
//             // Update status to cancelled
//             this.indexingSessions[this.activeFolder] = {
//                 status: "cancelled",
//                 progress: 0,
//                 desc: "Cancelled by user",
//             };
//             this.clearActive();
//         }
//     },

//     isInQueue(folder: string) {
//         return this.queue.includes(folder);
//     },

//     reset() {
//         this.queue = [];
//         this.activeFolder = undefined;
//         this.indexingSessions = {};
//     },
// };

// export default stateManager;


import { ProgressYield } from "./core/types.js";

type ContextLimits = {
    maxTokens: number;
    maxFiles: number;
    maxFileSize: number;
};

type RetrievalParams = {
    bm25Threshold: number;
    ftsNFinal: number;
    ftsTokenBudget: number;
    embeddingsNFinal: number;
    embeddingsTokenBudget: number;
    ftsPathWeight: number;
    ftsContentWeight: number;
};

export type GlobalState = {
    indexingSessions: Record<string, ProgressYield>;
    activeFolder?: string;
    queue: string[];

    // Model context config
    modelId: string;
    contextLength: number;
    contextUtilization: number;
    totalTokenBudget: number;
    repoMapTokenBudget: number;
    retrievalParams: RetrievalParams;
    repoMapRetrievalOptions: ContextLimits;

    // Queue control
    enqueue(folder: string): void;
    dequeue(): string | undefined;
    removeFromQueue(folder: string): boolean;
    getNextFolder(): string | undefined;

    // Active control
    setActive(folder: string): void;
    clearActive(): void;
    hasActive(): boolean;
    cancelActive(): void;

    // Utilities
    isInQueue(folder: string): boolean;
    reset(): void;

    // Dynamic config
    setModelContext(modelId: string, availableTokens: number): void;
};

// === Constants ===
const DEFAULT_CONTEXT_LENGTH = 32000;
const CONTEXT_UTILIZATION = 0.7;
const DEFAULT_N_FINAL = 20;
const DEFAULT_EMBEDDINGS_N_FINAL = DEFAULT_N_FINAL * 15;
const FTS_PATH_WEIGHT = 2;
const FTS_CONTENT_WEIGHT = 8;
const DEFAULT_MAX_FILES = 9;
const DEFAULT_MAX_FILE_SIZE = 30000;

const computeBudgets = (contextLength: number) => {
    const totalTokenBudget = contextLength * CONTEXT_UTILIZATION;
    const repoMapTokenBudget = totalTokenBudget * 0.5;
    const halfBudget = repoMapTokenBudget / 2;

    return {
        totalTokenBudget,
        repoMapTokenBudget,
        ftsTokenBudget: halfBudget,
        embeddingsTokenBudget: halfBudget,
    };
};

const stateManager: GlobalState = {
    indexingSessions: {},
    activeFolder: undefined,
    queue: [],

    modelId: "default-32k-model",
    contextLength: DEFAULT_CONTEXT_LENGTH,
    contextUtilization: CONTEXT_UTILIZATION,
    totalTokenBudget: DEFAULT_CONTEXT_LENGTH * CONTEXT_UTILIZATION,
    repoMapTokenBudget: DEFAULT_CONTEXT_LENGTH * CONTEXT_UTILIZATION * 0.5,

    retrievalParams: {
        bm25Threshold: -2.5,
        ftsNFinal: DEFAULT_N_FINAL,
        ftsTokenBudget:
            (DEFAULT_CONTEXT_LENGTH * CONTEXT_UTILIZATION * 0.5) / 2,
        embeddingsNFinal: DEFAULT_EMBEDDINGS_N_FINAL,
        embeddingsTokenBudget:
            (DEFAULT_CONTEXT_LENGTH * CONTEXT_UTILIZATION * 0.5) / 2,
        ftsPathWeight: FTS_PATH_WEIGHT,
        ftsContentWeight: FTS_CONTENT_WEIGHT,
    },

    repoMapRetrievalOptions: {
        maxTokens: DEFAULT_CONTEXT_LENGTH * CONTEXT_UTILIZATION * 0.5,
        maxFiles: DEFAULT_MAX_FILES,
        maxFileSize: DEFAULT_MAX_FILE_SIZE,
    },

    enqueue(folder) {
        this.queue.push(folder);
    },

    dequeue() {
        return this.queue.shift();
    },

    removeFromQueue(folder) {
        const index = this.queue.indexOf(folder);
        if (index !== -1) {
            this.queue.splice(index, 1);
            return true;
        }
        return false;
    },

    getNextFolder() {
        return this.queue.length > 0 ? this.queue[0] : undefined;
    },

    setActive(folder) {
        this.activeFolder = folder;
    },

    clearActive() {
        this.activeFolder = undefined;
    },

    hasActive() {
        return !!this.activeFolder;
    },

    cancelActive() {
        if (this.activeFolder) {
            this.indexingSessions[this.activeFolder] = {
                status: "cancelled",
                progress: 0,
                desc: "Cancelled by user",
            };
            this.clearActive();
        }
    },

    isInQueue(folder) {
        return this.queue.includes(folder);
    },

    reset() {
        this.queue = [];
        this.activeFolder = undefined;
        this.indexingSessions = {};
    },

    setModelContext(modelId: string, availableTokens?: number) {
        this.modelId = modelId;

        console.log({ modelIddddd: this.modelId });

        switch (modelId) {
            case "amazon.nova-pro-v1:0":
                this.contextLength = 300000;
                break;
            case "us.deepseek.r1-v1:0":
                this.contextLength = 200000;
                break;
            case "gpt-4o-mini":
                this.contextLength = 32000;
                break;
            case "alpine-intellect-1.0:0":
                this.contextLength = availableTokens!;
                break;
            default:
                this.contextLength = DEFAULT_CONTEXT_LENGTH;
        }

        const {
            totalTokenBudget,
            repoMapTokenBudget,
            ftsTokenBudget,
            embeddingsTokenBudget,
        } = computeBudgets(this.contextLength);

        this.totalTokenBudget = totalTokenBudget;
        this.repoMapTokenBudget = repoMapTokenBudget;

        this.repoMapRetrievalOptions = {
            maxTokens: repoMapTokenBudget,
            maxFiles: DEFAULT_MAX_FILES,
            maxFileSize: DEFAULT_MAX_FILE_SIZE,
        };

        this.retrievalParams = {
            bm25Threshold: -2.5,
            ftsNFinal: DEFAULT_N_FINAL,
            ftsTokenBudget,
            embeddingsNFinal: DEFAULT_EMBEDDINGS_N_FINAL,
            embeddingsTokenBudget,
            ftsPathWeight: FTS_PATH_WEIGHT,
            ftsContentWeight: FTS_CONTENT_WEIGHT,
        };
    },
};

export default stateManager;
