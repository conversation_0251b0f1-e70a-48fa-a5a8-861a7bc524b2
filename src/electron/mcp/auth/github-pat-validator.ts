// GitHub PAT Validator - Validates PATs against GitHub API
import log from 'electron-log';
import {
  GitHubPATValidationResult,
  GitHubUser,
  GITHUB_REQUIRED_SCOPES,
  MCP_ERROR_CODES
} from '../core/mcp-types.js';

export class GitHubPATValidator {
  private static readonly GITHUB_API_BASE = 'https://api.github.com';
  private static readonly REQUEST_TIMEOUT = 10000; // 10 seconds
  private static readonly MAX_RETRIES = 3;

  /**
   * Validates a GitHub Personal Access Token
   */
  async validatePAT(pat: string): Promise<GitHubPATValidationResult> {
    try {
      log.debug('Validating GitHub PAT with API');

      const response = await this.makeGitHubRequest('/user', pat);
      
      if (!response.ok) {
        return this.handleAPIError(response);
      }

      const userData = await response.json() as GitHubUser;
      const scopes = this.extractScopesFromHeaders(response.headers);

      log.info(`PAT validation successful for user: ${userData.login}`);
      
      return {
        valid: true,
        username: userData.login,
        scopes
      };

    } catch (error) {
      log.error('PAT validation failed:', error);
      
      if (error instanceof TypeError && error.message.includes('fetch')) {
        return {
          valid: false,
          error: 'Network error: Unable to reach GitHub API. Please check your internet connection.',
          errorType: 'network_error'
        };
      }

      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Unknown validation error',
        errorType: 'network_error'
      };
    }
  }

  /**
   * Gets detailed user information from GitHub API
   */
  async getUserInfo(pat: string): Promise<GitHubUser | null> {
    try {
      log.debug('Fetching GitHub user information');

      const response = await this.makeGitHubRequest('/user', pat);
      
      if (!response.ok) {
        log.warn('Failed to fetch user info:', response.status, response.statusText);
        return null;
      }

      const userData = await response.json() as GitHubUser;
      log.debug(`User info fetched for: ${userData.login}`);
      
      return userData;

    } catch (error) {
      log.error('Error fetching user info:', error);
      return null;
    }
  }

  /**
   * Checks if the PAT has specific scopes
   */
  async checkScopes(pat: string, requiredScopes: string[]): Promise<{
    hasAllScopes: boolean;
    missingScopes: string[];
    availableScopes: string[];
  }> {
    try {
      const response = await this.makeGitHubRequest('/user', pat);
      
      if (!response.ok) {
        throw new Error(`GitHub API error: ${response.status}`);
      }

      const availableScopes = this.extractScopesFromHeaders(response.headers);
      const missingScopes = requiredScopes.filter(scope => !availableScopes.includes(scope));

      return {
        hasAllScopes: missingScopes.length === 0,
        missingScopes,
        availableScopes
      };

    } catch (error) {
      log.error('Error checking scopes:', error);
      throw error;
    }
  }

  /**
   * Tests if the PAT can access a specific repository
   */
  async testRepositoryAccess(pat: string, owner: string, repo: string): Promise<boolean> {
    try {
      const response = await this.makeGitHubRequest(`/repos/${owner}/${repo}`, pat);
      return response.ok;
    } catch (error) {
      log.error(`Error testing repository access for ${owner}/${repo}:`, error);
      return false;
    }
  }

  /**
   * Gets the rate limit status for the PAT
   */
  async getRateLimitStatus(pat: string): Promise<{
    limit: number;
    remaining: number;
    reset: Date;
    used: number;
  } | null> {
    try {
      const response = await this.makeGitHubRequest('/rate_limit', pat);
      
      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      const core = data.resources?.core;
      
      if (!core) {
        return null;
      }

      return {
        limit: core.limit,
        remaining: core.remaining,
        reset: new Date(core.reset * 1000),
        used: core.used
      };

    } catch (error) {
      log.error('Error getting rate limit status:', error);
      return null;
    }
  }

  /**
   * Makes a request to the GitHub API with proper error handling
   */
  private async makeGitHubRequest(
    endpoint: string, 
    pat: string, 
    options: RequestInit = {}
  ): Promise<Response> {
    const url = `${GitHubPATValidator.GITHUB_API_BASE}${endpoint}`;
    
    const requestOptions: RequestInit = {
      ...options,
      headers: {
        'Authorization': `token ${pat}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Alpine-Intellect-Desktop/1.0.0',
        ...options.headers
      },
      signal: AbortSignal.timeout(GitHubPATValidator.REQUEST_TIMEOUT)
    };

    let lastError: Error | null = null;

    // Retry logic
    for (let attempt = 1; attempt <= GitHubPATValidator.MAX_RETRIES; attempt++) {
      try {
        log.debug(`Making GitHub API request to ${endpoint} (attempt ${attempt})`);
        
        const response = await fetch(url, requestOptions);
        
        // Check for rate limiting
        if (response.status === 403) {
          const rateLimitRemaining = response.headers.get('X-RateLimit-Remaining');
          if (rateLimitRemaining === '0') {
            const resetTime = response.headers.get('X-RateLimit-Reset');
            const resetDate = resetTime ? new Date(parseInt(resetTime) * 1000) : new Date();
            log.warn(`GitHub API rate limit exceeded. Resets at: ${resetDate}`);
          }
        }

        return response;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        log.warn(`GitHub API request attempt ${attempt} failed:`, lastError.message);
        
        // Don't retry on certain errors
        if (error instanceof TypeError && error.name === 'AbortError') {
          break; // Timeout - don't retry
        }
        
        // Wait before retrying (exponential backoff)
        if (attempt < GitHubPATValidator.MAX_RETRIES) {
          const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError || new Error('All retry attempts failed');
  }

  /**
   * Extracts OAuth scopes from GitHub API response headers
   */
  private extractScopesFromHeaders(headers: Headers): string[] {
    const scopesHeader = headers.get('X-OAuth-Scopes');
    
    if (!scopesHeader) {
      log.warn('No X-OAuth-Scopes header found in GitHub API response');
      return [];
    }

    return scopesHeader
      .split(',')
      .map(scope => scope.trim())
      .filter(scope => scope.length > 0);
  }

  /**
   * Handles GitHub API error responses
   */
  private async handleAPIError(response: Response): Promise<GitHubPATValidationResult> {
    const status = response.status;
    const statusText = response.statusText;

    try {
      const errorData = await response.json();
      const message = errorData.message || statusText;

      log.warn(`GitHub API error ${status}: ${message}`);

      switch (status) {
        case 401:
          return {
            valid: false,
            error: 'Invalid or expired Personal Access Token. Please check your token and try again.',
            errorType: 'auth_failed'
          };

        case 403:
          const rateLimitRemaining = response.headers.get('X-RateLimit-Remaining');
          if (rateLimitRemaining === '0') {
            const resetTime = response.headers.get('X-RateLimit-Reset');
            const resetDate = resetTime ? new Date(parseInt(resetTime) * 1000) : new Date();
            
            return {
              valid: false,
              error: `GitHub API rate limit exceeded. Rate limit resets at ${resetDate.toLocaleString()}.`,
              errorType: 'network_error'
            };
          }
          
          return {
            valid: false,
            error: 'Access forbidden. Your PAT may not have sufficient permissions.',
            errorType: 'insufficient_scope'
          };

        case 404:
          return {
            valid: false,
            error: 'GitHub API endpoint not found. This may indicate an invalid token.',
            errorType: 'auth_failed'
          };

        case 422:
          return {
            valid: false,
            error: 'Invalid request to GitHub API. Please check your token format.',
            errorType: 'invalid_format'
          };

        default:
          return {
            valid: false,
            error: `GitHub API error: ${message} (${status})`,
            errorType: 'network_error'
          };
      }

    } catch (parseError) {
      log.error('Error parsing GitHub API error response:', parseError);
      
      return {
        valid: false,
        error: `GitHub API error: ${statusText} (${status})`,
        errorType: 'network_error'
      };
    }
  }

  /**
   * Validates that the PAT has all required scopes for GitHub MCP
   */
  async validateRequiredScopes(pat: string): Promise<{
    valid: boolean;
    missingScopes: string[];
    error?: string;
  }> {
    try {
      const scopeCheck = await this.checkScopes(pat, GITHUB_REQUIRED_SCOPES);
      
      if (!scopeCheck.hasAllScopes) {
        return {
          valid: false,
          missingScopes: scopeCheck.missingScopes,
          error: `PAT is missing required scopes: ${scopeCheck.missingScopes.join(', ')}. Please regenerate your PAT with these scopes: ${GITHUB_REQUIRED_SCOPES.join(', ')}`
        };
      }

      return {
        valid: true,
        missingScopes: []
      };

    } catch (error) {
      log.error('Error validating required scopes:', error);
      return {
        valid: false,
        missingScopes: GITHUB_REQUIRED_SCOPES,
        error: error instanceof Error ? error.message : 'Failed to validate scopes'
      };
    }
  }
}
