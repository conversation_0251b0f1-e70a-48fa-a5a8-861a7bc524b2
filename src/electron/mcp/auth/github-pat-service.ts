// GitHub Personal Access Token Service
import * as keytar from 'keytar';
import log from 'electron-log';
import {
  GitHubPATValidationResult,
  GitHubUser,
  GITHUB_REQUIRED_SCOPES,
  PAT_VALIDATION_REGEX,
  MCPError,
  MCP_ERROR_CODES
} from '../core/mcp-types.js';
import { GitHubPATValidator } from './github-pat-validator.js';

export class GitHubPATService {
  private static readonly SERVICE_NAME = 'alpine-mcp';
  private static readonly PAT_KEY = 'github-pat';
  private static readonly USER_KEY = 'github-user';
  private static readonly SCOPES_KEY = 'github-scopes';
  
  private validator: GitHubPATValidator;
  private cachedPAT?: string;
  private cachedUser?: GitHubUser;
  private cachedScopes?: string[];

  constructor() {
    this.validator = new GitHubPATValidator();
  }

  /**
   * Validates and stores a GitHub Personal Access Token
   */
  async storePAT(pat: string): Promise<GitHubPATValidationResult> {
    try {
      log.info('Validating and storing GitHub PAT');

      // First validate the PAT format
      if (!this.isValidPATFormat(pat)) {
        const error: GitHubPATValidationResult = {
          valid: false,
          error: 'Invalid PAT format. Must start with ghp_ or github_pat_ and be at least 40 characters.',
          errorType: 'invalid_format'
        };
        log.warn('PAT format validation failed');
        return error;
      }

      // Validate PAT with GitHub API
      const validationResult = await this.validator.validatePAT(pat);
      
      if (!validationResult.valid) {
        log.warn('PAT validation failed:', validationResult.error);
        return validationResult;
      }

      // Check required scopes
      const scopeValidation = this.validateRequiredScopes(validationResult.scopes || []);
      if (!scopeValidation.valid) {
        const error: GitHubPATValidationResult = {
          valid: false,
          error: scopeValidation.error,
          errorType: 'insufficient_scope'
        };
        log.warn('PAT scope validation failed:', error.error);
        return error;
      }

      // Store PAT securely using keytar
      await keytar.setPassword(GitHubPATService.SERVICE_NAME, GitHubPATService.PAT_KEY, pat);
      
      // Store user information
      if (validationResult.username) {
        const userInfo = await this.validator.getUserInfo(pat);
        if (userInfo) {
          await keytar.setPassword(
            GitHubPATService.SERVICE_NAME, 
            GitHubPATService.USER_KEY, 
            JSON.stringify(userInfo)
          );
          this.cachedUser = userInfo;
        }
      }

      // Store scopes information
      if (validationResult.scopes) {
        await keytar.setPassword(
          GitHubPATService.SERVICE_NAME,
          GitHubPATService.SCOPES_KEY,
          JSON.stringify(validationResult.scopes)
        );
        this.cachedScopes = validationResult.scopes;
      }

      // Cache the PAT
      this.cachedPAT = pat;

      log.info('GitHub PAT validated and stored successfully');
      return validationResult;

    } catch (error) {
      log.error('Error storing GitHub PAT:', error);
      
      const mcpError: GitHubPATValidationResult = {
        valid: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        errorType: 'network_error'
      };
      
      return mcpError;
    }
  }

  /**
   * Retrieves the stored GitHub PAT
   */
  async getPAT(): Promise<string | null> {
    try {
      // Return cached PAT if available
      if (this.cachedPAT) {
        return this.cachedPAT;
      }

      // Retrieve from keytar
      const pat = await keytar.getPassword(GitHubPATService.SERVICE_NAME, GitHubPATService.PAT_KEY);
      
      if (pat) {
        this.cachedPAT = pat;
        log.debug('GitHub PAT retrieved from secure storage');
      }
      
      return pat;
    } catch (error) {
      log.error('Error retrieving GitHub PAT:', error);
      return null;
    }
  }

  /**
   * Retrieves the stored GitHub user information
   */
  async getUser(): Promise<GitHubUser | null> {
    try {
      // Return cached user if available
      if (this.cachedUser) {
        return this.cachedUser;
      }

      // Retrieve from keytar
      const userJson = await keytar.getPassword(GitHubPATService.SERVICE_NAME, GitHubPATService.USER_KEY);
      
      if (userJson) {
        this.cachedUser = JSON.parse(userJson);
        log.debug('GitHub user info retrieved from secure storage');
      }
      
      return this.cachedUser || null;
    } catch (error) {
      log.error('Error retrieving GitHub user info:', error);
      return null;
    }
  }

  /**
   * Retrieves the stored GitHub scopes
   */
  async getScopes(): Promise<string[] | null> {
    try {
      // Return cached scopes if available
      if (this.cachedScopes) {
        return this.cachedScopes;
      }

      // Retrieve from keytar
      const scopesJson = await keytar.getPassword(GitHubPATService.SERVICE_NAME, GitHubPATService.SCOPES_KEY);
      
      if (scopesJson) {
        this.cachedScopes = JSON.parse(scopesJson);
        log.debug('GitHub scopes retrieved from secure storage');
      }
      
      return this.cachedScopes || null;
    } catch (error) {
      log.error('Error retrieving GitHub scopes:', error);
      return null;
    }
  }

  /**
   * Validates the current stored PAT
   */
  async validateStoredPAT(): Promise<GitHubPATValidationResult> {
    try {
      const pat = await this.getPAT();
      
      if (!pat) {
        return {
          valid: false,
          error: 'No PAT found in secure storage',
          errorType: 'auth_failed'
        };
      }

      // Validate with GitHub API
      const result = await this.validator.validatePAT(pat);
      
      if (!result.valid) {
        // Clear invalid PAT
        await this.clearCredentials();
      }
      
      return result;
    } catch (error) {
      log.error('Error validating stored PAT:', error);
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Validation failed',
        errorType: 'network_error'
      };
    }
  }

  /**
   * Checks if the PAT has the required scopes
   */
  private validateRequiredScopes(scopes: string[]): { valid: boolean; error?: string } {
    const missingScopes = GITHUB_REQUIRED_SCOPES.filter(scope => !scopes.includes(scope));
    
    if (missingScopes.length > 0) {
      return {
        valid: false,
        error: `PAT is missing required scopes: ${missingScopes.join(', ')}. Please regenerate your PAT with the required scopes.`
      };
    }
    
    return { valid: true };
  }

  /**
   * Validates PAT format using regex
   */
  private isValidPATFormat(pat: string): boolean {
    return PAT_VALIDATION_REGEX.test(pat) && pat.length >= 40;
  }

  /**
   * Clears all stored GitHub credentials
   */
  async clearCredentials(): Promise<void> {
    try {
      log.info('Clearing GitHub credentials');
      
      // Clear from keytar
      await Promise.all([
        keytar.deletePassword(GitHubPATService.SERVICE_NAME, GitHubPATService.PAT_KEY),
        keytar.deletePassword(GitHubPATService.SERVICE_NAME, GitHubPATService.USER_KEY),
        keytar.deletePassword(GitHubPATService.SERVICE_NAME, GitHubPATService.SCOPES_KEY)
      ]);

      // Clear cache
      this.cachedPAT = undefined;
      this.cachedUser = undefined;
      this.cachedScopes = undefined;

      log.info('GitHub credentials cleared successfully');
    } catch (error) {
      log.error('Error clearing GitHub credentials:', error);
      throw error;
    }
  }

  /**
   * Checks if credentials are stored
   */
  async hasStoredCredentials(): Promise<boolean> {
    try {
      const pat = await this.getPAT();
      return !!pat;
    } catch (error) {
      log.error('Error checking stored credentials:', error);
      return false;
    }
  }

  /**
   * Gets the current authentication status
   */
  async getAuthStatus(): Promise<{
    authenticated: boolean;
    username?: string;
    scopes?: string[];
    error?: string;
  }> {
    try {
      const hasCredentials = await this.hasStoredCredentials();
      
      if (!hasCredentials) {
        return { authenticated: false };
      }

      const validation = await this.validateStoredPAT();
      
      if (!validation.valid) {
        return {
          authenticated: false,
          error: validation.error
        };
      }

      const user = await this.getUser();
      const scopes = await this.getScopes();

      return {
        authenticated: true,
        username: user?.login || validation.username,
        scopes: scopes || validation.scopes
      };
    } catch (error) {
      log.error('Error getting auth status:', error);
      return {
        authenticated: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Refreshes user information from GitHub API
   */
  async refreshUserInfo(): Promise<GitHubUser | null> {
    try {
      const pat = await this.getPAT();
      if (!pat) {
        return null;
      }

      const userInfo = await this.validator.getUserInfo(pat);
      if (userInfo) {
        await keytar.setPassword(
          GitHubPATService.SERVICE_NAME,
          GitHubPATService.USER_KEY,
          JSON.stringify(userInfo)
        );
        this.cachedUser = userInfo;
      }

      return userInfo;
    } catch (error) {
      log.error('Error refreshing user info:', error);
      return null;
    }
  }
}
