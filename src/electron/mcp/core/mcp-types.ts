// MCP Core Types and Interfaces
export interface MCPServerConfig {
  id: string;
  name: string;
  command: string;
  args: string[];
  transport: {
    type: 'stdio' | 'sse';
  };
  env?: Record<string, string>;
  capabilities: {
    tools: boolean;
    resources: boolean;
    prompts: boolean;
  };
  metadata: {
    name: string;
    version: string;
    description?: string;
  };
}

export interface MCPServerStatus {
  id: string;
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  lastConnected?: Date;
  error?: string;
  processId?: number;
  tools?: MCPTool[];
}

export interface MCPTool {
  name: string;
  description: string;
  inputSchema: any;
}

export interface MCPMessage {
  jsonrpc: '2.0';
  id?: string | number;
  method: string;
  params?: any;
}

export interface MCPResponse {
  jsonrpc: '2.0';
  id?: string | number;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

export interface MCPTransport {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  send(message: MCPMessage): Promise<MCPResponse>;
  isConnected(): boolean;
  onMessage(callback: (message: MCPMessage) => void): void;
  onError(callback: (error: Error) => void): void;
  onDisconnect(callback: () => void): void;
}

export interface GitHubPATValidationResult {
  valid: boolean;
  username?: string;
  scopes?: string[];
  error?: string;
  errorType?: 'invalid_format' | 'auth_failed' | 'insufficient_scope' | 'network_error';
}

export interface GitHubMCPServerConfig extends MCPServerConfig {
  id: 'github';
  name: 'GitHub Integration';
  command: 'docker';
  args: [
    'run', '-i', '--rm',
    '-e', 'GITHUB_PERSONAL_ACCESS_TOKEN=${PAT}',
    'ghcr.io/github/github-mcp-server:latest'
  ];
  transport: {
    type: 'stdio';
  };
  capabilities: {
    tools: true;
    resources: false;
    prompts: false;
  };
}

export interface MCPError extends Error {
  code: string;
  type: 'pat_error' | 'server_error' | 'api_error' | 'network_error';
  details?: any;
}

export interface MCPAuditEvent {
  timestamp: Date;
  event: string;
  serverId: string;
  details?: any;
  userId?: string;
}

export interface MCPTelemetryEvent {
  event: string;
  serverId: string;
  timestamp: Date;
  properties?: Record<string, any>;
}

// GitHub specific types
export interface GitHubUser {
  login: string;
  id: number;
  name: string;
  email: string;
  avatar_url: string;
}

export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  private: boolean;
  html_url: string;
  description: string;
}

export interface GitHubIssue {
  id: number;
  number: number;
  title: string;
  body: string;
  state: 'open' | 'closed';
  html_url: string;
}

export interface GitHubPullRequest {
  id: number;
  number: number;
  title: string;
  body: string;
  state: 'open' | 'closed' | 'merged';
  html_url: string;
}

// Tool execution types
export interface ToolExecutionRequest {
  serverId: string;
  toolName: string;
  parameters: Record<string, any>;
}

export interface ToolExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  executionTime: number;
}

// Configuration types
export interface MCPConfiguration {
  servers: Record<string, MCPServerConfig>;
  security: {
    patMaxAge: number;
    requiredScopes: string[];
    encryptionEnabled: boolean;
  };
  monitoring: {
    telemetryEnabled: boolean;
    auditLoggingEnabled: boolean;
    healthCheckInterval: number;
  };
  docker: {
    pullPolicy: 'always' | 'if-not-present' | 'never';
    timeout: number;
    restartPolicy: 'always' | 'on-failure' | 'never';
  };
}

// Default configurations
export const DEFAULT_GITHUB_MCP_CONFIG: GitHubMCPServerConfig = {
  id: 'github',
  name: 'GitHub Integration',
  command: 'docker',
  args: [
    'run', '-i', '--rm',
    '-e', 'GITHUB_PERSONAL_ACCESS_TOKEN=${PAT}',
    'ghcr.io/github/github-mcp-server:latest'
  ],
  transport: {
    type: 'stdio'
  },
  capabilities: {
    tools: true,
    resources: false,
    prompts: false
  },
  metadata: {
    name: 'GitHub Integration',
    version: '1.0.0',
    description: 'GitHub MCP Server for repository management'
  }
};

export const GITHUB_REQUIRED_SCOPES = ['repo', 'user', 'read:org'];

export const PAT_VALIDATION_REGEX = /^(ghp_|github_pat_)[a-zA-Z0-9]{35,}$/;

export const MCP_ERROR_CODES = {
  INVALID_PAT_FORMAT: 'INVALID_PAT_FORMAT',
  AUTH_FAILED: 'AUTH_FAILED',
  INSUFFICIENT_SCOPE: 'INSUFFICIENT_SCOPE',
  NETWORK_ERROR: 'NETWORK_ERROR',
  DOCKER_LAUNCH_FAILURE: 'DOCKER_LAUNCH_FAILURE',
  COMMUNICATION_ERROR: 'COMMUNICATION_ERROR',
  SERVER_CRASH: 'SERVER_CRASH',
  RATE_LIMIT: 'RATE_LIMIT'
} as const;

export type MCPErrorCode = typeof MCP_ERROR_CODES[keyof typeof MCP_ERROR_CODES];
