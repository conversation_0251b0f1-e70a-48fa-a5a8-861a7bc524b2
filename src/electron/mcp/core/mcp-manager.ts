// MCP Manager - Central coordinator for all MCP operations
import { EventEmitter } from 'events';
import log from 'electron-log';
import {
  MCPServerConfig,
  MCPServerStatus,
  MCPTool,
  ToolExecutionRequest,
  ToolExecutionResult,
  MCPError,
  MCPAuditEvent,
  MCPTelemetryEvent,
  DEFAULT_GITHUB_MCP_CONFIG
} from './mcp-types.js';
import { GitHubMCPServerManager } from '../servers/github-mcp-server-manager.js';
import { MCPAuditLogger } from '../monitoring/mcp-audit-logger.js';
import { MCPTelemetryCollector } from '../monitoring/mcp-telemetry-collector.js';

export class MCPManager extends EventEmitter {
  private servers: Map<string, GitHubMCPServerManager> = new Map();
  private serverStatuses: Map<string, MCPServerStatus> = new Map();
  private auditLogger: MCPAuditLogger;
  private telemetryCollector: MCPTelemetryCollector;
  private healthCheckInterval?: NodeJS.Timeout;

  constructor() {
    super();
    this.auditLogger = new MCPAuditLogger();
    this.telemetryCollector = new MCPTelemetryCollector();
    this.initializeDefaultServers();
    this.startHealthChecking();
  }

  private initializeDefaultServers(): void {
    // Initialize GitHub MCP server
    const githubManager = new GitHubMCPServerManager(DEFAULT_GITHUB_MCP_CONFIG);
    this.servers.set('github', githubManager);
    
    // Set initial status
    this.serverStatuses.set('github', {
      id: 'github',
      status: 'disconnected'
    });

    // Set up event listeners
    githubManager.on('status-changed', (status: MCPServerStatus) => {
      this.handleServerStatusChange(status);
    });

    githubManager.on('error', (error: MCPError) => {
      this.handleServerError('github', error);
    });

    githubManager.on('tools-updated', (tools: MCPTool[]) => {
      this.handleToolsUpdated('github', tools);
    });

    log.info('MCP Manager initialized with GitHub server');
  }

  private startHealthChecking(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, 30000); // Check every 30 seconds
  }

  private async performHealthCheck(): Promise<void> {
    for (const [serverId, manager] of this.servers) {
      try {
        const isHealthy = await manager.healthCheck();
        const currentStatus = this.serverStatuses.get(serverId);
        
        if (!isHealthy && currentStatus?.status === 'connected') {
          log.warn(`Health check failed for server: ${serverId}`);
          await this.handleServerError(serverId, new Error('Health check failed') as MCPError);
        }
      } catch (error) {
        log.error(`Health check error for server ${serverId}:`, error);
      }
    }
  }

  private handleServerStatusChange(status: MCPServerStatus): void {
    const previousStatus = this.serverStatuses.get(status.id);
    this.serverStatuses.set(status.id, status);

    // Log audit event
    this.auditLogger.logEvent({
      timestamp: new Date(),
      event: 'server_status_changed',
      serverId: status.id,
      details: {
        previousStatus: previousStatus?.status,
        newStatus: status.status,
        error: status.error
      }
    });

    // Collect telemetry
    this.telemetryCollector.collectEvent({
      event: 'server_status_changed',
      serverId: status.id,
      timestamp: new Date(),
      properties: {
        status: status.status,
        hasError: !!status.error
      }
    });

    // Emit event for UI updates
    this.emit('server-status-changed', status);

    log.info(`Server ${status.id} status changed to: ${status.status}`);
  }

  private handleServerError(serverId: string, error: MCPError): void {
    log.error(`Server ${serverId} error:`, error);

    // Update server status
    const currentStatus = this.serverStatuses.get(serverId);
    if (currentStatus) {
      currentStatus.status = 'error';
      currentStatus.error = error.message;
      this.serverStatuses.set(serverId, currentStatus);
    }

    // Log audit event
    this.auditLogger.logEvent({
      timestamp: new Date(),
      event: 'server_error',
      serverId,
      details: {
        error: error.message,
        code: error.code,
        type: error.type
      }
    });

    // Collect telemetry
    this.telemetryCollector.collectEvent({
      event: 'server_error',
      serverId,
      timestamp: new Date(),
      properties: {
        errorCode: error.code,
        errorType: error.type
      }
    });

    // Emit error event
    this.emit('server-error', { serverId, error });
  }

  private handleToolsUpdated(serverId: string, tools: MCPTool[]): void {
    const currentStatus = this.serverStatuses.get(serverId);
    if (currentStatus) {
      currentStatus.tools = tools;
      this.serverStatuses.set(serverId, currentStatus);
    }

    // Log audit event
    this.auditLogger.logEvent({
      timestamp: new Date(),
      event: 'tools_updated',
      serverId,
      details: {
        toolCount: tools.length,
        toolNames: tools.map(t => t.name)
      }
    });

    // Emit event for UI updates
    this.emit('tools-updated', { serverId, tools });

    log.info(`Tools updated for server ${serverId}: ${tools.length} tools available`);
  }

  // Public API methods

  async connectServer(serverId: string, credentials?: Record<string, string>): Promise<void> {
    const manager = this.servers.get(serverId);
    if (!manager) {
      throw new Error(`Server ${serverId} not found`);
    }

    try {
      log.info(`Connecting to server: ${serverId}`);
      
      // Update status to connecting
      const currentStatus = this.serverStatuses.get(serverId);
      if (currentStatus) {
        currentStatus.status = 'connecting';
        this.handleServerStatusChange(currentStatus);
      }

      await manager.connect(credentials);
      
      log.info(`Successfully connected to server: ${serverId}`);
    } catch (error) {
      log.error(`Failed to connect to server ${serverId}:`, error);
      throw error;
    }
  }

  async disconnectServer(serverId: string): Promise<void> {
    const manager = this.servers.get(serverId);
    if (!manager) {
      throw new Error(`Server ${serverId} not found`);
    }

    try {
      log.info(`Disconnecting from server: ${serverId}`);
      await manager.disconnect();
      log.info(`Successfully disconnected from server: ${serverId}`);
    } catch (error) {
      log.error(`Failed to disconnect from server ${serverId}:`, error);
      throw error;
    }
  }

  async executeTool(request: ToolExecutionRequest): Promise<ToolExecutionResult> {
    const manager = this.servers.get(request.serverId);
    if (!manager) {
      throw new Error(`Server ${request.serverId} not found`);
    }

    const startTime = Date.now();
    
    try {
      log.info(`Executing tool ${request.toolName} on server ${request.serverId}`);
      
      const result = await manager.executeTool(request.toolName, request.parameters);
      const executionTime = Date.now() - startTime;

      // Log audit event
      this.auditLogger.logEvent({
        timestamp: new Date(),
        event: 'tool_executed',
        serverId: request.serverId,
        details: {
          toolName: request.toolName,
          parameters: request.parameters,
          success: true,
          executionTime
        }
      });

      // Collect telemetry
      this.telemetryCollector.collectEvent({
        event: 'tool_executed',
        serverId: request.serverId,
        timestamp: new Date(),
        properties: {
          toolName: request.toolName,
          success: true,
          executionTime
        }
      });

      log.info(`Tool ${request.toolName} executed successfully in ${executionTime}ms`);
      
      return {
        success: true,
        result,
        executionTime
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      log.error(`Tool ${request.toolName} execution failed:`, error);

      // Log audit event
      this.auditLogger.logEvent({
        timestamp: new Date(),
        event: 'tool_execution_failed',
        serverId: request.serverId,
        details: {
          toolName: request.toolName,
          parameters: request.parameters,
          error: error instanceof Error ? error.message : String(error),
          executionTime
        }
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        executionTime
      };
    }
  }

  getServerStatus(serverId: string): MCPServerStatus | undefined {
    return this.serverStatuses.get(serverId);
  }

  getAllServerStatuses(): MCPServerStatus[] {
    return Array.from(this.serverStatuses.values());
  }

  getAvailableTools(serverId: string): MCPTool[] {
    const status = this.serverStatuses.get(serverId);
    return status?.tools || [];
  }

  getAllAvailableTools(): Record<string, MCPTool[]> {
    const result: Record<string, MCPTool[]> = {};
    for (const [serverId, status] of this.serverStatuses) {
      result[serverId] = status.tools || [];
    }
    return result;
  }

  async restartServer(serverId: string): Promise<void> {
    log.info(`Restarting server: ${serverId}`);
    
    try {
      await this.disconnectServer(serverId);
      // Wait a moment before reconnecting
      await new Promise(resolve => setTimeout(resolve, 1000));
      await this.connectServer(serverId);
    } catch (error) {
      log.error(`Failed to restart server ${serverId}:`, error);
      throw error;
    }
  }

  // Additional methods for the new MCP management interface

  getConnectedServers(): string[] {
    return Array.from(this.servers.keys()).filter(serverId => {
      const manager = this.servers.get(serverId);
      return manager && manager.isConnected();
    });
  }

  async installServer(serverId: string, config?: any): Promise<void> {
    log.info(`Installing server: ${serverId}`);

    // For now, this is a placeholder - in a real implementation,
    // this would handle downloading and installing MCP servers
    // For the current implementation, we'll just add it to available servers

    if (this.servers.has(serverId)) {
      throw new Error(`Server ${serverId} is already installed`);
    }

    // This would typically involve:
    // 1. Downloading the server package
    // 2. Installing dependencies
    // 3. Setting up configuration
    // 4. Adding to available servers list

    log.info(`Server ${serverId} installation completed`);
  }

  async configureServer(serverId: string, config: any): Promise<void> {
    log.info(`Configuring server: ${serverId}`);

    // This would typically involve:
    // 1. Validating configuration
    // 2. Storing configuration securely
    // 3. Updating server settings

    // For now, we'll just log the configuration
    log.info(`Server ${serverId} configured with:`, config);
  }

  async startServer(serverId: string): Promise<void> {
    log.info(`Starting server: ${serverId}`);

    // For local servers, this would start the process
    // For remote servers, this might just update status

    const manager = this.servers.get(serverId);
    if (!manager) {
      throw new Error(`Server ${serverId} not found`);
    }

    // If already connected, no need to start
    if (manager.isConnected()) {
      log.info(`Server ${serverId} is already running`);
      return;
    }

    // Start the server (this would be implementation-specific)
    await this.connectServer(serverId);
  }

  async stopServer(serverId: string): Promise<void> {
    log.info(`Stopping server: ${serverId}`);

    const manager = this.servers.get(serverId);
    if (!manager) {
      throw new Error(`Server ${serverId} not found`);
    }

    if (!manager.isConnected()) {
      log.info(`Server ${serverId} is already stopped`);
      return;
    }

    await this.disconnectServer(serverId);
  }

  async removeServer(serverId: string): Promise<void> {
    log.info(`Removing server: ${serverId}`);

    // First disconnect if connected
    if (this.servers.has(serverId)) {
      await this.disconnectServer(serverId);
    }

    // Remove from available servers
    this.servers.delete(serverId);

    // This would typically also:
    // 1. Remove configuration files
    // 2. Clean up installed packages
    // 3. Remove from registry

    log.info(`Server ${serverId} removed successfully`);
  }

  async shutdown(): Promise<void> {
    log.info('Shutting down MCP Manager');
    
    // Clear health check interval
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // Disconnect all servers
    const disconnectPromises = Array.from(this.servers.keys()).map(serverId =>
      this.disconnectServer(serverId).catch(error => 
        log.error(`Error disconnecting server ${serverId}:`, error)
      )
    );

    await Promise.all(disconnectPromises);

    // Shutdown monitoring services
    await this.auditLogger.shutdown();
    await this.telemetryCollector.shutdown();

    log.info('MCP Manager shutdown complete');
  }
}
