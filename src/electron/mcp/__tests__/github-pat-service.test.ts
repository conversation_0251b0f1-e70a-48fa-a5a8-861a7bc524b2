// GitHub PAT Service Tests
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { GitHubPATService } from '../auth/github-pat-service';
import { GitHubPATValidator } from '../auth/github-pat-validator';

// Mock keytar
jest.mock('keytar', () => ({
  setPassword: jest.fn(),
  getPassword: jest.fn(),
  deletePassword: jest.fn()
}));

// Mock the validator
jest.mock('../auth/github-pat-validator');

describe('GitHubPATService', () => {
  let patService: GitHubPATService;
  let mockValidator: jest.Mocked<GitHubPATValidator>;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    
    // Create service instance
    patService = new GitHubPATService();
    
    // Get mocked validator
    mockValidator = jest.mocked(new GitHubPATValidator());
    (patService as any).validator = mockValidator;
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('storePAT', () => {
    test('should validate and store a valid PAT', async () => {
      const validPAT = '********************************************';
      const mockValidationResult = {
        valid: true,
        username: 'testuser',
        scopes: ['repo', 'user', 'read:org']
      };

      mockValidator.validatePAT.mockResolvedValue(mockValidationResult);
      mockValidator.getUserInfo.mockResolvedValue({
        login: 'testuser',
        id: 12345,
        name: 'Test User',
        email: '<EMAIL>',
        avatar_url: 'https://github.com/avatar.jpg'
      });

      const keytar = require('keytar');
      keytar.setPassword.mockResolvedValue(undefined);

      const result = await patService.storePAT(validPAT);

      expect(result.valid).toBe(true);
      expect(result.username).toBe('testuser');
      expect(keytar.setPassword).toHaveBeenCalledWith('alpine-mcp', 'github-pat', validPAT);
    });

    test('should reject invalid PAT format', async () => {
      const invalidPAT = 'invalid_token';

      const result = await patService.storePAT(invalidPAT);

      expect(result.valid).toBe(false);
      expect(result.errorType).toBe('invalid_format');
      expect(result.error).toContain('Invalid PAT format');
    });

    test('should reject PAT with insufficient scopes', async () => {
      const validPAT = '********************************************';
      const mockValidationResult = {
        valid: true,
        username: 'testuser',
        scopes: ['repo'] // Missing 'user' and 'read:org'
      };

      mockValidator.validatePAT.mockResolvedValue(mockValidationResult);

      const result = await patService.storePAT(validPAT);

      expect(result.valid).toBe(false);
      expect(result.errorType).toBe('insufficient_scope');
      expect(result.error).toContain('missing required scopes');
    });

    test('should handle API validation failure', async () => {
      const validPAT = '********************************************';
      const mockValidationResult = {
        valid: false,
        error: 'Invalid token',
        errorType: 'auth_failed' as const
      };

      mockValidator.validatePAT.mockResolvedValue(mockValidationResult);

      const result = await patService.storePAT(validPAT);

      expect(result.valid).toBe(false);
      expect(result.errorType).toBe('auth_failed');
    });
  });

  describe('getPAT', () => {
    test('should retrieve stored PAT', async () => {
      const storedPAT = '********************************************';
      const keytar = require('keytar');
      keytar.getPassword.mockResolvedValue(storedPAT);

      const result = await patService.getPAT();

      expect(result).toBe(storedPAT);
      expect(keytar.getPassword).toHaveBeenCalledWith('alpine-mcp', 'github-pat');
    });

    test('should return null when no PAT is stored', async () => {
      const keytar = require('keytar');
      keytar.getPassword.mockResolvedValue(null);

      const result = await patService.getPAT();

      expect(result).toBeNull();
    });

    test('should handle keytar errors gracefully', async () => {
      const keytar = require('keytar');
      keytar.getPassword.mockRejectedValue(new Error('Keytar error'));

      const result = await patService.getPAT();

      expect(result).toBeNull();
    });
  });

  describe('validateStoredPAT', () => {
    test('should validate stored PAT successfully', async () => {
      const storedPAT = '********************************************';
      const keytar = require('keytar');
      keytar.getPassword.mockResolvedValue(storedPAT);

      const mockValidationResult = {
        valid: true,
        username: 'testuser',
        scopes: ['repo', 'user', 'read:org']
      };
      mockValidator.validatePAT.mockResolvedValue(mockValidationResult);

      const result = await patService.validateStoredPAT();

      expect(result.valid).toBe(true);
      expect(mockValidator.validatePAT).toHaveBeenCalledWith(storedPAT);
    });

    test('should return invalid when no PAT is stored', async () => {
      const keytar = require('keytar');
      keytar.getPassword.mockResolvedValue(null);

      const result = await patService.validateStoredPAT();

      expect(result.valid).toBe(false);
      expect(result.errorType).toBe('auth_failed');
    });

    test('should clear invalid PAT', async () => {
      const storedPAT = '********************************************';
      const keytar = require('keytar');
      keytar.getPassword.mockResolvedValue(storedPAT);
      keytar.deletePassword.mockResolvedValue(true);

      const mockValidationResult = {
        valid: false,
        error: 'Token expired',
        errorType: 'auth_failed' as const
      };
      mockValidator.validatePAT.mockResolvedValue(mockValidationResult);

      const result = await patService.validateStoredPAT();

      expect(result.valid).toBe(false);
      expect(keytar.deletePassword).toHaveBeenCalledWith('alpine-mcp', 'github-pat');
    });
  });

  describe('clearCredentials', () => {
    test('should clear all stored credentials', async () => {
      const keytar = require('keytar');
      keytar.deletePassword.mockResolvedValue(true);

      await patService.clearCredentials();

      expect(keytar.deletePassword).toHaveBeenCalledWith('alpine-mcp', 'github-pat');
      expect(keytar.deletePassword).toHaveBeenCalledWith('alpine-mcp', 'github-user');
      expect(keytar.deletePassword).toHaveBeenCalledWith('alpine-mcp', 'github-scopes');
    });

    test('should handle deletion errors gracefully', async () => {
      const keytar = require('keytar');
      keytar.deletePassword.mockRejectedValue(new Error('Deletion failed'));

      await expect(patService.clearCredentials()).rejects.toThrow('Deletion failed');
    });
  });

  describe('getAuthStatus', () => {
    test('should return authenticated status with user info', async () => {
      const storedPAT = '********************************************';
      const keytar = require('keytar');
      keytar.getPassword
        .mockResolvedValueOnce(storedPAT) // getPAT call
        .mockResolvedValueOnce(storedPAT) // validateStoredPAT call
        .mockResolvedValueOnce(JSON.stringify({ login: 'testuser' })) // getUser call
        .mockResolvedValueOnce(JSON.stringify(['repo', 'user', 'read:org'])); // getScopes call

      const mockValidationResult = {
        valid: true,
        username: 'testuser',
        scopes: ['repo', 'user', 'read:org']
      };
      mockValidator.validatePAT.mockResolvedValue(mockValidationResult);

      const result = await patService.getAuthStatus();

      expect(result.authenticated).toBe(true);
      expect(result.username).toBe('testuser');
      expect(result.scopes).toEqual(['repo', 'user', 'read:org']);
    });

    test('should return unauthenticated when no credentials', async () => {
      const keytar = require('keytar');
      keytar.getPassword.mockResolvedValue(null);

      const result = await patService.getAuthStatus();

      expect(result.authenticated).toBe(false);
    });

    test('should return unauthenticated when validation fails', async () => {
      const storedPAT = '********************************************';
      const keytar = require('keytar');
      keytar.getPassword.mockResolvedValue(storedPAT);

      const mockValidationResult = {
        valid: false,
        error: 'Token expired',
        errorType: 'auth_failed' as const
      };
      mockValidator.validatePAT.mockResolvedValue(mockValidationResult);

      const result = await patService.getAuthStatus();

      expect(result.authenticated).toBe(false);
      expect(result.error).toBe('Token expired');
    });
  });
});
