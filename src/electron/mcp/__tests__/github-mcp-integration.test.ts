// GitHub MCP Integration Tests
// Note: This file requires Je<PERSON> to be properly configured
// import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { MCPManager } from '../core/mcp-manager.js';
import { GitHubPATService } from '../auth/github-pat-service.js';

// Mock child_process for Docker spawning
jest.mock('child_process', () => ({
  spawn: jest.fn()
}));

// Mock keytar
jest.mock('keytar', () => ({
  setPassword: jest.fn(),
  getPassword: jest.fn(),
  deletePassword: jest.fn()
}));

// Mock fetch for GitHub API calls
global.fetch = jest.fn();

describe('GitHub MCP Integration', () => {
  let mcpManager: MCPManager;
  let patService: GitHubPATService;

  beforeEach(() => {
    jest.clearAllMocks();
    mcpManager = new MCPManager();
    patService = new GitHubPATService();
  });

  afterEach(async () => {
    await mcpManager.shutdown();
    jest.restoreAllMocks();
  });

  describe('End-to-End Connection Flow', () => {
    test('should complete full connection workflow', async () => {
      // Mock valid PAT
      const validPAT = '********************************************';
      
      // Mock GitHub API responses
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({
          'X-OAuth-Scopes': 'repo, user, read:org'
        }),
        json: async () => ({
          login: 'testuser',
          id: 12345,
          name: 'Test User',
          email: '<EMAIL>',
          avatar_url: 'https://github.com/avatar.jpg'
        })
      } as Response);

      // Mock keytar operations
      const keytar = require('keytar');
      keytar.setPassword.mockResolvedValue(undefined);
      keytar.getPassword.mockResolvedValue(validPAT);

      // Mock Docker process
      const { spawn } = require('child_process');
      const mockProcess = {
        pid: 12345,
        stdin: {
          write: jest.fn()
        },
        stdout: {
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              // Simulate MCP server ready message
              setTimeout(() => callback(Buffer.from('Ready\n')), 100);
            }
          })
        },
        stderr: {
          on: jest.fn()
        },
        on: jest.fn(),
        kill: jest.fn(),
        killed: false
      };
      spawn.mockReturnValue(mockProcess);

      // Test the complete flow
      try {
        // 1. Store and validate PAT
        const validation = await patService.storePAT(validPAT);
        expect(validation.valid).toBe(true);

        // 2. Connect to GitHub MCP server
        await mcpManager.connectServer('github', { pat: validPAT });

        // 3. Verify server status
        const status = mcpManager.getServerStatus('github');
        expect(status?.status).toBe('connected');

        // 4. Verify Docker was spawned with correct arguments
        expect(spawn).toHaveBeenCalledWith('docker', [
          'run', '-i', '--rm',
          '-e', `GITHUB_PERSONAL_ACCESS_TOKEN=${validPAT}`,
          'ghcr.io/github/github-mcp-server:latest'
        ], expect.any(Object));

      } catch (error) {
        console.error('Integration test failed:', error);
        throw error;
      }
    }, 10000); // 10 second timeout

    test('should handle PAT validation failure', async () => {
      const invalidPAT = 'invalid_token';

      // Mock GitHub API failure
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        json: async () => ({
          message: 'Bad credentials'
        })
      } as Response);

      // Test PAT validation failure
      const validation = await patService.storePAT(invalidPAT);
      expect(validation.valid).toBe(false);
      expect(validation.errorType).toBe('invalid_format');

      // Ensure connection fails
      await expect(
        mcpManager.connectServer('github', { pat: invalidPAT })
      ).rejects.toThrow();
    });

    test('should handle Docker launch failure', async () => {
      const validPAT = '********************************************';

      // Mock successful PAT validation
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({
          'X-OAuth-Scopes': 'repo, user, read:org'
        }),
        json: async () => ({
          login: 'testuser',
          id: 12345
        })
      } as Response);

      const keytar = require('keytar');
      keytar.setPassword.mockResolvedValue(undefined);

      // Mock Docker spawn failure
      const { spawn } = require('child_process');
      const mockProcess = {
        on: jest.fn((event, callback) => {
          if (event === 'error') {
            setTimeout(() => callback(new Error('Docker not found')), 100);
          }
        }),
        kill: jest.fn(),
        killed: false
      };
      spawn.mockReturnValue(mockProcess);

      // Test Docker failure handling
      await expect(
        mcpManager.connectServer('github', { pat: validPAT })
      ).rejects.toThrow();
    });
  });

  describe('Tool Execution', () => {
    test('should execute GitHub tools successfully', async () => {
      // This test would require a more complex setup with a mock MCP server
      // For now, we'll test the tool execution interface
      
      const toolRequest = {
        serverId: 'github',
        toolName: 'create_repository',
        parameters: {
          name: 'test-repo',
          description: 'Test repository',
          private: false
        }
      };

      // Mock server as connected with tools
      const mockStatus = {
        id: 'github',
        status: 'connected' as const,
        tools: [
          {
            name: 'create_repository',
            description: 'Create a new repository'
          }
        ]
      };

      // Set up mock server state
      (mcpManager as any).serverStatuses.set('github', mockStatus);

      // Mock the server manager
      const mockServerManager = {
        executeTool: jest.fn().mockResolvedValue({
          id: 123,
          name: 'test-repo',
          full_name: 'testuser/test-repo'
        })
      };
      (mcpManager as any).servers.set('github', mockServerManager);

      const result = await mcpManager.executeTool(toolRequest);

      expect(result.success).toBe(true);
      expect(result.result).toEqual({
        id: 123,
        name: 'test-repo',
        full_name: 'testuser/test-repo'
      });
      expect(mockServerManager.executeTool).toHaveBeenCalledWith(
        'create_repository',
        toolRequest.parameters
      );
    });

    test('should handle tool execution errors', async () => {
      const toolRequest = {
        serverId: 'github',
        toolName: 'create_repository',
        parameters: {
          name: 'invalid-repo-name-!@#'
        }
      };

      // Mock server as connected
      const mockStatus = {
        id: 'github',
        status: 'connected' as const,
        tools: [
          {
            name: 'create_repository',
            description: 'Create a new repository'
          }
        ]
      };
      (mcpManager as any).serverStatuses.set('github', mockStatus);

      // Mock tool execution failure
      const mockServerManager = {
        executeTool: jest.fn().mockRejectedValue(new Error('Invalid repository name'))
      };
      (mcpManager as any).servers.set('github', mockServerManager);

      const result = await mcpManager.executeTool(toolRequest);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid repository name');
    });
  });

  describe('Error Recovery', () => {
    test('should attempt reconnection on server crash', async () => {
      // Mock initial successful connection
      const validPAT = '********************************************';
      
      const keytar = require('keytar');
      keytar.getPassword.mockResolvedValue(validPAT);

      // Mock GitHub API
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Headers({
          'X-OAuth-Scopes': 'repo, user, read:org'
        }),
        json: async () => ({ login: 'testuser', id: 12345 })
      } as Response);

      // Mock Docker process that crashes
      const { spawn } = require('child_process');
      let processCallbacks: any = {};
      const mockProcess = {
        pid: 12345,
        stdin: { write: jest.fn() },
        stdout: {
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              setTimeout(() => callback(Buffer.from('Ready\n')), 100);
            }
          })
        },
        stderr: { on: jest.fn() },
        on: jest.fn((event, callback) => {
          processCallbacks[event] = callback;
        }),
        kill: jest.fn(),
        killed: false
      };
      spawn.mockReturnValue(mockProcess);

      // Connect initially
      await mcpManager.connectServer('github');
      
      // Verify connected
      let status = mcpManager.getServerStatus('github');
      expect(status?.status).toBe('connected');

      // Simulate process crash
      if (processCallbacks.exit) {
        processCallbacks.exit(1, null);
      }

      // Wait for error handling
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify error status
      status = mcpManager.getServerStatus('github');
      expect(status?.status).toBe('error');
    });
  });

  describe('Cleanup and Shutdown', () => {
    test('should clean up resources on shutdown', async () => {
      const { spawn } = require('child_process');
      const mockProcess = {
        pid: 12345,
        kill: jest.fn(),
        killed: false,
        stdin: { write: jest.fn() },
        stdout: { on: jest.fn() },
        stderr: { on: jest.fn() },
        on: jest.fn()
      };
      spawn.mockReturnValue(mockProcess);

      // Mock connection
      (mcpManager as any).serverStatuses.set('github', {
        id: 'github',
        status: 'connected'
      });

      const mockServerManager = {
        disconnect: jest.fn().mockResolvedValue(undefined)
      };
      (mcpManager as any).servers.set('github', mockServerManager);

      // Test shutdown
      await mcpManager.shutdown();

      expect(mockServerManager.disconnect).toHaveBeenCalled();
    });
  });
});
