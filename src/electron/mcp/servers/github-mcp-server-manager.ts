// GitHub MCP Server Manager - Manages Docker-based GitHub MCP server lifecycle
import { EventEmitter } from 'events';
import { spawn, ChildProcess } from 'child_process';
import log from 'electron-log';
import {
  MCPServerConfig,
  MCPServerStatus,
  MCPTool,
  MCPMessage,
  MCPResponse,
  MCPError,
  MCP_ERROR_CODES
} from '../core/mcp-types.js';
import { GitHubPATService } from '../auth/github-pat-service.js';
import { MCPStdioTransport } from '../transport/mcp-stdio-transport.js';

export class GitHubMCPServerManager extends EventEmitter {
  private config: MCPServerConfig;
  private patService: GitHubPATService;
  private transport?: MCPStdioTransport;
  private process?: ChildProcess;
  private status: MCPServerStatus;
  private tools: MCPTool[] = [];
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectDelay = 1000; // Start with 1 second
  private healthCheckInterval?: NodeJS.Timeout;

  constructor(config: MCPServerConfig) {
    super();
    this.config = config;
    this.patService = new GitHubPATService();
    this.status = {
      id: config.id,
      status: 'disconnected'
    };
  }

  /**
   * Connects to the GitHub MCP server
   */
  async connect(credentials?: Record<string, string>): Promise<void> {
    try {
      log.info('Starting GitHub MCP server connection');

      // Update status
      this.updateStatus('connecting');

      // Get or validate PAT
      let pat: string | null = null;
      
      if (credentials?.pat) {
        // New PAT provided, validate and store it
        const validation = await this.patService.storePAT(credentials.pat);
        if (!validation.valid) {
          throw this.createMCPError(
            validation.errorType || 'auth_failed',
            validation.error || 'PAT validation failed'
          );
        }
        pat = credentials.pat;
      } else {
        // Try to use stored PAT
        pat = await this.patService.getPAT();
        if (!pat) {
          throw this.createMCPError('auth_failed', 'No GitHub PAT found. Please provide a valid PAT.');
        }

        // Validate stored PAT
        const validation = await this.patService.validateStoredPAT();
        if (!validation.valid) {
          throw this.createMCPError(
            validation.errorType || 'auth_failed',
            validation.error || 'Stored PAT is invalid'
          );
        }
      }

      // Start Docker container
      await this.startDockerContainer(pat);

      // Initialize transport
      if (!this.process) {
        throw this.createMCPError('docker_launch_failure', 'Failed to start Docker process');
      }

      this.transport = new MCPStdioTransport(this.process);
      
      // Set up transport event listeners
      this.setupTransportListeners();

      // Connect transport
      await this.transport.connect();

      // Initialize MCP protocol
      await this.initializeMCPProtocol();

      // Discover available tools
      await this.discoverTools();

      // Start health checking
      this.startHealthChecking();

      // Update status
      this.updateStatus('connected');

      log.info('GitHub MCP server connected successfully');

    } catch (error) {
      log.error('Failed to connect to GitHub MCP server:', error);
      await this.cleanup();
      throw error;
    }
  }

  /**
   * Disconnects from the GitHub MCP server
   */
  async disconnect(): Promise<void> {
    try {
      log.info('Disconnecting from GitHub MCP server');

      this.updateStatus('disconnected');
      await this.cleanup();

      log.info('GitHub MCP server disconnected successfully');

    } catch (error) {
      log.error('Error during GitHub MCP server disconnect:', error);
      throw error;
    }
  }

  /**
   * Executes a tool on the GitHub MCP server
   */
  async executeTool(toolName: string, parameters: Record<string, any>): Promise<any> {
    if (!this.transport || this.status.status !== 'connected') {
      throw this.createMCPError('communication_error', 'MCP server not connected');
    }

    try {
      log.debug(`Executing tool: ${toolName}`, parameters);

      const request: MCPMessage = {
        jsonrpc: '2.0',
        id: this.generateRequestId(),
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: parameters
        }
      };

      const response = await this.transport.send(request);

      if (response.error) {
        throw new Error(`Tool execution failed: ${response.error.message}`);
      }

      log.debug(`Tool ${toolName} executed successfully`);
      return response.result;

    } catch (error) {
      log.error(`Tool execution failed for ${toolName}:`, error);
      throw error;
    }
  }

  /**
   * Gets the current server status
   */
  getStatus(): MCPServerStatus {
    return { ...this.status };
  }

  /**
   * Gets available tools
   */
  getTools(): MCPTool[] {
    return [...this.tools];
  }

  /**
   * Performs a health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.transport || this.status.status !== 'connected') {
        return false;
      }

      // Check if process is still running
      if (this.process && this.process.killed) {
        log.warn('GitHub MCP server process has been killed');
        return false;
      }

      // Send ping request
      const request: MCPMessage = {
        jsonrpc: '2.0',
        id: this.generateRequestId(),
        method: 'ping'
      };

      const response = await Promise.race([
        this.transport.send(request),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Health check timeout')), 5000)
        )
      ]);

      return !response.error;

    } catch (error) {
      log.warn('Health check failed:', error);
      return false;
    }
  }

  /**
   * Starts the Docker container with the GitHub MCP server
   */
  private async startDockerContainer(pat: string): Promise<void> {
    try {
      log.info('Starting GitHub MCP server Docker container');

      // Prepare Docker command
      const dockerArgs = [
        'run', '-i', '--rm',
        '-e', `GITHUB_PERSONAL_ACCESS_TOKEN=${pat}`,
        'ghcr.io/github/github-mcp-server:latest'
      ];

      // Spawn Docker process
      this.process = spawn('docker', dockerArgs, {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          // Ensure PAT is not inherited from parent process
          GITHUB_PERSONAL_ACCESS_TOKEN: undefined
        }
      });

      // Set up process event listeners
      this.setupProcessListeners();

      // Wait for container to be ready
      await this.waitForContainerReady();

      log.info('GitHub MCP server Docker container started successfully');

    } catch (error) {
      log.error('Failed to start Docker container:', error);
      throw this.createMCPError('docker_launch_failure', `Failed to start Docker container: ${error}`);
    }
  }

  /**
   * Sets up process event listeners
   */
  private setupProcessListeners(): void {
    if (!this.process) return;

    this.process.on('error', (error) => {
      log.error('Docker process error:', error);
      this.handleProcessError(error);
    });

    this.process.on('exit', (code, signal) => {
      log.warn(`Docker process exited with code ${code}, signal ${signal}`);
      this.handleProcessExit(code, signal);
    });

    // Log stderr for debugging
    this.process.stderr?.on('data', (data) => {
      const message = data.toString().trim();
      if (message) {
        log.debug('Docker stderr:', message);
      }
    });
  }

  /**
   * Waits for the Docker container to be ready
   */
  private async waitForContainerReady(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.process) {
        reject(new Error('No process available'));
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('Container startup timeout'));
      }, 30000); // 30 second timeout

      let stdoutBuffer = '';

      const onData = (data: Buffer) => {
        stdoutBuffer += data.toString();
        
        // Look for ready indicator
        if (stdoutBuffer.includes('Ready') || stdoutBuffer.includes('{"jsonrpc"')) {
          clearTimeout(timeout);
          this.process?.stdout?.off('data', onData);
          resolve();
        }
      };

      this.process.stdout?.on('data', onData);
    });
  }

  /**
   * Sets up transport event listeners
   */
  private setupTransportListeners(): void {
    if (!this.transport) return;

    this.transport.onError((error) => {
      log.error('Transport error:', error);
      this.handleTransportError(error);
    });

    this.transport.onDisconnect(() => {
      log.warn('Transport disconnected');
      this.handleTransportDisconnect();
    });

    this.transport.onMessage((message) => {
      log.debug('Received MCP message:', message);
      // Handle incoming messages if needed
    });
  }

  /**
   * Initializes the MCP protocol
   */
  private async initializeMCPProtocol(): Promise<void> {
    if (!this.transport) {
      throw new Error('Transport not available');
    }

    try {
      // Send initialize request
      const initRequest: MCPMessage = {
        jsonrpc: '2.0',
        id: this.generateRequestId(),
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {}
          },
          clientInfo: {
            name: 'Alpine Intellect',
            version: '1.0.0'
          }
        }
      };

      const response = await this.transport.send(initRequest);

      if (response.error) {
        throw new Error(`MCP initialization failed: ${response.error.message}`);
      }

      log.debug('MCP protocol initialized successfully');

    } catch (error) {
      log.error('MCP protocol initialization failed:', error);
      throw error;
    }
  }

  /**
   * Discovers available tools from the MCP server
   */
  private async discoverTools(): Promise<void> {
    if (!this.transport) {
      throw new Error('Transport not available');
    }

    try {
      const toolsRequest: MCPMessage = {
        jsonrpc: '2.0',
        id: this.generateRequestId(),
        method: 'tools/list'
      };

      const response = await this.transport.send(toolsRequest);

      if (response.error) {
        throw new Error(`Tool discovery failed: ${response.error.message}`);
      }

      this.tools = response.result?.tools || [];
      
      log.info(`Discovered ${this.tools.length} tools:`, this.tools.map(t => t.name));
      
      // Emit tools updated event
      this.emit('tools-updated', this.tools);

    } catch (error) {
      log.error('Tool discovery failed:', error);
      // Don't throw - tools discovery failure shouldn't prevent connection
    }
  }

  /**
   * Starts health checking
   */
  private startHealthChecking(): void {
    this.healthCheckInterval = setInterval(async () => {
      const isHealthy = await this.healthCheck();
      if (!isHealthy && this.status.status === 'connected') {
        log.warn('Health check failed, attempting reconnection');
        await this.attemptReconnection();
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Handles process errors
   */
  private handleProcessError(error: Error): void {
    log.error('Process error:', error);
    this.updateStatus('error', error.message);
    this.emit('error', this.createMCPError('docker_launch_failure', error.message));
  }

  /**
   * Handles process exit
   */
  private handleProcessExit(code: number | null, signal: string | null): void {
    log.warn(`Process exited with code ${code}, signal ${signal}`);
    
    if (this.status.status === 'connected') {
      this.updateStatus('error', `Process exited unexpectedly (code: ${code}, signal: ${signal})`);
      this.emit('error', this.createMCPError('server_crash', 'MCP server process exited unexpectedly'));
      
      // Attempt reconnection
      this.attemptReconnection();
    }
  }

  /**
   * Handles transport errors
   */
  private handleTransportError(error: Error): void {
    log.error('Transport error:', error);
    this.updateStatus('error', error.message);
    this.emit('error', this.createMCPError('communication_error', error.message));
  }

  /**
   * Handles transport disconnect
   */
  private handleTransportDisconnect(): void {
    log.warn('Transport disconnected');
    if (this.status.status === 'connected') {
      this.updateStatus('error', 'Transport disconnected');
      this.attemptReconnection();
    }
  }

  /**
   * Attempts to reconnect to the server
   */
  private async attemptReconnection(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      log.error('Max reconnection attempts reached');
      this.updateStatus('error', 'Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    log.info(`Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);

    setTimeout(async () => {
      try {
        await this.cleanup();
        await this.connect();
        this.reconnectAttempts = 0; // Reset on successful reconnection
      } catch (error) {
        log.error('Reconnection attempt failed:', error);
        await this.attemptReconnection();
      }
    }, delay);
  }

  /**
   * Updates the server status
   */
  private updateStatus(status: MCPServerStatus['status'], error?: string): void {
    this.status = {
      ...this.status,
      status,
      error,
      lastConnected: status === 'connected' ? new Date() : this.status.lastConnected,
      processId: this.process?.pid,
      tools: this.tools
    };

    this.emit('status-changed', this.status);
  }

  /**
   * Cleans up resources
   */
  private async cleanup(): Promise<void> {
    // Clear health check interval
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = undefined;
    }

    // Disconnect transport
    if (this.transport) {
      try {
        await this.transport.disconnect();
      } catch (error) {
        log.warn('Error disconnecting transport:', error);
      }
      this.transport = undefined;
    }

    // Kill process
    if (this.process && !this.process.killed) {
      try {
        this.process.kill('SIGTERM');
        
        // Wait for graceful shutdown, then force kill if needed
        setTimeout(() => {
          if (this.process && !this.process.killed) {
            this.process.kill('SIGKILL');
          }
        }, 5000);
      } catch (error) {
        log.warn('Error killing process:', error);
      }
      this.process = undefined;
    }

    // Clear tools
    this.tools = [];
  }

  /**
   * Creates an MCP error
   */
  private createMCPError(code: string, message: string): MCPError {
    const error = new Error(message) as MCPError;
    error.code = code;
    error.type = this.getErrorType(code);
    return error;
  }

  /**
   * Gets error type from error code
   */
  private getErrorType(code: string): MCPError['type'] {
    switch (code) {
      case MCP_ERROR_CODES.INVALID_PAT_FORMAT:
      case MCP_ERROR_CODES.AUTH_FAILED:
      case MCP_ERROR_CODES.INSUFFICIENT_SCOPE:
        return 'pat_error';
      case MCP_ERROR_CODES.DOCKER_LAUNCH_FAILURE:
      case MCP_ERROR_CODES.SERVER_CRASH:
        return 'server_error';
      case MCP_ERROR_CODES.RATE_LIMIT:
        return 'api_error';
      case MCP_ERROR_CODES.NETWORK_ERROR:
      case MCP_ERROR_CODES.COMMUNICATION_ERROR:
        return 'network_error';
      default:
        return 'server_error';
    }
  }

  /**
   * Generates a unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
