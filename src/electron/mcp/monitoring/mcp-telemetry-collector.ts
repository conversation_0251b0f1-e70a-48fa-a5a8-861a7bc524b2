// MCP Telemetry Collector - Collects and reports telemetry data
import log from 'electron-log';
import { app } from 'electron';
import { MCPTelemetryEvent } from '../core/mcp-types.js';

export class MCPTelemetryCollector {
  private enabled: boolean;
  private events: MCPTelemetryEvent[] = [];
  private reportInterval?: NodeJS.Timeout;
  private reportIntervalMs = 6 * 60 * 60 * 1000; // 6 hours
  private maxEventsBuffer = 1000;
  private telemetryEndpoint = 'http://telemetry.alpine-api/v1/metrics';

  constructor(enabled = true) {
    this.enabled = enabled;
    
    if (this.enabled) {
      this.startReportInterval();
    }
  }

  /**
   * Collects a telemetry event
   */
  collectEvent(event: MCPTelemetryEvent): void {
    if (!this.enabled) {
      return;
    }

    try {
      // Add system context to event
      const enrichedEvent: MCPTelemetryEvent = {
        ...event,
        properties: {
          ...event.properties,
          appVersion: app.getVersion(),
          platform: process.platform,
          arch: process.arch,
          nodeVersion: process.version
        }
      };

      this.events.push(enrichedEvent);

      // Trim buffer if too large
      if (this.events.length > this.maxEventsBuffer) {
        this.events = this.events.slice(-this.maxEventsBuffer);
      }

      log.debug('Telemetry event collected:', event.event);

    } catch (error) {
      log.error('Error collecting telemetry event:', error);
    }
  }

  /**
   * Collects a server connect attempt event
   */
  collectServerConnectAttempt(serverId: string, success: boolean, details?: any): void {
    this.collectEvent({
      event: 'server_connect_attempt',
      serverId,
      timestamp: new Date(),
      properties: {
        success,
        ...details
      }
    });
  }

  /**
   * Collects a PAT validated event
   */
  collectPATValidated(serverId: string, success: boolean, details?: any): void {
    this.collectEvent({
      event: 'pat_validated',
      serverId,
      timestamp: new Date(),
      properties: {
        success,
        ...details
      }
    });
  }

  /**
   * Collects an MCP server started event
   */
  collectMCPServerStarted(serverId: string, details?: any): void {
    this.collectEvent({
      event: 'mcp_server_started',
      serverId,
      timestamp: new Date(),
      properties: details
    });
  }

  /**
   * Collects an available tools listed event
   */
  collectAvailableToolsListed(serverId: string, toolCount: number, toolNames: string[]): void {
    this.collectEvent({
      event: 'available_tools_listed',
      serverId,
      timestamp: new Date(),
      properties: {
        toolCount,
        toolNames: toolNames.slice(0, 10) // Limit to first 10 tool names
      }
    });
  }

  /**
   * Collects an error detected event
   */
  collectErrorDetected(serverId: string, errorType: string, errorCode?: string): void {
    this.collectEvent({
      event: 'error_detected',
      serverId,
      timestamp: new Date(),
      properties: {
        errorType,
        errorCode
      }
    });
  }

  /**
   * Collects a tool execution event
   */
  collectToolExecution(serverId: string, toolName: string, success: boolean, executionTime: number): void {
    this.collectEvent({
      event: 'tool_executed',
      serverId,
      timestamp: new Date(),
      properties: {
        toolName,
        success,
        executionTime
      }
    });
  }

  /**
   * Reports telemetry data to the endpoint
   */
  private async reportTelemetry(): Promise<void> {
    if (!this.enabled || this.events.length === 0) {
      return;
    }

    try {
      log.debug(`Reporting ${this.events.length} telemetry events`);

      const payload = {
        events: this.events,
        metadata: {
          reportedAt: new Date().toISOString(),
          appVersion: app.getVersion(),
          platform: process.platform,
          arch: process.arch
        }
      };

      const response = await fetch(this.telemetryEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': `Alpine-Intellect-Desktop/${app.getVersion()}`
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(30000) // 30 second timeout
      });

      if (response.ok) {
        log.debug('Telemetry reported successfully');
        this.events = []; // Clear events after successful report
      } else {
        log.warn(`Telemetry report failed: ${response.status} ${response.statusText}`);
      }

    } catch (error) {
      log.error('Error reporting telemetry:', error);
      // Don't clear events on error - they'll be retried next time
    }
  }

  /**
   * Starts the report interval
   */
  private startReportInterval(): void {
    this.reportInterval = setInterval(() => {
      this.reportTelemetry();
    }, this.reportIntervalMs);

    // Also report on app exit
    app.on('before-quit', () => {
      this.reportTelemetry();
    });
  }

  /**
   * Gets telemetry statistics
   */
  getStats(): {
    enabled: boolean;
    eventsBuffered: number;
    eventsByType: Record<string, number>;
    eventsByServer: Record<string, number>;
    oldestEvent?: Date;
    newestEvent?: Date;
  } {
    const eventsByType: Record<string, number> = {};
    const eventsByServer: Record<string, number> = {};
    let oldestEvent: Date | undefined;
    let newestEvent: Date | undefined;

    for (const event of this.events) {
      // Count by event type
      eventsByType[event.event] = (eventsByType[event.event] || 0) + 1;

      // Count by server
      eventsByServer[event.serverId] = (eventsByServer[event.serverId] || 0) + 1;

      // Track date range
      const eventDate = new Date(event.timestamp);
      if (!oldestEvent || eventDate < oldestEvent) {
        oldestEvent = eventDate;
      }
      if (!newestEvent || eventDate > newestEvent) {
        newestEvent = eventDate;
      }
    }

    return {
      enabled: this.enabled,
      eventsBuffered: this.events.length,
      eventsByType,
      eventsByServer,
      oldestEvent,
      newestEvent
    };
  }

  /**
   * Enables or disables telemetry collection
   */
  setEnabled(enabled: boolean): void {
    this.enabled = enabled;

    if (!enabled) {
      // Clear buffered events when disabled
      this.events = [];
      
      if (this.reportInterval) {
        clearInterval(this.reportInterval);
        this.reportInterval = undefined;
      }
    } else if (!this.reportInterval) {
      // Restart interval when re-enabled
      this.startReportInterval();
    }

    log.info(`Telemetry collection ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Sets the telemetry endpoint
   */
  setEndpoint(endpoint: string): void {
    this.telemetryEndpoint = endpoint;
    log.debug(`Telemetry endpoint set to: ${endpoint}`);
  }

  /**
   * Sets the report interval
   */
  setReportInterval(intervalMs: number): void {
    this.reportIntervalMs = intervalMs;

    // Restart interval with new timing
    if (this.reportInterval) {
      clearInterval(this.reportInterval);
      this.startReportInterval();
    }

    log.debug(`Telemetry report interval set to: ${intervalMs}ms`);
  }

  /**
   * Forces an immediate telemetry report
   */
  async forceReport(): Promise<void> {
    await this.reportTelemetry();
  }

  /**
   * Clears all buffered telemetry events
   */
  clearEvents(): void {
    this.events = [];
    log.debug('Telemetry events cleared');
  }

  /**
   * Gets a copy of all buffered events
   */
  getEvents(): MCPTelemetryEvent[] {
    return [...this.events];
  }

  /**
   * Shuts down the telemetry collector
   */
  async shutdown(): Promise<void> {
    try {
      // Clear report interval
      if (this.reportInterval) {
        clearInterval(this.reportInterval);
        this.reportInterval = undefined;
      }

      // Send final report if enabled
      if (this.enabled) {
        await this.reportTelemetry();
      }

      log.debug('Telemetry collector shutdown complete');

    } catch (error) {
      log.error('Error shutting down telemetry collector:', error);
    }
  }
}
