// MCP Audit Logger - Handles audit logging for MCP operations
import fs from 'fs-extra';
import path from 'path';
import log from 'electron-log';
import { app } from 'electron';
import { MCPAuditEvent } from '../core/mcp-types.js';

export class MCPAuditLogger {
  private logFilePath: string;
  private enabled: boolean;
  private logQueue: MCPAuditEvent[] = [];
  private flushInterval?: NodeJS.Timeout;
  private maxQueueSize = 100;
  private flushIntervalMs = 5000; // 5 seconds

  constructor(enabled = true) {
    this.enabled = enabled;
    this.logFilePath = this.getLogFilePath();
    
    if (this.enabled) {
      this.initializeLogFile();
      this.startFlushInterval();
    }
  }

  /**
   * Logs an audit event
   */
  logEvent(event: MCPAuditEvent): void {
    if (!this.enabled) {
      return;
    }

    try {
      // Add to queue
      this.logQueue.push(event);

      // Flush if queue is full
      if (this.logQueue.length >= this.maxQueueSize) {
        this.flushLogs();
      }

      log.debug('Audit event logged:', event.event);

    } catch (error) {
      log.error('Error logging audit event:', error);
    }
  }

  /**
   * Logs a server startup event
   */
  logServerStartup(serverId: string, details?: any): void {
    this.logEvent({
      timestamp: new Date(),
      event: 'server_startup',
      serverId,
      details
    });
  }

  /**
   * Logs a server shutdown event
   */
  logServerShutdown(serverId: string, details?: any): void {
    this.logEvent({
      timestamp: new Date(),
      event: 'server_shutdown',
      serverId,
      details
    });
  }

  /**
   * Logs a PAT entry event
   */
  logPATEntry(serverId: string, success: boolean, details?: any): void {
    this.logEvent({
      timestamp: new Date(),
      event: 'pat_entry',
      serverId,
      details: {
        success,
        ...details
      }
    });
  }

  /**
   * Logs a PAT validation event
   */
  logPATValidation(serverId: string, success: boolean, details?: any): void {
    this.logEvent({
      timestamp: new Date(),
      event: 'pat_validation',
      serverId,
      details: {
        success,
        ...details
      }
    });
  }

  /**
   * Logs a tool usage event
   */
  logToolUsage(serverId: string, toolName: string, success: boolean, details?: any): void {
    this.logEvent({
      timestamp: new Date(),
      event: 'tool_usage',
      serverId,
      details: {
        toolName,
        success,
        ...details
      }
    });
  }

  /**
   * Logs an error event
   */
  logError(serverId: string, error: string, details?: any): void {
    this.logEvent({
      timestamp: new Date(),
      event: 'error_event',
      serverId,
      details: {
        error,
        ...details
      }
    });
  }

  /**
   * Flushes queued logs to file
   */
  private async flushLogs(): Promise<void> {
    if (this.logQueue.length === 0) {
      return;
    }

    try {
      const logsToFlush = [...this.logQueue];
      this.logQueue = [];

      // Format logs as JSONL (JSON Lines)
      const logLines = logsToFlush.map(event => JSON.stringify(event)).join('\n') + '\n';

      // Append to log file
      await fs.appendFile(this.logFilePath, logLines);

      log.debug(`Flushed ${logsToFlush.length} audit logs to file`);

    } catch (error) {
      log.error('Error flushing audit logs:', error);
      
      // Put logs back in queue if write failed
      this.logQueue.unshift(...this.logQueue);
    }
  }

  /**
   * Starts the flush interval
   */
  private startFlushInterval(): void {
    this.flushInterval = setInterval(() => {
      this.flushLogs();
    }, this.flushIntervalMs);
  }

  /**
   * Initializes the log file
   */
  private async initializeLogFile(): Promise<void> {
    try {
      // Ensure log directory exists
      const logDir = path.dirname(this.logFilePath);
      await fs.ensureDir(logDir);

      // Create log file if it doesn't exist
      if (!(await fs.pathExists(this.logFilePath))) {
        await fs.writeFile(this.logFilePath, '');
        log.info(`Created audit log file: ${this.logFilePath}`);
      }

      // Rotate log file if it's too large (>10MB)
      await this.rotateLogFileIfNeeded();

    } catch (error) {
      log.error('Error initializing audit log file:', error);
      this.enabled = false;
    }
  }

  /**
   * Rotates log file if it's too large
   */
  private async rotateLogFileIfNeeded(): Promise<void> {
    try {
      const stats = await fs.stat(this.logFilePath);
      const maxSize = 10 * 1024 * 1024; // 10MB

      if (stats.size > maxSize) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const rotatedPath = this.logFilePath.replace('.log', `-${timestamp}.log`);
        
        await fs.move(this.logFilePath, rotatedPath);
        await fs.writeFile(this.logFilePath, '');
        
        log.info(`Rotated audit log file to: ${rotatedPath}`);
      }

    } catch (error) {
      log.warn('Error rotating audit log file:', error);
    }
  }

  /**
   * Gets the log file path
   */
  private getLogFilePath(): string {
    const userDataPath = app.getPath('userData');
    const logsDir = path.join(userDataPath, 'logs');
    return path.join(logsDir, 'github-mcp-connect.log');
  }

  /**
   * Reads audit logs from file
   */
  async readLogs(limit = 1000): Promise<MCPAuditEvent[]> {
    try {
      if (!(await fs.pathExists(this.logFilePath))) {
        return [];
      }

      const content = await fs.readFile(this.logFilePath, 'utf-8');
      const lines = content.trim().split('\n').filter(line => line.trim());

      // Parse JSON lines and return most recent entries
      const events = lines
        .slice(-limit)
        .map(line => {
          try {
            return JSON.parse(line) as MCPAuditEvent;
          } catch (error) {
            log.warn('Error parsing audit log line:', line, error);
            return null;
          }
        })
        .filter((event): event is MCPAuditEvent => event !== null);

      return events;

    } catch (error) {
      log.error('Error reading audit logs:', error);
      return [];
    }
  }

  /**
   * Searches audit logs by criteria
   */
  async searchLogs(criteria: {
    serverId?: string;
    event?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }): Promise<MCPAuditEvent[]> {
    try {
      const allLogs = await this.readLogs(10000); // Read more for searching

      let filteredLogs = allLogs;

      // Filter by server ID
      if (criteria.serverId) {
        filteredLogs = filteredLogs.filter(log => log.serverId === criteria.serverId);
      }

      // Filter by event type
      if (criteria.event) {
        filteredLogs = filteredLogs.filter(log => log.event === criteria.event);
      }

      // Filter by date range
      if (criteria.startDate) {
        filteredLogs = filteredLogs.filter(log => 
          new Date(log.timestamp) >= criteria.startDate!
        );
      }

      if (criteria.endDate) {
        filteredLogs = filteredLogs.filter(log => 
          new Date(log.timestamp) <= criteria.endDate!
        );
      }

      // Apply limit
      const limit = criteria.limit || 1000;
      return filteredLogs.slice(-limit);

    } catch (error) {
      log.error('Error searching audit logs:', error);
      return [];
    }
  }

  /**
   * Gets audit log statistics
   */
  async getLogStats(): Promise<{
    totalEvents: number;
    eventsByType: Record<string, number>;
    eventsByServer: Record<string, number>;
    dateRange: { start?: Date; end?: Date };
  }> {
    try {
      const logs = await this.readLogs(10000);

      const eventsByType: Record<string, number> = {};
      const eventsByServer: Record<string, number> = {};
      let startDate: Date | undefined;
      let endDate: Date | undefined;

      for (const log of logs) {
        // Count by event type
        eventsByType[log.event] = (eventsByType[log.event] || 0) + 1;

        // Count by server
        eventsByServer[log.serverId] = (eventsByServer[log.serverId] || 0) + 1;

        // Track date range
        const logDate = new Date(log.timestamp);
        if (!startDate || logDate < startDate) {
          startDate = logDate;
        }
        if (!endDate || logDate > endDate) {
          endDate = logDate;
        }
      }

      return {
        totalEvents: logs.length,
        eventsByType,
        eventsByServer,
        dateRange: { start: startDate, end: endDate }
      };

    } catch (error) {
      log.error('Error getting log stats:', error);
      return {
        totalEvents: 0,
        eventsByType: {},
        eventsByServer: {},
        dateRange: {}
      };
    }
  }

  /**
   * Clears old audit logs
   */
  async clearOldLogs(olderThanDays = 30): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const logs = await this.readLogs(10000);
      const recentLogs = logs.filter(log => 
        new Date(log.timestamp) > cutoffDate
      );

      // Rewrite file with only recent logs
      const logLines = recentLogs.map(event => JSON.stringify(event)).join('\n') + '\n';
      await fs.writeFile(this.logFilePath, logLines);

      log.info(`Cleared audit logs older than ${olderThanDays} days`);

    } catch (error) {
      log.error('Error clearing old audit logs:', error);
    }
  }

  /**
   * Shuts down the audit logger
   */
  async shutdown(): Promise<void> {
    try {
      // Clear flush interval
      if (this.flushInterval) {
        clearInterval(this.flushInterval);
        this.flushInterval = undefined;
      }

      // Flush remaining logs
      await this.flushLogs();

      log.debug('Audit logger shutdown complete');

    } catch (error) {
      log.error('Error shutting down audit logger:', error);
    }
  }
}
