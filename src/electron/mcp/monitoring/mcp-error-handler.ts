// MCP Error Handler - Centralized error handling for MCP operations
import log from 'electron-log';
import { MCPError, MCP_ERROR_CODES } from '../core/mcp-types.js';

export class MCPErrorHandler {
  private static instance: MCPErrorHandler;
  private errorCounts: Map<string, number> = new Map();
  private lastErrors: Map<string, Date> = new Map();
  private maxRetries = 3;
  private retryDelay = 1000; // 1 second base delay

  private constructor() {}

  static getInstance(): MCPErrorHandler {
    if (!MCPErrorHandler.instance) {
      MCPErrorHandler.instance = new MCPErrorHandler();
    }
    return MCPErrorHandler.instance;
  }

  /**
   * Handles an MCP error with appropriate logging and recovery
   */
  handleError(error: MCPError, context?: string): {
    shouldRetry: boolean;
    retryDelay: number;
    userMessage: string;
  } {
    const errorKey = `${error.code}_${context || 'general'}`;
    
    // Track error frequency
    this.errorCounts.set(errorKey, (this.errorCounts.get(errorKey) || 0) + 1);
    this.lastErrors.set(errorKey, new Date());

    // Log error with context
    log.error(`MCP Error [${error.code}] in ${context || 'unknown context'}:`, {
      message: error.message,
      type: error.type,
      details: error.details,
      stack: error.stack
    });

    // Determine retry strategy
    const errorCount = this.errorCounts.get(errorKey) || 0;
    const shouldRetry = this.shouldRetryError(error, errorCount);
    const retryDelay = this.calculateRetryDelay(errorCount);
    const userMessage = this.getUserFriendlyMessage(error);

    return {
      shouldRetry,
      retryDelay,
      userMessage
    };
  }

  /**
   * Determines if an error should be retried
   */
  private shouldRetryError(error: MCPError, errorCount: number): boolean {
    // Don't retry if we've exceeded max attempts
    if (errorCount >= this.maxRetries) {
      return false;
    }

    // Determine retry based on error type
    switch (error.code) {
      case MCP_ERROR_CODES.INVALID_PAT_FORMAT:
      case MCP_ERROR_CODES.AUTH_FAILED:
      case MCP_ERROR_CODES.INSUFFICIENT_SCOPE:
        // Authentication errors - don't retry automatically
        return false;

      case MCP_ERROR_CODES.NETWORK_ERROR:
      case MCP_ERROR_CODES.COMMUNICATION_ERROR:
        // Network errors - retry with backoff
        return true;

      case MCP_ERROR_CODES.DOCKER_LAUNCH_FAILURE:
        // Docker errors - retry once
        return errorCount < 2;

      case MCP_ERROR_CODES.SERVER_CRASH:
        // Server crashes - retry with longer delay
        return errorCount < 2;

      case MCP_ERROR_CODES.RATE_LIMIT:
        // Rate limiting - retry after delay
        return true;

      default:
        // Unknown errors - retry once
        return errorCount < 2;
    }
  }

  /**
   * Calculates retry delay with exponential backoff
   */
  private calculateRetryDelay(errorCount: number): number {
    const baseDelay = this.retryDelay;
    const exponentialDelay = baseDelay * Math.pow(2, errorCount - 1);
    const maxDelay = 30000; // 30 seconds max
    
    return Math.min(exponentialDelay, maxDelay);
  }

  /**
   * Converts technical errors to user-friendly messages
   */
  private getUserFriendlyMessage(error: MCPError): string {
    switch (error.code) {
      case MCP_ERROR_CODES.INVALID_PAT_FORMAT:
        return 'Invalid Personal Access Token format. Please check that your token starts with "ghp_" or "github_pat_" and is at least 40 characters long.';

      case MCP_ERROR_CODES.AUTH_FAILED:
        return 'Authentication failed. Please check your Personal Access Token and try again.';

      case MCP_ERROR_CODES.INSUFFICIENT_SCOPE:
        return 'Your Personal Access Token doesn\'t have the required permissions. Please regenerate your token with "repo", "user", and "read:org" scopes.';

      case MCP_ERROR_CODES.NETWORK_ERROR:
        return 'Network connection failed. Please check your internet connection and try again.';

      case MCP_ERROR_CODES.DOCKER_LAUNCH_FAILURE:
        return 'Failed to start the GitHub MCP server. Please ensure Docker is installed and running.';

      case MCP_ERROR_CODES.COMMUNICATION_ERROR:
        return 'Communication with the MCP server failed. The connection will be retried automatically.';

      case MCP_ERROR_CODES.SERVER_CRASH:
        return 'The MCP server stopped unexpectedly. Attempting to restart...';

      case MCP_ERROR_CODES.RATE_LIMIT:
        return 'GitHub API rate limit reached. Please wait a moment before trying again.';

      default:
        return `An unexpected error occurred: ${error.message}`;
    }
  }

  /**
   * Creates a standardized MCP error
   */
  createError(
    code: string,
    message: string,
    type: MCPError['type'] = 'server_error',
    details?: any
  ): MCPError {
    const error = new Error(message) as MCPError;
    error.code = code;
    error.type = type;
    error.details = details;
    return error;
  }

  /**
   * Wraps a function with error handling
   */
  async withErrorHandling<T>(
    operation: () => Promise<T>,
    context: string,
    onError?: (error: MCPError) => void
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      const mcpError = this.normalizeError(error);
      const handling = this.handleError(mcpError, context);
      
      if (onError) {
        onError(mcpError);
      }

      throw mcpError;
    }
  }

  /**
   * Normalizes any error to MCPError format
   */
  private normalizeError(error: any): MCPError {
    if (error instanceof Error && 'code' in error && 'type' in error) {
      return error as MCPError;
    }

    // Convert regular errors to MCP errors
    const message = error instanceof Error ? error.message : String(error);
    return this.createError('UNKNOWN_ERROR', message, 'server_error');
  }

  /**
   * Gets error statistics
   */
  getErrorStats(): {
    totalErrors: number;
    errorsByCode: Record<string, number>;
    recentErrors: Array<{ code: string; timestamp: Date; count: number }>;
  } {
    const totalErrors = Array.from(this.errorCounts.values()).reduce((sum, count) => sum + count, 0);
    
    const errorsByCode: Record<string, number> = {};
    for (const [key, count] of this.errorCounts) {
      const code = key.split('_')[0];
      errorsByCode[code] = (errorsByCode[code] || 0) + count;
    }

    const recentErrors = Array.from(this.errorCounts.entries())
      .map(([key, count]) => ({
        code: key,
        timestamp: this.lastErrors.get(key) || new Date(),
        count
      }))
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 10);

    return {
      totalErrors,
      errorsByCode,
      recentErrors
    };
  }

  /**
   * Clears error statistics
   */
  clearStats(): void {
    this.errorCounts.clear();
    this.lastErrors.clear();
  }

  /**
   * Checks if an error type is currently experiencing issues
   */
  isErrorFrequent(errorCode: string, timeWindowMs = 300000): boolean { // 5 minutes
    const now = new Date();
    let recentCount = 0;

    for (const [key, timestamp] of this.lastErrors) {
      if (key.startsWith(errorCode) && (now.getTime() - timestamp.getTime()) < timeWindowMs) {
        recentCount += this.errorCounts.get(key) || 0;
      }
    }

    return recentCount >= 3; // 3 or more errors in time window
  }

  /**
   * Gets recovery suggestions for an error
   */
  getRecoverySuggestions(error: MCPError): string[] {
    const suggestions: string[] = [];

    switch (error.code) {
      case MCP_ERROR_CODES.INVALID_PAT_FORMAT:
        suggestions.push('Verify your token format matches: ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx');
        suggestions.push('Copy the token directly from GitHub without extra spaces');
        break;

      case MCP_ERROR_CODES.AUTH_FAILED:
        suggestions.push('Check if your Personal Access Token is still valid');
        suggestions.push('Try regenerating your token on GitHub');
        suggestions.push('Ensure you copied the complete token');
        break;

      case MCP_ERROR_CODES.INSUFFICIENT_SCOPE:
        suggestions.push('Regenerate your token with "repo", "user", and "read:org" scopes');
        suggestions.push('Make sure all required scopes are selected when creating the token');
        break;

      case MCP_ERROR_CODES.NETWORK_ERROR:
        suggestions.push('Check your internet connection');
        suggestions.push('Verify GitHub.com is accessible');
        suggestions.push('Check if you\'re behind a corporate firewall');
        break;

      case MCP_ERROR_CODES.DOCKER_LAUNCH_FAILURE:
        suggestions.push('Ensure Docker Desktop is installed and running');
        suggestions.push('Check Docker daemon status');
        suggestions.push('Verify Docker has sufficient resources');
        break;

      case MCP_ERROR_CODES.RATE_LIMIT:
        suggestions.push('Wait for the rate limit to reset');
        suggestions.push('Consider using a different GitHub account');
        suggestions.push('Reduce the frequency of operations');
        break;

      default:
        suggestions.push('Try disconnecting and reconnecting');
        suggestions.push('Restart Alpine Intellect if the problem persists');
        suggestions.push('Check the logs for more detailed error information');
    }

    return suggestions;
  }
}
