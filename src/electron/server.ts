import { pipeline, env } from "@xenova/transformers";
import express, { Express, Request, Response } from "express";
import cors from "cors";
import { cpus } from "os";
import {
    cancelIndexing,
    latestIndexingStatus,
    startIndexing,
} from "./core/main.js";
import { retrieveContextItemsFromEmbeddings } from "./core/retrieval/retrieval.js";
import stateManager from "./global-state.js";
import { ModelConfigManager } from "./core/models/model-manager.js";

// Configure ONNX runtime for better performance
env.backends.onnx.wasm.numThreads = Math.min(4, cpus().length);
env.backends.onnx.wasm.simd = true;

export function createServer(): Express {
    const app = express();
    app.use(cors({
        origin: '*', 
        methods: ['GET', 'POST', 'OPTIONS'],
        allowedHeaders: ['Content-Type'],
        credentials: false,
    }));
    app.use(express.json({ limit: "50mb" }));

    // Start indexing endpoint
    app.post("/ain/start-indexing", async (req: Request, res: Response) => {
        try {
            const { dirPath } = req.body;

            void (async () => {
                try {
                    if (stateManager.hasActive()) {
                        stateManager.enqueue(dirPath);
                    } else {
                        stateManager.setActive(dirPath);
                        startIndexing(dirPath);
                    }
                } catch (e) {
                    console.log(
                        "Error in /ain/start-indexing initialization...",
                        e
                    );
                }
            })();

            res.status(202).json({
                status: "ok",
                message: "Succesfully initialized indexing...",
            });
        } catch (error) {
            console.log("Error in api/start-index..", error);
            res.status(500).json({
                error: "Failed to start indexing...",
            });
        }
    });

    // Indexing status endpoint
    app.post("/ain/indexing-status", (req: Request, res: Response) => {
        try {
            const { dirPath } = req.body;
            res.status(200).send(latestIndexingStatus(dirPath));
        } catch (error) {
            console.log(
                "Error while getting latest indexing status....",
                error
            );
            res.status(500).json({
                error: "Failed to get latest indexing status...",
            });
        }
    });

    // Indexing cancellation endpoint
    app.post("/ain/cancel-indexing", async (req: Request, res: Response) => {
        try {
            const { dirPath } = req.body;
            res.status(200).json({
                status: await cancelIndexing(dirPath),
            });
        } catch (error) {
            console.log(
                "Error while getting latest indexing status....",
                error
            );
            res.status(500).json({ status: false });
        }
    });

    //
    app.post("/ain/get-context", async (req: Request, res: Response) => {
        try {
            const { dirPath, query, modelId, availableTokens } = req.body;

            const context = await retrieveContextItemsFromEmbeddings(
                query,
                dirPath,
                modelId,
                availableTokens
            );

            res.status(200).json({
                status: "ok",
                context,
            });
        } catch (error) {
            console.log("Error in api/start-index..", error);
            res.status(500).json({
                error: "Failed to start indexing...",
                context: { contextText: "", usedReferences: {} },
            });
        }
    });

    // Model configuration endpoints for VS Code Extension sync
    app.get("/ain/model-config", async (req: Request, res: Response) => {
        try {
            const configManager = new ModelConfigManager();
            const config = await configManager.loadConfig();

            // Return simplified config for VS Code Extension
            const sharedConfig = {
                llmOptions: config.llmModels.filter(m => m.enabled).map(model => ({
                    id: model.id,
                    label: model.label,
                    enabled: model.enabled,
                    tokenBudget: model.tokenBudget,
                    provider: model.provider
                })),
                preferences: config.preferences,
                indexingOptions: config.indexingOptions,
                retrievalOptions: config.retrievalOptions,
                lastUpdated: new Date().toISOString()
            };

            res.status(200).json(sharedConfig);
        } catch (error: any) {
            console.error("Failed to get model configuration:", error.message);
            res.status(500).json({ error: "Failed to get model configuration" });
        }
    });

    // POST endpoint for VS Code Extension to update model configuration
    app.post("/ain/model-config", async (req: Request, res: Response) => {
        try {
            const incomingConfig = req.body;
            console.log("Received model config update from VS Code Extension:", incomingConfig);

            const configManager = new ModelConfigManager();
            const currentConfig = await configManager.loadConfig();

            // Merge incoming changes with current configuration
            const updatedConfig = {
                ...currentConfig,
                llmModels: currentConfig.llmModels.map(model => {
                    const incomingModel = incomingConfig.llmOptions?.find((m: any) => m.id === model.id);
                    if (incomingModel) {
                        return {
                            ...model,
                            enabled: incomingModel.enabled,
                            tokenBudget: incomingModel.tokenBudget || model.tokenBudget,
                            provider: incomingModel.provider || model.provider
                        };
                    }
                    return model;
                }),
                preferences: incomingConfig.preferences || currentConfig.preferences,
                indexingOptions: incomingConfig.indexingOptions || currentConfig.indexingOptions,
                retrievalOptions: incomingConfig.retrievalOptions || currentConfig.retrievalOptions
            };

            // Save updated configuration
            await configManager.saveConfig(updatedConfig);

            res.status(200).json({
                success: true,
                message: "Model configuration updated successfully",
                lastUpdated: new Date().toISOString()
            });

            console.log("Model configuration updated successfully from VS Code Extension");

        } catch (error: any) {
            console.error("Failed to update model configuration:", error.message);
            res.status(500).json({
                success: false,
                error: "Failed to update model configuration",
                details: error.message
            });
        }
    });

    // Health check endpoint
    app.get("/ain/health", (req: Request, res: Response) => {
        res.status(200).json({
            status: "ok",
            timestamp: new Date().toISOString(),
            version: "1.0.0",
            services: {
                modelConfig: true,
                indexing: true,
                webSocket: true
            }
        });
    });

    return app;
}
