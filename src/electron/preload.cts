// const electron = require("electron");

// electron.contextBridge.exposeInMainWorld("electron", {
//     subscribeStatistics: (callback) => {
//         electron.ipcRenderer.on("statistics", (_: any, stats: Statistics) => {
//             callback(stats);
//         });
//     },
//     getStaticData: () => electron.ipcRenderer.invoke("getStaticData"),
// } satisfies Window["electron"]);


const electron = require("electron");

// contextBridge.exposeInMainWorld('electron', {
//   appDir: __dirname, // expose safely
// });

electron.contextBridge.exposeInMainWorld("electron", {
    subscribeStatistics: (callback) =>
         ipcOn("statistics", (stats) => {
            callback(stats);
        }),
    subscribeChangeView: (callback) =>
        ipcOn("changeView", (view) => {
            callback(view);
        }),
    getStaticData: () => ipcInvoke("getStaticData"),
    sendFrameAction: (payload) => ipcSend("sendFrameAction", payload),
} satisfies Window["electron"]);

function ipcInvoke<Key extends keyof EventPayloadMapping>(
    key: Key
): Promise<EventPayloadMapping[Key]> {
    return electron.ipcRenderer.invoke(key);
}

function ipcOn<Key extends keyof EventPayloadMapping>(
    key: Key,
    callback: (payload: EventPayloadMapping[Key]) => void
) {
    const cb = (_: Electron.IpcRendererEvent, payload: any) =>
        callback(payload);
    electron.ipcRenderer.on(key, cb);
    return () => electron.ipcRenderer.off(key, cb);
}

function ipcSend<Key extends keyof EventPayloadMapping>(
    key: Key,
    payload: EventPayloadMapping[Key]
) {
    electron.ipcRenderer.send(key, payload);
}

import { contextBridge, ipcRenderer, shell } from 'electron';

contextBridge.exposeInMainWorld('api', {
  createLoginSession: async (source: string, redirectUri: string) => {
    return await ipcRenderer.invoke("create-login-session", { source, redirectUri });
  },
  openExternal: (url:string) => ipcRenderer.send('open-external', url),
  openFolderDialog: () => ipcRenderer.invoke('open-folder-dialog'),
  getSourceFiles: (folderPath: string) => ipcRenderer.invoke('read-source-files', folderPath),
  saveFile: (folderPath: string, fileName: string, content: string) =>
    ipcRenderer.invoke('save-file', folderPath, fileName, content),
  onAuthSuccess: (callback: (token: string) => void) => {
    ipcRenderer.on('auth-success', (_event, token: string) => {
      callback(token);
    });
  },
  // Model Management APIs
  getModelConfiguration: () => ipcRenderer.invoke('get-model-configuration'),
  saveModelConfiguration: (config: any) => ipcRenderer.invoke('save-model-configuration', config),
  toggleModel: (modelId: string, modelType: 'llm' | 'embedding') =>
    ipcRenderer.invoke('toggle-model', modelId, modelType),
  setPreferredModel: (type: string, modelId: string) =>
    ipcRenderer.invoke('set-preferred-model', type, modelId),
  clearModelCache: (modelId: string) => ipcRenderer.invoke('clear-model-cache', modelId),
  syncWithPlugins: () => ipcRenderer.invoke('sync-with-plugins'),
  removeAuthListeners: () => {
    ipcRenderer.removeAllListeners('auth-success');
  },
  scanIDEs: () => ipcRenderer.invoke("scan-ides"),
  getAuthToken: () => ipcRenderer.invoke("get-auth-token"),
  logout: () => ipcRenderer.invoke("logout"),
  getIDEPlugins: (ideName: string) => ipcRenderer.invoke('get-ide-plugins', ideName),
  checkPluginUpdate: (ide: string) => ipcRenderer.invoke('check-plugin-update', ide),
  downloadPlugin: (url: string) => ipcRenderer.invoke("download-plugin", url),
  installPlugin: (filePath: string, ide:string) => ipcRenderer.invoke("install-plugin", filePath,ide),
  onIndexingProgress: (callback: any) => ipcRenderer.on('indexing-progress', (_event, data) => callback(data)),
  onIndexingMeta: (callback: any) => ipcRenderer.on('indexing-meta', (_event, data) => callback(data)),
  loadExistingMeta: (): Promise<Record<string, any>> => {
    return ipcRenderer.invoke("load-existing-meta");
  },
  removeIndex: async (path: string) => {
    return await ipcRenderer.invoke('remove-index', path);
  },
  reindex: async (dirPath: string) => {
    return await ipcRenderer.invoke('reindex', dirPath);
  },
  resumeIndexing: async (dirPath: string) => {
    return await ipcRenderer.invoke('resume-indexing', dirPath);
  },
  pauseIndexing: async (dirPath: string, curProgress: number) => {
    return await ipcRenderer.invoke('pause-indexing', dirPath, curProgress);
  },

  // MCP Management APIs
  mcpGetServers: () => ipcRenderer.invoke('mcp-get-servers'),
  mcpGetServerStatus: (serverId: string) => ipcRenderer.invoke('mcp-get-server-status', serverId),
  mcpConnectServer: (serverId: string, credentials?: Record<string, string>) =>
    ipcRenderer.invoke('mcp-connect-server', serverId, credentials),
  mcpDisconnectServer: (serverId: string) => ipcRenderer.invoke('mcp-disconnect-server', serverId),
  mcpGetTools: (serverId: string) => ipcRenderer.invoke('mcp-get-tools', serverId),
  mcpExecuteTool: (request: { serverId: string; toolName: string; parameters: Record<string, any> }) =>
    ipcRenderer.invoke('mcp-execute-tool', request),
  mcpRestartServer: (serverId: string) => ipcRenderer.invoke('mcp-restart-server', serverId),
  mcpGetAllTools: () => ipcRenderer.invoke('mcp-get-all-tools'),

  // GitHub MCP APIs
  githubMCPConfigurePAT: (pat: string) => ipcRenderer.invoke('github-mcp-configure-pat', pat),
  githubMCPValidatePAT: (pat?: string) => ipcRenderer.invoke('github-mcp-validate-pat', pat),
  githubMCPGetUser: () => ipcRenderer.invoke('github-mcp-get-user'),
  githubMCPGetAuthStatus: () => ipcRenderer.invoke('github-mcp-get-auth-status'),
  githubMCPClearCredentials: () => ipcRenderer.invoke('github-mcp-clear-credentials'),
  githubMCPRefreshUser: () => ipcRenderer.invoke('github-mcp-refresh-user'),
  githubMCPHasCredentials: () => ipcRenderer.invoke('github-mcp-has-credentials'),

  // MCP Event Listeners
  onMCPServerStatusChanged: (callback: (status: any) => void) => {
    ipcRenderer.on('mcp-server-status-changed', (_event, status) => callback(status));
  },
  onMCPServerError: (callback: (data: any) => void) => {
    ipcRenderer.on('mcp-server-error', (_event, data) => callback(data));
  },
  onMCPToolsUpdated: (callback: (data: any) => void) => {
    ipcRenderer.on('mcp-tools-updated', (_event, data) => callback(data));
  },
  removeMCPListeners: () => {
    ipcRenderer.removeAllListeners('mcp-server-status-changed');
    ipcRenderer.removeAllListeners('mcp-server-error');
    ipcRenderer.removeAllListeners('mcp-tools-updated');
  },
});


