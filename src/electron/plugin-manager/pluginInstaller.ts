import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import os from 'os';

import { IDEName, ideRegistry } from '../ide-detector/ideRegistry.js';
import { resolveIDEExecutable } from '../ide-detector/resolveExecutable.js';


export function installPlugin(
  filePath: string,
  ide: IDEName
): Promise<{ success: boolean; error?: string }> {
  return new Promise((resolve) => {
    const pluginId = ideRegistry[ide]?.pluginId;
    const resolved = resolveIDEExecutable(ide);
    
    // Validate plugin ID
    if (!pluginId) {
      return resolve({ success: false, error: `No pluginId defined for ${ide}` });
    }
    
    // Validate executable path and fallback
    if (!resolved.path || resolved.source === 'prompt' as any) {
      return resolve({
        success: false,
        error: resolved.reason,
      });
    }
    
    const execPath = resolved.path.replace(/"/g, '');
    if (!fs.existsSync(execPath)) {
      return resolve({
        success: false,
        error: `Executable path not found: ${execPath}`,
      });
    }
    
    // Validate plugin file path
    if (!filePath || !fs.existsSync(filePath)) {
      return resolve({
        success: false,
        error: `Plugin file not found at: ${filePath}`,
      });
    }
    
    const uninstallCmd = `"${execPath}" --uninstall-extension ${pluginId}`;
    const installCmd = `"${execPath}" --install-extension "${filePath}"`;
    
    console.log(`[Plugin Install] Uninstalling: ${pluginId}`);
    exec(uninstallCmd, (uninstallErr, stdout, stderr) => {
      if (uninstallErr) {
        console.warn(`[Plugin Uninstall] Skipped: ${uninstallErr.message}`);
      }
      
      // Clean up plugin directory
      let baseDir: string | null = null;
      
      // More explicit IDE checking with debugging
      console.log(`[Debug] IDE value: "${ide}"`);
      
      if (ide === 'vscode') {
        baseDir = path.join(os.homedir(), '.vscode', 'extensions');
      } else if (ide === 'cursor') {
        baseDir = path.join(os.homedir(), '.cursor', 'extensions');
      } else {
        console.log(`[Debug] Unknown IDE: ${ide}`);
      }
      
      if (baseDir && fs.existsSync(baseDir)) {
        console.log(`[Plugin Cleanup] Scanning directory: ${baseDir}`);
        
        try {
          const pluginFolders = fs.readdirSync(baseDir).filter((folder) =>
            folder.startsWith(pluginId)
          );
          
          console.log(`[Plugin Cleanup] Found ${pluginFolders.length} matching folders:`, pluginFolders);
          
          for (const folder of pluginFolders) {
            const fullPath = path.join(baseDir, folder);
            try {
              fs.rmSync(fullPath, { recursive: true, force: true });
              console.log(`[Plugin Cleanup] Removed: ${fullPath}`);
            } catch (err) {
              console.warn(`[Plugin Cleanup] Failed to remove ${fullPath}: ${(err as Error).message}`);
            }
          }
        } catch (err) {
          console.warn(`[Plugin Cleanup] Failed to read directory ${baseDir}: ${(err as Error).message}`);
        }
      } else {
        console.log(`[Plugin Cleanup] Skipped - baseDir: ${baseDir}, exists: ${baseDir ? fs.existsSync(baseDir) : 'N/A'}`);
      }
      
      console.log(`[Plugin Install] Installing from: ${filePath}`);
      exec(installCmd, (installErr, stdout, stderr) => {
        if (installErr) {
          const errorOutput = stderr || installErr.message || 'Unknown error';
          if (/restart|already running/i.test(errorOutput)) {
            return resolve({
              success: false,
              error: 'Please close all IDE windows and try again.',
            });
          }
          return resolve({
            success: false,
            error: `Plugin install failed: ${errorOutput}`,
          });
        }
        console.log(`[Plugin Install] Success:\n${stdout}`);
        return resolve({ success: true });
      });
    });
  });
}

// export function installPlugin(
//   filePath: string,
//   ide: IDEName
// ): Promise<{ success: boolean; error?: string }> {
//   return new Promise((resolve) => {
//     const pluginId = ideRegistry[ide]?.pluginId;
//     const resolved = resolveIDEExecutable(ide);

//     // Validate plugin ID
//     if (!pluginId) {
//       return resolve({ success: false, error: `No pluginId defined for ${ide}` });
//     }

//     // Validate executable path and fallback
//     if (!resolved.path || resolved.source === 'prompt' as any) {
//       return resolve({
//         success: false,
//         error: resolved.reason,
//       });
//     }

//     const execPath = resolved.path.replace(/"/g, '');

//     if (!fs.existsSync(execPath)) {
//       return resolve({
//         success: false,
//         error: `Executable path not found: ${execPath}`,
//       });
//     }

//     // Validate plugin file path
//     if (!filePath || !fs.existsSync(filePath)) {
//       return resolve({
//         success: false,
//         error: `Plugin file not found at: ${filePath}`,
//       });
//     }

//     const uninstallCmd = `"${execPath}" --uninstall-extension ${pluginId}`;
//     const installCmd = `"${execPath}" --install-extension "${filePath}"`;

//     console.log(`[Plugin Install] Uninstalling: ${pluginId}`);
//     exec(uninstallCmd, (uninstallErr, _stdout, _stderr) => {
//       if (uninstallErr) {
//         console.warn(`[Plugin Uninstall] Skipped: ${uninstallErr.message}`);
//       }

//       // Clean up plugin directory
//       const baseDir =
//         ide === 'vscode'
//           ? path.join(os.homedir(), '.vscode', 'extensions')
//           : ide === 'cursor'
//           ? path.join(os.homedir(), '.cursor', 'extensions')
//           : null;

//       console.log("delete old version",{ide, baseDir, uninstallCmd, installCmd, doesexists: fs.existsSync(baseDir as string)});

//       if (baseDir && fs.existsSync(baseDir)) {
//         const pluginFolders = fs.readdirSync(baseDir).filter((folder) =>
//           folder.startsWith(pluginId)
//         );

//         for (const folder of pluginFolders) {
//           const fullPath = path.join(baseDir, folder);
//           try {
//             fs.rmSync(fullPath, { recursive: true, force: true });
//             console.log(`[Plugin Cleanup] Removed: ${fullPath}`);
//           } catch (err) {
//             console.warn(`[Plugin Cleanup] Failed to remove ${fullPath}: ${(err as Error).message}`);
//           }
//         }
//       }

//       console.log(`[Plugin Install] Installing from: ${filePath}`);
//       exec(installCmd, (installErr, stdout, stderr) => {
//         if (installErr) {
//           const errorOutput = stderr || installErr.message || 'Unknown error';
//           if (/restart|already running/i.test(errorOutput)) {
//             return resolve({
//               success: false,
//               error: 'Please close all IDE windows and try again.',
//             });
//           }

//           return resolve({
//             success: false,
//             error: `Plugin install failed: ${errorOutput}`,
//           });
//         }

//         console.log(`[Plugin Install] Success:\n${stdout}`);
//         return resolve({ success: true });
//       });
//     });
//   });
// }

