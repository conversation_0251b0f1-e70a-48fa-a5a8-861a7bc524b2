import http from 'http';
import https from 'https';
import fs from 'fs';
import path from 'path';
import os from 'os';

import log from "electron-log";

export const DOWNLOAD_DIR = path.join(os.tmpdir(), 'alpine-plugins');

export async function downloadPluginFile(
  url: string
): Promise<{ success: boolean; path?: string; error?: string }> {
  try {
    if (!url || (!url.startsWith('http://') && !url.startsWith('https://'))) {
      return { success: false, error: 'Invalid download URL' };
    }

    log.info("Downloading plugin from: ", url);

    if (!fs.existsSync(DOWNLOAD_DIR)) {
      fs.mkdirSync(DOWNLOAD_DIR, { recursive: true });
    }

    const filename = path.basename(new URL(url).pathname);
    const outputPath = path.join(DOWNLOAD_DIR, filename);

    log.info("Downloading plugin to: ", outputPath);
    log.info("Download dir: ", DOWNLOAD_DIR);
    log.info("File name: ", filename);

    // If already downloaded, skip
    if (fs.existsSync(outputPath)) {
      return { success: true, path: outputPath };
    }

    const file = fs.createWriteStream(outputPath);
    const client = url.startsWith('https') ? https : http;

    log.info("Downloading plugin...", client);

    await new Promise<void>((resolve, reject) => {
        const req = client.get(url, (response) => {
            if (response.statusCode !== 200) {
              log.error("Download failed. Status: ", response);
              reject(new Error(`Failed to download plugin. Status: ${response.statusCode}`));
              return;
            }

            response.pipe(file);

            file.on('finish', () => {
              file.close((err) => {
                  if (err) reject(err);
                  else resolve();
              });
            });
        });

        req.on('error', (err) => {
            if (fs.existsSync(outputPath)) fs.unlinkSync(outputPath);
            log.error("Download error: ", err);
            reject(err);
        });

        req.setTimeout(10000, () => {
            req.destroy(new Error('Download timed out'));
        });
    });


    return { success: true, path: outputPath };
  } catch (err) {
    return { success: false, error: `Download failed: ${String(err)}` };
  }
}
