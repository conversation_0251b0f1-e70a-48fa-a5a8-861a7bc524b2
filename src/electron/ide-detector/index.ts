import { ideRegistry, IDEName } from '../ide-detector/ideRegistry.js';
import fs from 'fs';
import os from 'os';
import { resolveIDEExecutable } from './resolveExecutable.js';
import { spawnSync } from 'child_process';
import path from 'path';



/**
 * Check if IDE is Installed
 * Is VS Code installed
 */

function isIDEInstalled(ide: IDEName): boolean {
  const platform = os.platform() as 'darwin' | 'win32' | 'linux';
  const idePaths = ideRegistry[ide].paths[platform];
  return idePaths.some(p => fs.existsSync(p));
}

// Usage
console.log('VS Code Installed:', isIDEInstalled('vscode'));


/**
 * Get Executable Path
 * Resolve VS Code CLI path
 */

const vscodePath = resolveIDEExecutable('vscode');
console.log('VS Code CLI path:', vscodePath);



/**
 * Get IDE Version
 * Spawn executable to get version
 */

function getIDEVersion(ide: IDEName): string | null {
  const execPath = resolveIDEExecutable(ide);
  if (!execPath) return null;

  try {
    const result = spawnSync(execPath.path as any, ['--version'], { encoding: 'utf-8' });
    return result.stdout.trim();
  } catch {
    return null;
  }
}

console.log('VS Code Version:', getIDEVersion('vscode'));


/**
 * Check if Plugin is Installed
 * Check thealpinecode.alpineintellect plugin for VS Code
 */
function isPluginInstalled(ide: 'vscode' | 'cursor', pluginId: string): boolean {
  const baseDir = ide === 'vscode'
    ? path.join(os.homedir(), '.vscode', 'extensions')
    : path.join(os.homedir(), '.cursor', 'extensions');

  if (!fs.existsSync(baseDir)) return false;

  const pluginDirs = fs.readdirSync(baseDir);
  return pluginDirs.some(dir => dir.startsWith(pluginId));
}

// Usage
console.log('Plugin Installed:', isPluginInstalled('vscode', 'thealpinecode.alpineintellect'));
