
import os from 'os';
import fs from 'fs';
import { ideRegistry } from './ideRegistry.js';

export type IDEName = 'vscode' | 'cursor' | 'intellij' | 'pycharm';

export interface IDEDefinition {
  label: string;
  paths: {
    darwin: string[];
    win32: string[];
    linux: string[];
  };
  executable:{
    darwin: string;
    win32: string;
    linux: string;
  }
}

export interface IDEStatus {
  ide: IDEName;
  label: string;
  installed: boolean;
}

const typedRegistry = ideRegistry as Record<IDEName, IDEDefinition>;


function checkAnyPathExists(paths: string[]): boolean {
  return paths.some(p => fs.existsSync(p));
}

export function detectInstalledIDEs(selected?: IDEName[]): Record<IDEName, boolean> {
  const platform = os.platform() as 'darwin' | 'win32' | 'linux';

  const result: Record<IDEName, boolean> = {} as any;

  const ideKeys = selected ?? (Object.keys(typedRegistry) as IDEName[]);

  for (const key of ideKeys) {
    const ide = typedRegistry[key];
    const paths = ide.paths[platform];
    result[key] = checkAnyPathExists(paths);
  }
  return result;
}

export function getIDELabel(id: IDEName): string {
  return typedRegistry[id]?.label ?? id;
}

export function getIDEStatusList(): IDEStatus[] {
  const platform = os.platform() as 'darwin' | 'win32' | 'linux';

  return (Object.keys(ideRegistry) as IDEName[]).map((ide) => {
    const label = typedRegistry[ide].label;
    const paths = typedRegistry[ide].paths[platform];
    const installed = paths.some(p => fs.existsSync(p));

    return {
      ide,
      label,
      installed,
    };
  });
}


