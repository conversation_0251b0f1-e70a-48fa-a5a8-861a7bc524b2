import os from 'os';
import fs from 'fs';
import { spawnSync } from 'child_process';
import { ideRegistry, IDEName } from '../ide-detector/ideRegistry.js';

export type ExecutableResolution =
  | { path: string; source: 'cli' | 'registry' | 'electronFallback'; reason: string }
  | { path: null; source: 'prompt'; reason: string };

function resolveFromSystem(cliName: string): string | null {
  const platform = process.platform;
  const command = platform === 'win32' ? 'where' : 'which';

  const result = spawnSync(command, [cliName], { encoding: 'utf-8' });

  if (result.status === 0 && result.stdout) {
    const firstPath = result.stdout.trim().split('\n')[0];
    if (fs.existsSync(firstPath)) return firstPath;
  }

  return null;
}

export function resolveIDEExecutable(ide: IDEName): ExecutableResolution {
  const platform = os.platform() as 'darwin' | 'win32' | 'linux';
  const def = ideRegistry[ide];

  // 1. CLI installed and available in PATH (preferred and safest)
  const cliPath = resolveFromSystem(def.cliName);
  if (cliPath) {
    return {
      path: cliPath,
      source: 'cli',
      reason: `Resolved via PATH using '${def.cliName}'`,
    };
  }

  // 2. Use static fallback executable path from registry
  // const exec = def.executable[platform];
  // let registryPath: string | null = null;
  // if (typeof exec === 'string') registryPath = exec;
  // if (typeof exec === 'function') registryPath = exec(def.paths[platform]);

  // if (registryPath && fs.existsSync(registryPath) && !registryPath.includes('/app/bin/')) {
  //   return {
  //     path: registryPath,
  //     source: 'registry',
  //     reason: `Resolved from static registry path`,
  //   };
  // }
  const exec = def.executable[platform];
  let registryPath: string | null = null;
  if (typeof exec === 'string') registryPath = exec;
  if (typeof exec === 'function') registryPath = exec(def.paths[platform]);

  if (registryPath && fs.existsSync(registryPath)) {
    return {
      path: registryPath,
      source: 'registry',
      reason: `Resolved via static registry path`,
    };
  }

  // 3. Electron fallback (e.g., Mac/Linux internal binaries)
  // if (def.getElectronFallback) {
  //   const fallback = def.getElectronFallback();
  //   if (fallback) {
  //     if (fs.existsSync(fallback.electron) && fs.existsSync(fallback.cliJs)) {
  //       return {
  //         path: `"${fallback.electron}" "${fallback.cliJs}"`,
  //         source: 'electronFallback',
  //         reason: `Resolved via Electron fallback (internal VS Code JS CLI)`,
  //       };
  //     }
  //   }
  // }

  // 4. Nothing found → prompt user to install CLI
  return {
    path: null,
    source: 'prompt',
    reason: `No executable found for ${ide}. Please install '${def.cliName}' in your PATH.\nIn ${def.label}, run "Shell Command: Install '${def.cliName}' in PATH" from the command palette.`,
  };
}



// import os from 'os';
// import fs from 'fs';
// import { execSync } from 'child_process';
// import { ideRegistry, IDEName } from '../ide-detector/ideRegistry.js';

// export type ExecutableResolution =
//   | { path: string; source: 'cli' | 'registry' | 'electronFallback'; reason: string }
//   | { path: null; source: 'prompt'; reason: string };

// function resolveFromSystem(cliName: string): string | null {
//   try {
//     const cmd = process.platform === 'win32' ? `where ${cliName}` : `which ${cliName}`;
//     const resolved = execSync(cmd, { encoding: 'utf-8' }).trim().split('\n')[0];
//     return fs.existsSync(resolved) ? resolved : null;
//   } catch {
//     return null;
//   }
// }

// export function resolveIDEExecutable(ide: IDEName): ExecutableResolution {
//   const platform = os.platform() as 'darwin' | 'win32' | 'linux';
//   const def = ideRegistry[ide];

//   // 1. CLI available via system PATH
//   const cliPath = resolveFromSystem(def.cliName);
//   if (cliPath) {
//     return { path: cliPath, source: 'cli', reason:'' };
//   }

//   // 2. Full path defined in registry (but not shell wrapper like /app/bin/code)
//   const exec = def.executable[platform];
//   let registryPath: string | null = null;
//   if (typeof exec === 'string') registryPath = exec;
//   if (typeof exec === 'function') registryPath = exec(def.paths[platform]);

//   if (registryPath && fs.existsSync(registryPath) && !registryPath.includes('/app/bin/')) {
//     return { path: registryPath, source: 'registry', reason:'' };
//   }

//   // 3. Electron fallback
//   if (def.getElectronFallback) {
//     const fallback = def.getElectronFallback();
//     if (fallback) {
//       return {
//         path: `"${fallback.electron}" "${fallback.cliJs}"`,
//         source: 'electronFallback',
//         reason:''
//       };
//     }
//   }

//   // 4.  Nothing found
//   return {
//     path: null,
//     source: 'prompt',
//     reason: `No CLI found for ${ide}. Please install '${def.cliName}' in your system PATH.\nOpen your IDE → Run 'Shell Command: Install "${def.cliName}" in PATH' from command palette.`,
//   };
// }




// /**
//  * 
//  * 

// import { resolveIDEExecutable } from './resolveExecutable';

// const pathToVSCode = resolveIDEExecutable('vscode');
// console.log('VS Code Executable Path:', pathToVSCode);

// const pathToCursor = resolveIDEExecutable('cursor');
// console.log('Cursor Executable Path:', pathToCursor);

//  * 
//  * 
//  * 
//  * 
//  */