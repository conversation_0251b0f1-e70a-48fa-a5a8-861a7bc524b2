import path from 'path';
import os from 'os';
import fs from 'fs';
import which from 'which';

function getNodePath(): string {
  try {
    return which.sync('node'); // Safest when node is installed globally
  } catch {
    return process.execPath; // Fallback to the node path that runs Electron itself
  }
}

export type Platform = 'darwin' | 'win32' | 'linux';
export type IDEName = 'vscode' | 'cursor';

export interface IDEDefinition {
  label: string;
  cliName: string;
  paths: Record<Platform, string[]>;
  executable: Record<Platform, string | ((rootDirs: string[]) => string | null)>;
  pluginId: string;
  pluginPath: Record<Platform, string>;
  getElectronFallback?: () => { electron: string; cliJs: string } | null;
}

export const ideRegistry: Record<IDEName, IDEDefinition> = {
  vscode: {
    label: 'VS Code',
    cliName: 'code',
    paths: {
      darwin: ['/Applications/Visual Studio Code.app'],
      win32: [
        path.join(process.env.LOCALAPPDATA || '', 'Programs', 'Microsoft VS Code'),
        path.join(process.env.ProgramFiles || '', 'Microsoft VS Code'),
      ],
      linux: ['/usr/bin/code', '/snap/bin/code', '/opt/code/bin'],
    },
    executable: {
      darwin: '/Applications/Visual Studio Code.app/Contents/Resources/app/bin/code',
      win32: (dirs) => findExecutableWin(dirs, 'code.cmd'),
      linux: (dirs) => findExecutableUnix(dirs, 'code'),
    },
    pluginId: 'thealpinecode.alpineintellect',
    pluginPath: {
      darwin: path.join(os.homedir(), '.vscode', 'extensions'),
      win32: path.join(os.homedir(), '.vscode', 'extensions'),
      linux: path.join(os.homedir(), '.vscode', 'extensions'),
    },
    // getElectronFallback: () => {
    //   const platform = os.platform();
    //   const mac = {
    //     electron: '/Applications/Visual Studio Code.app/Contents/MacOS/Electron',
    //     cliJs: '/Applications/Visual Studio Code.app/Contents/Resources/app/out/cli.js',
    //   };
    //   const linux = {
    //     electron: '/usr/share/code/code',
    //     cliJs: '/usr/share/code/resources/app/out/cli.js',
    //   };
    //   const fallback = platform === 'darwin' ? mac : platform === 'linux' ? linux : null;
    //   if (fallback && fs.existsSync(fallback.electron) && fs.existsSync(fallback.cliJs)) {
    //     return fallback;
    //   }
    //   return null;
    // },
  },

  cursor: {
    label: 'Cursor',
    cliName: 'cursor',
    paths: {
      darwin: ['/Applications/Cursor.app'],
      win32: [path.join(process.env.LOCALAPPDATA || '', 'Programs', 'Cursor')],
      linux: ['/opt/cursor', '/usr/bin/cursor'],
    },
    executable: {
      darwin: '/Applications/Cursor.app/Contents/Resources/app/bin/cursor',
      win32: (dirs) => findExecutableWin(dirs, 'cursor.cmd'),
      linux: (dirs) => findExecutableUnix(dirs, 'cursor'),
    },
    pluginId: 'thealpinecode.alpineintellect',
    pluginPath: {
      darwin: path.join(os.homedir(), '.cursor', 'extensions'),
      win32: path.join(os.homedir(), '.cursor', 'extensions'),
      linux: path.join(os.homedir(), '.cursor', 'extensions'),
    },
    // getElectronFallback: () => {
    //   const platform = os.platform();
    //   const mac = {
    //     electron: '/Applications/Cursor.app/Contents/MacOS/Electron',
    //     cliJs: '/Applications/Cursor.app/Contents/Resources/app/out/cli.js',
    //   };
    //   const linux = {
    //     electron: '/opt/cursor/cursor', // Needs to be validated on real systems
    //     cliJs: '/opt/cursor/resources/app/out/cli.js',
    //   };
    //   const fallback = platform === 'darwin' ? mac : platform === 'linux' ? linux : null;
    //   if (fallback && fs.existsSync(fallback.electron) && fs.existsSync(fallback.cliJs)) {
    //     return fallback;
    //   }
    //   return null;
    // },
    // getElectronFallback: () => {
    //   const platform = os.platform();
    //   const nodePath = getNodePath();

    //   if (!nodePath) return null;

    //   const cliJs =
    //     platform === 'darwin'
    //       ? '/Applications/Cursor.app/Contents/Resources/app/out/cli.js'
    //       : platform === 'linux'
    //       ? '/opt/cursor/resources/app/out/cli.js'
    //       : 'C:\\Program Files\\Cursor\\resources\\app\\out\\cli.js';

    //   if (fs.existsSync(cliJs)) {
    //     return { electron: nodePath, cliJs };
    //   }

    //   return null;
    // },
  },

  // Extend for IntelliJ, PyCharm similarly...
};

function findExecutableWin(possibleRootDirs: string[], binaryName: string): string | null {
  for (const dir of possibleRootDirs) {
    const fullPath = path.join(dir, 'bin', binaryName);
    if (fs.existsSync(fullPath)) return fullPath;
  }
  return null;
}

function findExecutableUnix(possiblePaths: string[], binaryName: string): string | null {
  for (const dir of possiblePaths) {
    const fullPath = path.join(dir, binaryName);
    if (fs.existsSync(fullPath)) return fullPath;
  }
  return null;
}
