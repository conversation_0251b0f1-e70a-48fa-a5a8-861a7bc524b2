import fs from 'fs';
import os from 'os';
import path from 'path';
import semver from 'semver';
import { formatDistanceToNow } from 'date-fns';
import { IDEName, ideRegistry } from '../ide-detector/ideRegistry.js';
import { resolveIDEExecutable } from './resolveExecutable.js';
import { spawnSync } from 'child_process';
import log from "electron-log";

// Determine base plugin install path
function getPluginBasePath(ide: IDEName): string | null {
  const platform = os.platform() as 'darwin' | 'win32' | 'linux';
  const ideDef = ideRegistry[ide];

  return ideDef.pluginPath?.[platform] ?? null;
}

// Check if plugin is installed
export function isPluginInstalled(ide: IDEName, pluginId?: string): boolean {
  const pluginKey = pluginId || ideRegistry[ide].pluginId;
  log.info("plugin key", pluginKey);
  const basePath = getPluginBasePath(ide);
  log.info("base path", basePath);
  if (!basePath || !fs.existsSync(basePath)) return false;

  return isPluginInstalledCheckViaExecPath(ide, pluginKey) && fs.readdirSync(basePath).some(f => f.startsWith(pluginKey));
}




// export function isPluginInstalledCheckViaExecPath(
//   ide: IDEName,
//   pluginId: string
// ): boolean {
//   const resolved = resolveIDEExecutable(ide);
//   log.info("resolved", resolved);

//   if (!resolved.path) {
//     console.warn(`Could not resolve CLI for ${ide}. Reason: ${resolved.reason}`);
//     return false;
//   }

//   try {
//     const isElectronFallback = resolved.source === 'electronFallback';
//     log.info("is electron fallback", isElectronFallback);

//     const parts = resolved.path.split(' ').filter(Boolean);
//     const command = parts.shift()!;
//     const args = [...parts, '--list-extensions'];

//     const result = spawnSync(command, args, {
//       encoding: 'utf-8',
//       env: {
//         ...process.env,
//         ...(isElectronFallback ? { ELECTRON_RUN_AS_NODE: '1' } : {}),
//       },
//     });
//     // const result = spawnSync(resolved.path, ['--list-extensions'], {
//     //   encoding: 'utf-8',
//     // });

//     if (result.error) {
//       console.warn(`spawnSync failed for ${ide}: ${result.error.message}`);
//       return false;
//     }

//     if (result.status !== 0) {
//       console.warn(`Non-zero exit code (${result.status}) for ${ide}`);
//       console.warn(`stderr: ${result.stderr}`);
//       return false;
//     }

//     const stdout = result.stdout.trim();
//     const plugins = stdout
//       .split('\n')
//       .map(line => line.trim().toLowerCase())
//       .filter(Boolean);
//     return plugins.includes(pluginId.toLowerCase());
//   } catch (err: any) {
//     console.warn(`Plugin check threw for ${ide}: ${err.message}`);
//     return false;
//   }
// }


export function isPluginInstalledCheckViaExecPath(
  ide: IDEName,
  pluginId: string
): boolean {
  const resolved = resolveIDEExecutable(ide);
  log.info("resolved", resolved);

  if (!resolved.path) {
    console.warn(`Could not resolve CLI for ${ide}. Reason: ${resolved.reason}`);
    return false;
  }

  try {
    const isElectronFallback = resolved.source === 'electronFallback';
    log.info("is electron fallback", isElectronFallback);

    const executablePath = resolved.path.replace(/"/g, '').trim();
    log.info("Resolved exec path:", executablePath);

    if (!fs.existsSync(executablePath)) {
      console.warn(`Executable not found at path: ${executablePath}`);
      return false;
    }

    const result = spawnSync(executablePath, ['--list-extensions'], {
      encoding: 'utf-8',
      env: {
        ...process.env,
        ...(isElectronFallback ? { ELECTRON_RUN_AS_NODE: '1' } : {}),
      },
    });

    if (result.error) {
      console.warn(`spawnSync failed for ${ide}: ${result.error.message}`);
      return false;
    }

    if (result.status !== 0) {
      console.warn(`Non-zero exit code (${result.status}) for ${ide}`);
      console.warn(`stderr: ${result.stderr}`);
      return false;
    }

    const stdout = result.stdout.trim();
    const plugins = stdout
      .split('\n')
      .map(line => line.trim().toLowerCase())
      .filter(Boolean);

    return plugins.includes(pluginId.toLowerCase());
  } catch (err: any) {
    console.warn(`Plugin check threw for ${ide}: ${err.message}`);
    return false;
  }
}


// export function getInstalledPluginVersion(ide: IDEName, pluginId?: string): string | null {
//   const pluginKey = pluginId || ideRegistry[ide].pluginId;

//   const resolved = resolveIDEExecutable(ide);
//   if (resolved.path) {
//     try {
//       const parts = resolved.path.split(' ').filter(Boolean);
//       const command = parts.shift()!;
//       const args = [...parts, '--list-extensions'];

//       const result = spawnSync(command, args, { encoding: 'utf-8' });
//       if (result.status === 0 && result.stdout.includes(pluginKey)) {
//         // Plugin is actually installed according to the CLI
//         return getVersionFromPluginFolder(ide, pluginKey); // You can keep this logic to fetch version
//       } else {
//         return null; // CLI says it's not active, ignore stale folders
//       }
//     } catch (e) {
//       console.warn(`Plugin check via CLI failed for ${ide}`, e);
//     }
//   }

//   // Fallback if CLI fails (e.g., missing CLI, spawn error)
//   return getVersionFromPluginFolder(ide, pluginKey);
// }


export function getInstalledPluginVersion(
  ide: IDEName,
  pluginId?: string
): string | null {
  const pluginKey = pluginId || ideRegistry[ide].pluginId;

  const resolved = resolveIDEExecutable(ide);
  if (resolved.path) {
    try {
      const executablePath = resolved.path.replace(/"/g, '').trim();

      if (!fs.existsSync(executablePath)) {
        console.warn(`Executable not found at path: ${executablePath}`);
        return getVersionFromPluginFolder(ide, pluginKey); // fallback
      }

      const result = spawnSync(executablePath, ['--list-extensions'], {
        encoding: 'utf-8',
      });

      if (result.status === 0 && result.stdout.includes(pluginKey)) {
        // Plugin is actually installed according to the CLI
        return getVersionFromPluginFolder(ide, pluginKey);
      } else {
        return null; // CLI says it's not active, ignore stale folders
      }
    } catch (e) {
      console.warn(`Plugin check via CLI failed for ${ide}`, e);
    }
  }

  // Fallback if CLI fails (e.g., missing CLI, spawn error)
  return getVersionFromPluginFolder(ide, pluginKey);
}


function getVersionFromPluginFolder(ide: IDEName, pluginKey: string): string | null {
  const basePath = getPluginBasePath(ide);
  if (!basePath || !fs.existsSync(basePath)) return null;

  const folders = fs.readdirSync(basePath);

  const versions = folders
    .filter(f => f.startsWith(`${pluginKey}-`))
    .map(f => {
      const match = f.match(new RegExp(`${pluginKey}-(\\d+\\.\\d+\\.\\d+)`));
      return match ? match[1] : null;
    })
    .filter((v): v is string => v !== null);

  if (versions.length === 0) return null;

  return versions.sort(semver.rcompare)[0]; // Latest version
}

// Get human-readable last modified time of plugin folder
export function getPluginLastUpdated(ide: IDEName, pluginId?: string): string | null {
  const pluginKey = pluginId || ideRegistry[ide].pluginId;;
  const basePath = getPluginBasePath(ide);
  if (!basePath || !fs.existsSync(basePath)) return null;

  const folders = fs.readdirSync(basePath)
    .filter(f => f.startsWith(pluginKey))
    .map(f => ({
      folder: f,
      mtime: fs.statSync(path.join(basePath, f)).mtime
    }))
    .sort((a, b) => b.mtime.getTime() - a.mtime.getTime());

  if (folders.length === 0) return null;

  return formatDistanceToNow(folders[0].mtime, { addSuffix: true });
}

// Get latest version from server
export async function getLatestPluginInfo(): Promise<{
  latestVersion: string;
  downloadUrl: string;
} | null> {
    try {
        const baseUrl =
            process.env.NODE_ENV === "production"
                ? process.env.CONSOLE_APP_URL_MAIN
                : process.env.VITE_CONSOLE_URL;
        const res = await fetch(`${baseUrl}/plugin-info.json`);
        const pluginInfo = await res.json();
        log.info("latest plugin info ", pluginInfo);
        return pluginInfo; // { latestVersion, downloadUrl }
        // const vsixPath = path.resolve(__dirname, '../dist-electron/ain-plugins/alpineintellect-0.0.2.vsix');
        // return {
        //   latestVersion: '0.0.2',
        //   downloadUrl: `file://${vsixPath}`,
        // }
    } catch (err) {
        console.error("Failed to fetch latest plugin info", err);
        log.error("Failed to fetch latest plugin info", err);
        return null;
    }
}

// Compare installed vs latest
export function isNewerVersion(current: string, latest: string): boolean {
  return semver.valid(current) && semver.valid(latest)
    ? semver.lt(current, latest)
    : false;
}

// Get full plugin status (used in IPCs or dashboards)
export async function getPluginUpdateStatus(ide: IDEName): Promise<{
  pluginStatus: 'installed' | 'not-installed' | 'needs-update' | 'unknown';
  installedVersion: string | null;
  latestVersion: string | null;
  updateAvailable: boolean;
  lastUpdated: string | null;
  downloadUrl: string | null;
}> {
  const pluginId = ideRegistry[ide].pluginId;
  const installed = getInstalledPluginVersion(ide, pluginId);
  const lastUpdated = installed ? getPluginLastUpdated(ide, pluginId) : null;

  const latestInfo = await getLatestPluginInfo();
  console.log("latest plugin info ",latestInfo);

  if (!latestInfo) {
    return {
      pluginStatus: 'unknown',
      installedVersion: installed,
      latestVersion: null,
      updateAvailable: false,
      lastUpdated,
      downloadUrl: null,
    };
  }

  const updateAvailable = installed ? isNewerVersion(installed, latestInfo.latestVersion) : false;

  return {
    pluginStatus: installed
      ? updateAvailable ? 'needs-update' : 'installed'
      : 'not-installed',
    installedVersion: installed,
    latestVersion: latestInfo.latestVersion,
    updateAvailable,
    lastUpdated,
    downloadUrl: latestInfo.downloadUrl,
  };
}


// export function getPluginInfo(
//   ide: IDEName,
//   pluginId: string
// ): {
//   pluginStatus: 'installed' | 'not-installed' | 'unknown';
//   installedVersion: string;
//   lastUpdated: string | null;
// } {
//   const execPath = resolveIDEExecutable(ide);
//   log.info("exec path", execPath);
//   // If we couldn't resolve the path, return unknown
//   if (!execPath.path || execPath.source === 'prompt' as any) {
//     return {
//       pluginStatus: 'unknown',
//       installedVersion: '-',
//       lastUpdated: null,
//     };
//   }
//   // Validate that path exists (not just string)
//   // const pathParts = execPath.path.split(' ')[0].replace(/"/g, '');
//   const pathParts = execPath.path.replace(/"/g, '').trim();
//   log.info("path parts", pathParts);
//   if (!fs.existsSync(pathParts)) {
//     return {
//       pluginStatus: 'unknown',
//       installedVersion: '-',
//       lastUpdated: null,
//     };
//   }

  

//   // --- Step 1: Check active plugin list via CLI ---
//   const pluginInstalled = isPluginInstalled(ide, pluginId);
  
//   // --- Step 2: Get version and last modified folder ---
//   const pluginInstalledVersion = getInstalledPluginVersion(ide, pluginId) ?? "-";
  
//   // --- Step 3: Get Plugin and last modified ---
//   const pluginLastUpdated = getPluginLastUpdated(ide, pluginId);

//   return {
//     pluginStatus: pluginInstalled ? 'installed' : 'not-installed',
//     installedVersion: pluginInstalledVersion,
//     lastUpdated: pluginLastUpdated
//   };
// }


export function getPluginInfo(
  ide: IDEName,
  pluginId: string
): {
  pluginStatus: 'installed' | 'not-installed' | 'unknown';
  installedVersion: string;
  lastUpdated: string | null;
} {
  const execPath = resolveIDEExecutable(ide);
  log.info("exec path", execPath);

  if (!execPath.path || execPath.source === 'prompt' as any) {
    return {
      pluginStatus: 'unknown',
      installedVersion: '-',
      lastUpdated: null,
    };
  }

  const executablePath = execPath.path.replace(/"/g, '').trim();
  log.info("Resolved exec path:", executablePath);

  if (!fs.existsSync(executablePath)) {
    return {
      pluginStatus: 'unknown',
      installedVersion: '-',
      lastUpdated: null,
    };
  }

  const pluginInstalled = isPluginInstalled(ide, pluginId);
  log.info("plugin installed", pluginInstalled);
  const pluginInstalledVersion = getInstalledPluginVersion(ide, pluginId) ?? "-";
  log.info("plugin installed version", pluginInstalledVersion);
  const pluginLastUpdated = getPluginLastUpdated(ide, pluginId);

  return {
    pluginStatus: pluginInstalled ? 'installed' : 'not-installed',
    installedVersion: pluginInstalledVersion,
    lastUpdated: pluginLastUpdated
  };
}




