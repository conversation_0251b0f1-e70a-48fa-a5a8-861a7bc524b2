type SessionSource = "desktop" | "web";

export type SessionMetadata = {
  source?: SessionSource;
  redirectUri?: string;
};

type InternalSession = SessionMetadata & {
  createdAt: number;
  expiresAt: number;
};

export class SessionManager {
  private sessions: Map<string, InternalSession> = new Map();
  private readonly ttl: number;

  constructor(ttlInMs: number = 5 * 60 * 1000) {
    this.ttl = ttlInMs;
    this.startCleanupInterval();
  }

  createSession(metadata: SessionMetadata = {}): string {
    const id = crypto.randomUUID();
    const now = Date.now();

    this.sessions.set(id, {
      ...metadata,
      createdAt: now,
      expiresAt: now + this.ttl,
    });

    return id;
  }

  validateSession(id: string): { valid: boolean; session?: InternalSession } {
    const session = this.sessions.get(id);
    if (!session) return { valid: false };

    if (Date.now() > session.expiresAt) {
      this.sessions.delete(id);
      return { valid: false };
    }

    return { valid: true, session };
  }

  deleteSession(id: string): void {
    this.sessions.delete(id);
  }

  private startCleanupInterval(): void {
    setInterval(() => {
      const now = Date.now();
      for (const [id, session] of this.sessions) {
        if (now > session.expiresAt) {
          this.sessions.delete(id);
        }
      }
    }, 5 * 60 * 1000); // clean every 5 minute
  }
}
