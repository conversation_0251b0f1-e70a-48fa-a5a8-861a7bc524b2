import { ContextLimits } from "./core/retrieval/intelligent-file-reader.js";

export const AIN_INDEX_DIR = ".ain-index";
export const SERVER_PORT_FILE = "server.json";
export const RDS_DB_FILE = "index.sqlite";
export const LANCE_DB_DIR = "lancedb";
export const DEFAULT_BRANCH_NAME = "main";

export const ignoredFolders = new Set([
  ".DS_Store",
  "coverage",
  "logs",
  ".idea",
  "cdk.out",
  ".next",
  ".keystone",
  ".git",
  "node_modules",
  "__pycache__",
  "__generated__",
  "venv",
  ".venv",
  "dist",
  "build",
  ".idea",
  ".vscode",
  ".yarn",
  ".next",
  "out",
  "release",
  "bin",
  "target",
  ".gradle",
  "gradle",
  ".expo",
  ".dart_tool",
  "Pods",
  "DerivedData",
  ".cocoapods",
  ".flutter-plugins",
  "web-build",
  "android/app/build",
  "ios/build",
  "native",
  "vendor",
  "runtime",
  "web/assets",
  "tests/_output",
  "assets",
  "protected/runtime",
  "protected/tests/runtime",
  "lancedb",
]);

export const ignoredFileExtensions = [
    ".lock",
    ".tmp",
    ".log",
    ".ds_store",
    ".svg",
    ".png",
    ".jpg",
    ".jpeg",
    ".zip",
    ".gz",
    ".gif",
    ".out",
    ".apk",
    ".ipa",
    ".aar",
    ".dsym",
    ".iml",
    ".pbxuser",
    ".mode1v3",
    ".mode2v3",
    ".xcuserstate",
    ".xcworkspace",
    ".pdf",
    ".onnx",
    ".model",
    ".bin",
    ".exe",
    ".dll",
    ".so",
    ".dylib",
    ".ico",
    ".ttf",
    ".node",
    ".bak",
    ".mwb",
    ".xlsx",
    ".dmg",
    ".min.js",
    ".mp4",
    ".fla",
    ".pod",
    ".swf",
    ".tar.gz",
    ".zip",
    ".wasm",
    ".map",
    ".otf",
    ".eot",
    ".woff",
    ".woff2",
    ".bmp",
    ".cur",
    ".class",
    '.xap',
    '.psd',
    ".db",
    ".ser",
    ".tif",

    // As of now these are ignored, need to be handled later
    ".toml"
];

export const ignoredFileNames = new Set([
  "package-lock.json",
  ".DS_Store",
  "Thumbs.db",
  "Podfile.lock",
  "yarn.lock",
  "pubspec.lock",
  "flutter_export_environment.sh",
  ".env",
  ".env.local",
  ".env.development",
  ".env.production",
]);

// const CONTEXT_LENGTH = 200000;
// const CONTEXT_UTILIZATION = 0.7;
// const TOTAL_TOKEN_BUDGET = CONTEXT_LENGTH * CONTEXT_UTILIZATION; // 140000

// const REPO_MAP_TOKEN_BUDGET = TOTAL_TOKEN_BUDGET * 0.5; // 70000

// const nFinal = 30;
// const nRetrieve = 2 * nFinal;

// export const RETRIEVAL_PARAMS = {
//     bm25Threshold: -2.5,
//     ftsNFinal: nFinal,
//     embeddingsNFinal: nRetrieve,
//     ftsPathWeight: 2,
//     ftsContentWeight: 8,
// };

// export const REPO_MAP_RETRIEVAL_OPTIONS: ContextLimits = {
//     maxTokens: REPO_MAP_TOKEN_BUDGET, // 50% of total usable context
//     maxFiles: 9, // assuming ~7500 tokens per file (~30K chars)
//     maxFileSize: 30000, // in characters, ~= 7500 tokens
// };
