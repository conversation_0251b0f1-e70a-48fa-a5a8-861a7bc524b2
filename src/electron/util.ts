import { ipcMain, WebContents, WebFrameMain } from "electron";
import { getUIPath } from "./path-resolver.js";
import { pathToFileURL } from "url";
import { dialog, BrowserWindow } from 'electron';
import path from 'path';
import log from 'electron-log'
import os from 'os';
import { formatDistanceToNow, format } from 'date-fns';
import { AIN_INDEX_DIR } from "./constants.js";
import fs from 'fs-extra';

export function isDev(): boolean {
    return process.env.NODE_ENV === "development";
}

export function ipcMainHandle<Key extends keyof EventPayloadMapping>(
    key: Key,
    handler: () => EventPayloadMapping[Key]
) {
    ipcMain.handle(key, (event,payload) => {
      log.debug(event,payload,"ipcMainHandle")
        validateEventFrame(event.senderFrame!,payload);
        return handler();
    });
}

export function ipcMainOn<Key extends keyof EventPayloadMapping>(
    key: Key,
    handler: (payload: EventPayloadMapping[Key]) => void
) {
    ipcMain.on(key, (event, payload) => {
        validateEventFrame(event.senderFrame!,payload);
        return handler(payload);
    });
}

export function ipcWebContentsSend<Key extends keyof EventPayloadMapping>(
    key: Key,
    webContents: WebContents,
    payload: EventPayloadMapping[Key]
) {
    webContents.send(key, payload);
}

// export function validateEventFrame(frame: WebFrameMain,context?:string) {
//   log.debug({context},"validateEventFrame")
//   if (context ===  "CLOSE" || context === "MAXIMIZE" || context === "MINIMIZE") {
//     return;
// }
//     if (isDev() && new URL(frame.url).host === "localhost:5132") {
//         return;
//     }
//     if (frame.url !== pathToFileURL(getUIPath()).toString()) {
//         throw new Error("Malicious event");
//     }
// }

export function validateEventFrame(frame: WebFrameMain, context?: string) {
  log.debug({ context }, "validateEventFrame");

  const allowedActions = ["CLOSE", "MAXIMIZE", "MINIMIZE"];

  // Allow trusted context actions
  if (context && allowedActions.includes(context)) {
    return;
  }

  const url = frame?.url || "";
  if (isDev() && url.includes("localhost:5132")) {
    return;
  }

  if (url === pathToFileURL(getUIPath()).toString()) {
    return;
  }

  log.warn(" Blocked IPC event from unexpected frame:", url);
  throw new Error("Malicious event");
}



export async function openFolderDialog(win: BrowserWindow): Promise<string | null> {
  const result = await dialog.showOpenDialog(win, {
    title: 'Choose a folder to give read and write permissions',
    properties: ['openDirectory'],
  });

  return result.canceled || result.filePaths.length === 0
    ? null
    : result.filePaths[0];
}


// export function readAllSourceFiles(folderPath: string) {
//   const files = fs.readdirSync(folderPath);
//   return files
//     .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
//     .map(file => ({
//       name: file,
//       content: fs.readFileSync(path.join(folderPath, file), 'utf-8')
//     }));
// }

export function readAllSourceFiles(folderPath: string): { name: string; content: string }[] {
  const result: { name: string; content: string }[] = [];

  const walk = (dirPath: string) => {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const item of items) {
      const fullPath = path.join(dirPath, item.name);

      if (item.isDirectory()) {
        walk(fullPath); // Recursive read
      } else if (item.name.endsWith('.ts') || item.name.endsWith('.js')) {
        const content = fs.readFileSync(fullPath, 'utf-8');
        result.push({ name: fullPath, content });
      }
    }
  };

  walk(folderPath);
  return result;
}

export async function loadMetaJson(path: string) {
    try {
        const content = await fs.promises.readFile(path, "utf-8");
        return JSON.parse(content);
    } catch (e) {
        return {};
    }
}

export function getIndexingMetaPath(): string {
    const metaIndexPath = path.join(os.homedir(), AIN_INDEX_DIR, "meta.json");
    return metaIndexPath;
}

export async function removeProjectFromMeta(dirKey: string): Promise<void> {
  try {
    const metaJsonPath = getIndexingMetaPath();
    const existingMeta = await loadMetaJson(metaJsonPath);

    if (existingMeta[dirKey]) {
      delete existingMeta[dirKey];

      await fs.writeFile(
        metaJsonPath,
        JSON.stringify(existingMeta, null, 2),
        "utf-8"
      );

      // const enriched = updateProjectMetadata(existingMeta);

      // const win = BrowserWindow.getAllWindows()[0];
      // if (win?.webContents) {
      //   win.webContents.send("indexing-meta", enriched);
      // }
    }
  } catch (error) {
    console.error("Failed to remove project from meta.json:", error);
    throw error;
  }
}


/**
 * Converts a timestamp to a human-readable "time ago" string.
 * 
 * @param timestamp - Date or timestamp (string or number)
 * @returns A string like "2 hours ago", "3 days ago"
 */
export function getRelativeTime(timestamp: string | number | Date): string {
  try {
    const date = new Date(timestamp);
    return formatDistanceToNow(date, { addSuffix: true });
  } catch (error) {
    console.error('Invalid date input:', timestamp);
    return 'NaN';
  }
}


/**
 * Calculates the duration between two timestamps in a human-readable format.
 * - If >= 1 hour: "1h 3m 20s"
 * - Else: "57m 30s"
 * 
 * @param start - Start timestamp (Date | number | string)
 * @param end - End timestamp (Date | number | string)
 * @returns Duration string
 */
export function getIndexingDuration(start: Date | number | string, end: Date | number | string): string {
  const startTime = new Date(start).getTime();
  const endTime = new Date(end).getTime();

  if (isNaN(startTime) || isNaN(endTime)) {
    return 'NaN';
  }

  const diffInMs = Math.max(0, endTime - startTime);
  const totalSeconds = Math.floor(diffInMs / 1000);

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  if (hours > 0) {
    return `${hours}h ${minutes}m ${seconds}s`;
  } else {
    return `${minutes}m ${seconds}s`;
  }
}


export function updateProjectMetadata(mockProjects: Record<string, any>): Record<string, any> {
  const updatedProjects: typeof mockProjects = {};
  for (const path in mockProjects) {
    const project = mockProjects[path];

    const {
      indexingStartedAt,
      indexingFinishedAt,
      status,
    } = project;

    // lastIndexed
    const lastIndexed =
      status === 'indexing'
        ? 'Currently indexing...'
        : indexingFinishedAt
        ? getRelativeTime(indexingFinishedAt)
        : 'Not available';

    // indexingDuration
    const indexingDuration =
      indexingStartedAt && indexingFinishedAt
        ? getIndexingDuration(indexingStartedAt, indexingFinishedAt)
        : 'In progress';

    // color
    let color = 'gray';
    if (status === 'done') color = 'green';
    else if (status === 'indexing') color = 'yellow';
    else if (status === 'paused') color = 'slate';

    // createdAt from indexingStartedAt
    const createdAt = indexingStartedAt
      ? format(new Date(indexingStartedAt), 'yyyy-MM-dd')
      : undefined;

    updatedProjects[path] = {
      ...project,
      lastIndexed,
      indexingDuration,
      color,
      ...(createdAt && { createdAt }), // only add if available
    };
  }

  return updatedProjects;
}






