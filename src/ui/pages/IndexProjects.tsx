
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Layout } from "@/components/Layout";
import { 
  FolderOpen, 
  Clock, 
  Timer,
  Search, 
  Filter, 
  Folder, 
  FileText, 
  HardDrive, 
  RefreshCw, 
  Pause, 
  Play, 
  Trash2,
  MoreVertical,
  X
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  Di<PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useBufferedIndexingUpdates } from "@/hooks/useBufferedIndexingUpdates";
import { toast } from "sonner";


type ProjectMeta = {
  name: string;
  color?: string;
  createdAt: string;
  progress: number;
  status: "done" | "indexing" | "paused" | string;
  lastIndexed: string;
  techStack: string[];
  totalSize: number;
  filesCount: number;
  indexingStartedAt: number;
  indexingFinishedAt: number;
  indexingDuration: string;
};



const IndexProjects = () => {
  const [projects, setProjects] = useState<Record<string, ProjectMeta>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showSearch, setShowSearch] = useState(false);
  const [statusFilter, setStatusFilter] = useState("all");
  const [showFilter, setShowFilter] = useState(false);
  


  useBufferedIndexingUpdates(setProjects);
  function isValidProjectMeta(obj: any): obj is Partial<ProjectMeta> {
    return obj && typeof obj === "object" && "status" in obj;
  }

  // Simulate loading initial data
  useEffect(() => {
    const fetchExistingMeta = async () => {
      try {
        const existingMeta = await window.api?.loadExistingMeta?.() as Record<string, Partial<ProjectMeta>>;
        if (existingMeta) {
          setProjects(prev => {
            const updated = { ...prev };
            for (const [dirKey, meta] of Object.entries(existingMeta)) {
              if (!isValidProjectMeta(meta)) continue;
              updated[dirKey] = {
                name: meta.name ?? dirKey.split("/").pop() ?? "Unknown",
                color: meta.color,
                createdAt: new Date().toISOString(),
                progress: meta.progress ? Math.round(meta.progress * 100) : 0,
                status: meta.status ?? "indexing",
                lastIndexed: meta.lastIndexed || "N/A",
                techStack: meta.techStack ?? [],
                totalSize: meta.totalSize ?? 0,
                filesCount: meta.filesCount ?? 0,
                indexingStartedAt: meta.indexingStartedAt ?? Date.now(),
                indexingFinishedAt: meta.indexingFinishedAt ?? Date.now(),
                indexingDuration: meta.indexingDuration ?? "In Progress"
              };
            }
            return updated;
          });
        }
      } catch (err) {
        console.error("Failed to load initial meta", err);
      }
    };
    const loadProjects = async () => {
      setIsLoading(true);
      await fetchExistingMeta();
      await new Promise(resolve => setTimeout(resolve, 500));
      setIsLoading(false);
    };

    loadProjects();
  }, []);
  
  const projectPaths = Object.keys(projects);
  const hasProjects = projectPaths.length > 0;

  // Convert projects object to array for easier filtering
  const projectsArray = projectPaths.map(path => ({
    path,
    ...projects[path]
  }));

  // Filter projects based on search term and status
  const filteredProjects = projectsArray.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.techStack.join(", ").toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || project.status.toLowerCase() === statusFilter.toLowerCase();
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "done":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      case "indexing":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "paused":
        return "bg-slate-500/20 text-slate-400 border-slate-500/30";
      default:
        return "bg-slate-500/20 text-slate-400 border-slate-500/30";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "done":
        return "Active";
      case "indexing":
        return "Indexing";
      case "paused":
        return "Paused";
      default:
        return status;
    }
  };

  const getProgressColor = (state: string): string => {
    switch (state) {
      case "done":
        return "bg-green-500";
      case "indexing":
        return "bg-yellow-500";
      case "paused":
        return "bg-slate-500";
      default:
        return "bg-blue-500"; // fallback or initial state
    }
  };


  const getProgressColorSet = (state: string): { bg: string; text: string } => {
    switch (state) {
      case "done":
        return { bg: "bg-green-500/20", text: "text-green-500" };
      case "indexing":
        return { bg: "bg-yellow-500/20", text: "text-yellow-500" };
      case "paused":
        return { bg: "bg-slate-500/20", text: "text-slate-500" };
      default:
        return { bg: "bg-blue-500/20", text: "text-blue-500" }; // fallback
    }
  };


  // const handleReindex = (projectPath: string) => {
  //   setProjects(prev => ({
  //     ...prev,
  //     [projectPath]: {
  //       ...prev[projectPath],
  //       status: "indexing",
  //       progress: 0,
  //       lastIndexed: "Just started",
  //       timestamp: Date.now()
  //     }
  //   }));
  //   console.log(`Reindexing project ${projectPath}`);
  // };


  const handleReindex = async (projectPath: string) => {
    try {
      const result = await window.api.reindex(projectPath);
      if (result.success) {
        toast.success("Reindexed successfully!");
        console.log(`Reindex triggered for ${projectPath}`);
        // setProjects(prev => ({
        //   ...prev,
        //   [projectPath]: {
        //     ...prev[projectPath],
        //     status: "indexing",
        //     progress: 0,
        //     lastIndexed: "Just started",
        //     timestamp: Date.now(),
        //   }
        // }));
      } else {
        console.error(`Reindex failed: ${result.error}`);
      }
    } catch (err) {
      console.error("Reindex error:", err);
    }
  };

  // const handlePauseResume = (projectPath: string) => {
  //   setProjects(prev => {
  //     const project = prev[projectPath];
  //     let newStatus = project.status;
  //     let newLastIndexed = project.lastIndexed;

  //     if (project.status === "indexing") {
  //       newStatus = "paused";
  //       newLastIndexed = "Just paused";
  //     } else if (project.status === "paused") {
  //       newStatus = "indexing";
  //       newLastIndexed = "Just resumed";
  //     }

  //     return {
  //       ...prev,
  //       [projectPath]: {
  //         ...project,
  //         status: newStatus,
  //         lastIndexed: newLastIndexed,
  //         timestamp: Date.now()
  //       }
  //     };
  //   });
  // };

  const handlePauseResume = async (projectPath: string) => {
    const project = projects[projectPath];

    if (project.status === "indexing") {
      // Pause indexing
      const result = await window.api.pauseIndexing(projectPath, project.progress/100);
      if (result.success) {
        setProjects(prev => ({
          ...prev,
          [projectPath]: {
            ...prev[projectPath],
            status: "paused",
            lastIndexed: "Just paused",
          }
        }));
      } else {
        console.error("Pause failed:", result.error);
      }
    } else if (project.status === "paused") {
      // Resume indexing
      const result = await window.api.resumeIndexing(projectPath);
      if (result.success) {
        setProjects(prev => ({
          ...prev,
          [projectPath]: {
            ...prev[projectPath],
            status: "indexing",
            lastIndexed: "Just resumed",
          }
        }));
      } else {
        console.error("Resume failed:", result.error);
      }
    }
  };


  const handleRemoveProject = async (projectPath: string) => {
    try {
      const result = await window.api.removeIndex(projectPath);
      if (result.success) {
        setProjects(prev => {
          const newProjects = { ...prev };
          delete newProjects[projectPath];
          return newProjects;
        });
        toast.success(`Index removed successfully!`);
      } else {
        console.error(` Failed to remove index: ${result.error}`);
      }
    } catch (err) {
      console.error(`Unexpected error while removing index:`, err);
    }
  };

  const clearSearch = () => {
    setSearchTerm("");
    setShowSearch(false);
  };

  const clearFilter = () => {
    setStatusFilter("all");
    setShowFilter(false);
  };


  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="p-6 space-y-6">
      {/* Header Skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-9 w-48" />
        <div className="flex space-x-3">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>

      {/* Stats Cards Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="bg-slate-800 border-slate-700">
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-20" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-5 w-12" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Project List Skeleton */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <Skeleton className="h-6 w-40" />
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="p-6 bg-slate-700/50 rounded-lg border border-slate-600/30">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <Skeleton className="w-12 h-12 rounded-lg" />
                    <div>
                      <Skeleton className="h-5 w-48 mb-2" />
                      <Skeleton className="h-4 w-32 mb-1" />
                      <Skeleton className="h-3 w-64" />
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-12" />
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  {[1, 2, 3, 4].map((j) => (
                    <Skeleton key={j} className="h-4 w-24" />
                  ))}
                </div>
                
                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-12" />
                  </div>
                  <Skeleton className="h-2 w-full rounded-full" />
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-slate-600/50">
                  <div className="flex space-x-2">
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-8 w-20" />
                  </div>
                  <Skeleton className="h-8 w-16" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  // Show loading skeleton while data is being fetched
  if (isLoading) {
    return (
      <Layout>
        <LoadingSkeleton />
      </Layout>
    );
  }

  if (!hasProjects) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <Card className="bg-slate-800 border-slate-700 max-w-lg w-full mx-4">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-16 h-16 bg-slate-600/20 rounded-full flex items-center justify-center mb-4">
                <Folder className="w-8 h-8 text-slate-400" />
              </div>
              <CardTitle className="text-white text-xl font-titling">No Indexed Projects</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-slate-400 text-sm leading-relaxed font-body">
                You haven't indexed any projects yet. Start by adding your first project 
                to begin organizing and tracking your codebase.
              </p>
              <div className="flex items-center justify-center space-x-2 text-slate-500 text-xs font-body">
                <FileText className="w-4 h-4" />
                <span>Projects will appear here once indexed</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="p-6 space-y-6">

        {/* Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-white font-titling">Project Index</h1>
          <div className="flex space-x-3">
            {/* Search Section */}
            <div className="flex items-center space-x-2">
              {showSearch ? (
                <div className="flex items-center space-x-2 bg-slate-800 border border-slate-600 rounded-md px-3 py-2">
                  <Search className="w-4 h-4 text-slate-400" />
                  <Input
                    placeholder="Search projects..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="bg-transparent border-none text-slate-300 placeholder-slate-500 focus:ring-0 w-48 font-body"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearSearch}
                    className="text-slate-400 hover:text-white p-1 h-auto"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ) : (
                <Button 
                  variant="outline" 
                  className="text-slate-300 border-slate-600 font-body"
                  onClick={() => setShowSearch(true)}
                >
                  <Search className="w-4 h-4 mr-2" />
                  Search
                </Button>
              )}
            </div>

            {/* Filter Section */}
            <div className="flex items-center space-x-2">
              {showFilter ? (
                <div className="flex items-center space-x-2 bg-slate-800 border border-slate-600 rounded-md px-3 py-2">
                  <Filter className="w-4 h-4 text-slate-400" />
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="bg-transparent border-none text-slate-300 focus:ring-0 w-32 font-body">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-800 border-slate-700">
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="done">Active</SelectItem>
                      <SelectItem value="indexing">Indexing</SelectItem>
                      <SelectItem value="paused">Paused</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilter}
                    className="text-slate-400 hover:text-white p-1 h-auto"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ) : (
                <Button 
                  variant="outline" 
                  className="text-slate-300 border-slate-600 font-body"
                  onClick={() => setShowFilter(true)}
                >
                  <Filter className="w-4 h-4 mr-2" />
                  Filter
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {(searchTerm || statusFilter !== "all") && (
          <div className="flex items-center space-x-2 text-sm text-slate-400 font-body">
            <span>Active filters:</span>
            {searchTerm && (
              <Badge variant="secondary" className="bg-slate-700 text-slate-300">
                Search: "{searchTerm}"
              </Badge>
            )}
            {statusFilter !== "all" && (
              <Badge variant="secondary" className="bg-slate-700 text-slate-300">
                Status: {statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
              </Badge>
            )}
            <span className="text-slate-500">({filteredProjects.length} results)</span>
          </div>
        )}

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-slate-400 font-body">Total Projects</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white font-titling">{projectPaths.length}</div>
              <Badge variant="secondary" className="mt-2 font-body">Tracked</Badge>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-slate-400 font-body">Total Size</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white font-titling">
                {Math.round(projectsArray.reduce((sum, p) => sum + p.totalSize, 0))} MB
              </div>
              <Badge variant="secondary" className="mt-2 font-body">Storage</Badge>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-slate-400 font-body">Total Files</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white font-titling">
                {(projectsArray.reduce((sum, p) => sum + p.filesCount, 0) / 1000).toFixed(1)}K
              </div>
              <Badge variant="secondary" className="mt-2 font-body">Files</Badge>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-slate-400 font-body">Active Projects</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white font-titling">
                {projectsArray.filter(p => p.status === "done").length}
              </div>
              <Badge variant="secondary" className="mt-2 font-body">Running</Badge>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Project List */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white font-titling">Indexed Projects</CardTitle>
          </CardHeader>
          <CardContent>
            {filteredProjects.length === 0 ? (
              <div className="text-center py-8">
                <div className="mx-auto w-12 h-12 bg-slate-600/20 rounded-full flex items-center justify-center mb-4">
                  <Search className="w-6 h-6 text-slate-400" />
                </div>
                <p className="text-slate-400 font-body">No projects match your current filters</p>
                <p className="text-slate-500 text-sm mt-1 font-body">Try adjusting your search or filter criteria</p>
              </div>
            ) : (
              <div className="space-y-6">
                {filteredProjects.map((project) => {
                  const { bg, text } =  getProgressColorSet(project.status);//colorMap.gray;
                  
                  return (
                    <div key={project.path} className="p-6 bg-slate-700/50 rounded-lg border border-slate-600/30">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-4">
                          {/* <div className={`w-12 h-12 bg-${project.color}-500/20 rounded-lg flex items-center justify-center`}>
                              <FolderOpen className={`w-6 h-6 text-${project.color}-500`} />
                            </div> */}
                            <div className={`w-12 h-12 ${bg} rounded-lg flex items-center justify-center`}>
                              <FolderOpen className={`w-6 h-6 ${text} `} />
                            </div>
                          <div>
                            <h3 className="text-white font-semibold text-lg font-titling">{project.name}</h3>
                            <p className="text-slate-400 text-sm font-body">{project.techStack.join(" • ")}</p>
                            <p className="text-slate-500 text-xs mt-1 font-body">{project.path}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Badge className={getStatusColor(project.status)}>{getStatusLabel(project.status)}</Badge>
                          {/* <Switch defaultChecked={project.status === "done"} /> */}
                        </div>
                      </div>

                      {/* Enhanced Project Info Grid */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div className="flex items-center space-x-2 text-slate-300">
                          <FileText className="w-4 h-4 text-slate-400" />
                          <span className="text-sm font-body">{project.filesCount.toLocaleString()} files</span>
                        </div>
                        <div className="flex items-center space-x-2 text-slate-300">
                          <HardDrive className="w-4 h-4 text-slate-400" />
                          <span className="text-sm font-body">{project.totalSize} MB</span>
                        </div>
                        <div className="flex items-center space-x-2 text-slate-300">
                          <Clock className="w-4 h-4 text-slate-400" />
                          <span className="text-sm font-body">Last: {project.lastIndexed}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-slate-300">
                          <Timer className="w-4 h-4 text-slate-400" />
                          <span className="text-sm font-body">Duration: {project.indexingDuration}</span>
                        </div>
                      </div>
                      
                      {/* Progress Bar */}
                      <div className="space-y-2 mb-4">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-slate-400 font-body">
                            {project.status === "indexing" ? "Indexing Progress" : "Index Status"}
                          </span>
                          <span className="text-slate-300 font-body">{project.progress}%</span>
                        </div>
                        <div className="w-full bg-slate-600 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(project.status)}`}
                            style={{ width: `${project.progress}%` }}
                          />
                        </div>
                        {project.status === "indexing" && (
                          <p className="text-xs text-slate-500 font-body">
                            <Clock className="w-3 h-3 inline mr-1" />
                            Estimated time remaining: {Math.ceil((100 - project.progress) / 10)} minutes
                          </p>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center justify-between pt-4 border-t border-slate-600/50">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleReindex(project.path)}
                            className="text-slate-300 border-slate-600 hover:bg-slate-600 font-body"
                          >
                            <RefreshCw className="w-4 h-4 mr-1" />
                            Reindex
                          </Button>
                          
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handlePauseResume(project.path)}
                            className="text-slate-300 border-slate-600 hover:bg-slate-600 font-body"
                            disabled={project.status === "done"}
                          >
                            {project.status === "indexing" ? (
                              <>
                                <Pause className="w-4 h-4 mr-1" />
                                Pause
                              </>
                            ) : project.status === "paused" ? (
                              <>
                                <Play className="w-4 h-4 mr-1" />
                                Resume
                              </>
                            ) : (
                              <>
                                <Pause className="w-4 h-4 mr-1" />
                                Pause
                              </>
                            )}
                          </Button>

                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-slate-300 border-slate-600 hover:bg-slate-600 font-body"
                              >
                                <MoreVertical className="w-4 h-4 mr-1" />
                                Details
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="bg-slate-800 border-slate-700">
                              <DialogHeader>
                                <DialogTitle className="text-white font-titling">{project.name} - Details</DialogTitle>
                                <DialogDescription className="text-slate-400 font-body">
                                  Comprehensive project information and statistics
                                </DialogDescription>
                              </DialogHeader>
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <h4 className="text-sm font-medium text-slate-300 mb-1 font-titling">Technology Stack</h4>
                                    <p className="text-sm text-slate-400 font-body">{project.techStack.join(", ")}</p>
                                  </div>
                                  <div>
                                    <h4 className="text-sm font-medium text-slate-300 mb-1 font-titling">Project Size</h4>
                                    <p className="text-sm text-slate-400 font-body">{project.totalSize} MB</p>
                                  </div>
                                  <div>
                                    <h4 className="text-sm font-medium text-slate-300 mb-1 font-titling">Total Files</h4>
                                    <p className="text-sm text-slate-400 font-body">{project.filesCount.toLocaleString()}</p>
                                  </div>
                                  <div>
                                    <h4 className="text-sm font-medium text-slate-300 mb-1 font-titling">Indexing Duration</h4>
                                    <p className="text-sm text-slate-400 font-body">{project.indexingDuration}</p>
                                  </div>
                                  <div>
                                      <h4 className="text-sm font-medium text-slate-300 mb-1  font-titling">Created</h4>
                                      <p className="text-sm text-slate-400 font-body">{new Date(project.createdAt).toLocaleDateString()}</p>
                                  </div>
                                </div>
                                <div>
                                  <h4 className="text-sm font-medium text-slate-300 mb-1 font-titling">Project Path</h4>
                                  <p className="text-sm text-slate-400 font-mono bg-slate-900/50 p-2 rounded">{project.path}</p>
                                </div>
                                <div>
                                  <h4 className="text-sm font-medium text-slate-300 mb-1 font-titling">Last Indexed</h4>
                                  <p className="text-sm text-slate-400 font-body">{project.lastIndexed}</p>
                                </div>
                              </div>
                            </DialogContent>
                          </Dialog>
                        </div>

                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-red-400 border-red-500/30 hover:bg-red-500/10 font-body"
                            >
                              <Trash2 className="w-4 h-4 mr-1" />
                              Remove
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent className="bg-slate-800 border-slate-700">
                            <AlertDialogHeader>
                              <AlertDialogTitle className="text-white font-titling">Remove Project</AlertDialogTitle>
                              <AlertDialogDescription className="text-slate-400 font-body">
                                Are you sure you want to remove "{project.name}" from the index? 
                                This action cannot be undone and all indexed data will be lost.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel className="bg-slate-700 text-slate-300 border-slate-600 font-body">
                                Cancel
                              </AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleRemoveProject(project.path)}
                                className="bg-red-600 hover:bg-red-700 font-body"
                              >
                                Yes, Remove
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>
                  );})}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default IndexProjects;
