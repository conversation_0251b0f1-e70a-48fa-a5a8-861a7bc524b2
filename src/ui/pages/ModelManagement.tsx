import { useState, useEffect, use<PERSON><PERSON>back } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Layout } from "@/components/Layout";
import { LoadingWrapper } from "@/components/LoadingWrapper";
import { Spinner } from "@/components/ui/spinner";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Download,
  CheckCircle,
  AlertCircle,
  Cloud,
  HardDrive,
  RefreshCw,
  MessageSquare,
  Database,
  Zap,
  Trash2
} from "lucide-react";

// Import types from model manager
interface ModelConfiguration {
  llmModels: LLMModel[];
  embeddingModels: EmbeddingModel[];
  preferences: {
    defaultChatModel: string;
    defaultIndexingModel: string;
    // defaultCodeAssistantModel: string;
  };
  indexingOptions: {
    maxChunkSize: number;
    minChunkSize: number;
    overlap: number;
  };
  retrievalOptions: {
    rerankThreshold: number;
    relevantCodeSnippets: number;
    relevantFilePaths: number;
  };
  apiSettings: {
    genaiUrl: string;
    authToken?: string;
    timeout: number;
  };
}

interface LLMModel {
  id: string;
  label: string;
  provider: 'openai' | 'anthropic' | 'bedrock' | 'local' | 'google' | 'huggingface';
  enabled: boolean;
  tokenBudget: number;
  usage: 'chat' | 'code-assistant' | 'both';
  modelFamily?: string;
}

interface EmbeddingModel {
  id: string;
  label: string;
  provider: 'openai' | 'local' | 'huggingface' | 'bedrock';
  enabled: boolean;
  usage: 'indexing' | 'retrieval' | 'both';
  size?: string;
  status: 'running' | 'stopped' | 'downloading' | 'error';
  downloadProgress?: number;
  localPath?: string;
}

// Extend window interface for API
// declare global {
//   interface Window {
//     api: {
//       getModelConfiguration: () => Promise<ModelConfiguration>;
//       saveModelConfiguration: (config: ModelConfiguration) => Promise<{ success: boolean }>;
//       toggleModel: (modelId: string, modelType: 'llm' | 'embedding') => Promise<{ success: boolean }>;
//       setPreferredModel: (type: string, modelId: string) => Promise<{ success: boolean }>;
//       syncWithPlugins: () => Promise<{ success: boolean }>;
//       clearModelCache: (modelId: string) => Promise<{ success: boolean }>;
//     };
//   }
// }

const ModelManagement = () => {
  const [modelConfig, setModelConfig] = useState<ModelConfiguration | null>(null);
  const [loading, setLoading] = useState(true);
  const [settingsOpen, setSettingsOpen] = useState<string | null>(null);
  const [isSyncing, setIsSyncing] = useState(false);
  const [togglingModels, setTogglingModels] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadModelConfiguration();
  }, []);

  const loadModelConfiguration = async () => {
    try {
      setLoading(true);
      const config = await window.api.getModelConfiguration();
      setModelConfig(config);
    } catch (error) {
      console.error('Failed to load model configuration:', error);
      toast.error('Failed to load model configuration');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'stopped':
        return <AlertCircle className="w-4 h-4 text-slate-400" />;
      case 'downloading':
        return <Download className="w-4 h-4 text-blue-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getProviderIcon = (provider: string) => {
    return provider === 'local' ? <HardDrive className="w-4 h-4" /> : <Cloud className="w-4 h-4" />;
  };

  const toggleModelEnabled = useCallback(async (modelId: string, modelType: 'llm' | 'embedding') => {
    if (!modelConfig || togglingModels.has(modelId)) return;

    try {
      // Add to toggling set
      setTogglingModels(prev => new Set(prev).add(modelId));

      // Optimistically update UI first to prevent flickering
      const updatedConfig = { ...modelConfig };
      if (modelType === 'llm') {
        const modelIndex = updatedConfig.llmModels.findIndex(m => m.id === modelId);
        if (modelIndex !== -1) {
          updatedConfig.llmModels[modelIndex] = {
            ...updatedConfig.llmModels[modelIndex],
            enabled: !updatedConfig.llmModels[modelIndex].enabled
          };
        }
      } else {
        const modelIndex = updatedConfig.embeddingModels.findIndex(m => m.id === modelId);
        if (modelIndex !== -1) {
          updatedConfig.embeddingModels[modelIndex] = {
            ...updatedConfig.embeddingModels[modelIndex],
            enabled: !updatedConfig.embeddingModels[modelIndex].enabled
          };
        }
      }

      // Update UI immediately
      setModelConfig(updatedConfig);

      // Then make API call
      await window.api.toggleModel(modelId, modelType);
      toast.success('Model status updated successfully');
    } catch (error) {
      console.error('Failed to toggle model:', error);
      toast.error('Failed to update model status');
      // Revert on error
      await loadModelConfiguration();
    } finally {
      // Remove from toggling set
      setTogglingModels(prev => {
        const newSet = new Set(prev);
        newSet.delete(modelId);
        return newSet;
      });
    }
  }, [modelConfig, togglingModels]);

  const setPreferredModel = async (type: string, modelId: string) => {
    if (!modelConfig) return;

    try {
      // Optimistically update UI first
      const updatedConfig = { ...modelConfig };
      switch (type) {
        case 'chat':
          updatedConfig.preferences.defaultChatModel = modelId;
          break;
        case 'indexing':
          updatedConfig.preferences.defaultIndexingModel = modelId;
          break;
        // case 'code-assistant':
        //   updatedConfig.preferences.defaultCodeAssistantModel = modelId;
        //   break;
      }

      // Update UI immediately
      setModelConfig(updatedConfig);

      // Then make API call
      await window.api.setPreferredModel(type, modelId);
      toast.success('Preferred model updated successfully');
    } catch (error) {
      console.error('Failed to set preferred model:', error);
      toast.error('Failed to update preferred model');
      // Revert on error
      await loadModelConfiguration();
    }
  };

  const clearModelCache = async (modelId: string) => {
    try {
      setTogglingModels(prev => new Set(prev).add(modelId));
      await window.api.clearModelCache(modelId);
      toast.success('Model cache cleared successfully');
    } catch (error) {
      console.error('Failed to clear model cache:', error);
      toast.error('Failed to clear model cache');
    } finally {
      setTogglingModels(prev => {
        const newSet = new Set(prev);
        newSet.delete(modelId);
        return newSet;
      });
    }
  };

  const syncWithPlugins = async () => {
    setIsSyncing(true);
    try {
      await window.api.syncWithPlugins();
      toast.success('Successfully synced with IDE plugins');
    } catch (error) {
      console.error('Failed to sync with plugins:', error);
      toast.error('Failed to sync with IDE plugins');
    } finally {
      setIsSyncing(false);
    }
  };

  // Helper functions to get model data
  const getEnabledLLMModels = () => modelConfig?.llmModels.filter(m => m.enabled) || [];
  const getEnabledEmbeddingModels = () => modelConfig?.embeddingModels.filter(m => m.enabled) || [];
  const getAllLLMModels = () => modelConfig?.llmModels || [];
  const getAllEmbeddingModels = () => modelConfig?.embeddingModels || [];

  // Count enabled models (both LLM and embedding)
  const getEnabledModelsCount = () => {
    const enabledLLM = modelConfig?.llmModels.filter(m => m.enabled).length || 0;
    const enabledEmbedding = modelConfig?.embeddingModels.filter(m => m.enabled).length || 0;
    return enabledLLM + enabledEmbedding;
  };

  const getLocalModelsCount = () => {
    const llmLocal = modelConfig?.llmModels.filter(m => m.provider === 'local').length || 0;
    const embeddingLocal = modelConfig?.embeddingModels.filter(m => m.provider === 'local').length || 0;
    return llmLocal + embeddingLocal;
  };
  const getTotalModelsCount = () => {
    const llmCount = modelConfig?.llmModels.length || 0;
    const embeddingCount = modelConfig?.embeddingModels.length || 0;
    return llmCount + embeddingCount;
  };
  const getCloudModelsCount = () => getTotalModelsCount() - getLocalModelsCount();

  if (loading) {
    return (
      <Layout>
        <LoadingWrapper
          skeletonProps={{
            showHeader: true,
            showStats: true,
            showTabs: true,
            showTable: true
          }}
        >
          <div className="p-6">
            <div className="text-center text-slate-400">Loading model configuration...</div>
          </div>
        </LoadingWrapper>
      </Layout>
    );
  }

  if (!modelConfig) {
    return (
      <Layout>
        <div className="p-6">
          <div className="text-center text-red-400">Failed to load model configuration</div>
          <Button onClick={loadModelConfiguration} className="mt-4">
            Retry
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <LoadingWrapper 
        skeletonProps={{ 
          showHeader: true, 
          showStats: true, 
          showTabs: true, 
          showTable: true 
        }}
      >
        <div className="p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-white font-titling">Model Management</h1>
            <div className="flex space-x-3">
              <Button variant="outline" className="text-slate-300 border-slate-600">
                <Download className="w-4 h-4 mr-2" />
                Download Model
              </Button>
              <Button
                onClick={syncWithPlugins}
                disabled={isSyncing}
                className="bg-purple-600 hover:bg-purple-700 disabled:opacity-50"
              >
                {isSyncing ? (
                  <Spinner size="sm" className="mr-2" />
                ) : (
                  <RefreshCw className="w-4 h-4 mr-2" />
                )}
                {isSyncing ? 'Syncing...' : 'Sync with Plugin'}
              </Button>
            </div>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm text-slate-400 font-body">Enabled Models</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{getEnabledModelsCount()}</div>
                <Badge variant="secondary" className="mt-2">
                  <Zap className="w-3 h-3 mr-1" />
                  Active
                </Badge>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm text-slate-400 font-body">Total Models</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{getTotalModelsCount()}</div>
                <Badge variant="secondary" className="mt-2">Available</Badge>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm text-slate-400 font-body">Local Models</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{getLocalModelsCount()}</div>
                <Badge variant="secondary" className="mt-2">
                  <HardDrive className="w-3 h-3 mr-1" />
                  Offline
                </Badge>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm text-slate-400 font-body">Cloud Models</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">{getCloudModelsCount()}</div>
                <Badge variant="secondary" className="mt-2">
                  <Cloud className="w-3 h-3 mr-1" />
                  API
                </Badge>
              </CardContent>
            </Card>
          </div>

          {/* Model Preferences */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white font-titling">Model Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm text-slate-400 mb-2 block">Preferred Chat Model</label>
                  <Select
                    value={modelConfig.preferences.defaultChatModel}
                    onValueChange={(value) => setPreferredModel('chat', value)}
                  >
                    <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {getEnabledLLMModels().map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          <div className="flex items-center space-x-2">
                            {getProviderIcon(model.provider)}
                            <span>{model.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm text-slate-400 mb-2 block">Preferred Indexing Model</label>
                  <Select
                    value={modelConfig.preferences.defaultIndexingModel}
                    onValueChange={(value) => setPreferredModel('indexing', value)}
                  >
                    <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {getEnabledEmbeddingModels().map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          <div className="flex items-center space-x-2">
                            {getProviderIcon(model.provider)}
                            <span>{model.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Model Tabs */}
          <Tabs defaultValue="llm" className="space-y-4">
            <TabsList className="bg-slate-800 border-slate-700">
              <TabsTrigger value="llm" className="data-[state=active]:bg-slate-700">
                <MessageSquare className="w-4 h-4 mr-2" />
                LLM Models
              </TabsTrigger>
              <TabsTrigger value="embedding" className="data-[state=active]:bg-slate-700">
                <Database className="w-4 h-4 mr-2" />
                Embedding Models
              </TabsTrigger>
            </TabsList>

            <TabsContent value="llm">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white font-titling">Language Models</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow className="border-slate-700">
                        <TableHead className="text-slate-300">Model</TableHead>
                        <TableHead className="text-slate-300">Provider</TableHead>
                        <TableHead className="text-slate-300">Status</TableHead>
                        <TableHead className="text-slate-300">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {getAllLLMModels().map((model) => (
                        <TableRow key={model.id} className="border-slate-700">
                          <TableCell className="text-white font-medium">
                            <div className="flex items-center space-x-2">
                              <Bot className="w-4 h-4 text-purple-500" />
                              <span>{model.label}</span>
                              {model.id === modelConfig.preferences.defaultChatModel && (
                                <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                                  Chat
                                </Badge>
                              )}
                              {/* {model.id === modelConfig.preferences.defaultCodeAssistantModel && (
                                <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                                  Assistant
                                </Badge>
                              )} */}
                            </div>
                          </TableCell>
                          <TableCell className="text-slate-300">
                            <div className="flex items-center space-x-2">
                              {getProviderIcon(model.provider)}
                              <span className="capitalize">{model.provider}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {getStatusIcon(model.enabled ? 'running' : 'stopped')}
                              <span className="text-slate-300 capitalize">
                                {model.enabled ? 'enabled' : 'disabled'}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Dialog open={settingsOpen === model.id} onOpenChange={(open) => setSettingsOpen(open ? model.id : null)}>
                              <DialogTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-slate-300 border-slate-600"
                                >
                                  <Settings className="w-4 h-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="bg-slate-800 border-slate-700">
                                <DialogHeader>
                                  <DialogTitle className="text-white">Model Settings - {model.label}</DialogTitle>
                                </DialogHeader>
                                <div className="space-y-4">
                                  <div className="flex items-center justify-between">
                                    <label className="text-slate-300">Enable Model</label>
                                    <div className="flex items-center space-x-2">
                                      {togglingModels.has(model.id) && (
                                        <Spinner size="sm" />
                                      )}
                                      <Switch
                                        checked={model.enabled}
                                        disabled={togglingModels.has(model.id)}
                                        onCheckedChange={() => toggleModelEnabled(model.id, 'llm')}
                                      />
                                    </div>
                                  </div>
                                  <div className="text-sm text-slate-400">
                                    {model.enabled
                                      ? "This model is available for selection in preferences."
                                      : "This model is disabled and won't appear in preferences."
                                    }
                                  </div>
                                  <div className="text-sm text-slate-300">
                                    <strong>Token Budget:</strong> {model.tokenBudget.toLocaleString()}
                                  </div>
                                  <div className="text-sm text-slate-300">
                                    <strong>Usage:</strong> {model.usage}
                                  </div>
                                </div>
                              </DialogContent>
                            </Dialog>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="embedding">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white font-titling">Embedding Models</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow className="border-slate-700">
                        <TableHead className="text-slate-300">Model</TableHead>
                        <TableHead className="text-slate-300">Provider</TableHead>
                        <TableHead className="text-slate-300">Size</TableHead>
                        <TableHead className="text-slate-300">Status</TableHead>
                        <TableHead className="text-slate-300">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {getAllEmbeddingModels().map((model) => (
                        <TableRow key={model.id} className="border-slate-700">
                          <TableCell className="text-white font-medium">
                            <div className="flex items-center space-x-2">
                              <Database className="w-4 h-4 text-blue-500" />
                              <span>{model.label}</span>
                              {model.id === modelConfig.preferences.defaultIndexingModel && (
                                <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                                  Indexing
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-slate-300">
                            <div className="flex items-center space-x-2">
                              {getProviderIcon(model.provider)}
                              <span className="capitalize">{model.provider}</span>
                            </div>
                          </TableCell>
                          <TableCell className="text-slate-300">
                            {model.size || 'API'}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {getStatusIcon(model.enabled ? 'running' : 'stopped')}
                              <span className="text-slate-300 capitalize">
                                {model.enabled ? 'enabled' : 'disabled'}
                              </span>
                              {model.status === 'downloading' && model.downloadProgress && (
                                <span className="text-xs text-blue-400">({model.downloadProgress}%)</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Dialog open={settingsOpen === model.id} onOpenChange={(open) => setSettingsOpen(open ? model.id : null)}>
                              <DialogTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-slate-300 border-slate-600"
                                >
                                  <Settings className="w-4 h-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="bg-slate-800 border-slate-700">
                                <DialogHeader>
                                  <DialogTitle className="text-white">Model Settings - {model.label}</DialogTitle>
                                </DialogHeader>
                                <div className="space-y-4">
                                  <div className="flex items-center justify-between">
                                    <label className="text-slate-300">Enable Model</label>
                                    <div className="flex items-center space-x-2">
                                      {togglingModels.has(model.id) && (
                                        <Spinner size="sm" />
                                      )}
                                      <Switch
                                        checked={model.enabled}
                                        disabled={togglingModels.has(model.id)}
                                        onCheckedChange={() => toggleModelEnabled(model.id, 'embedding')}
                                      />
                                    </div>
                                  </div>
                                  {model.provider === 'local' && (
                                    <div className="flex items-center justify-between">
                                      <label className="text-slate-300">Clear Cache</label>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => clearModelCache(model.id)}
                                        disabled={togglingModels.has(model.id)}
                                        className="text-red-400 border-red-600 hover:bg-red-600/10 disabled:opacity-50"
                                      >
                                        {togglingModels.has(model.id) ? (
                                          <Spinner size="sm" className="mr-2" />
                                        ) : (
                                          <Trash2 className="w-4 h-4 mr-2" />
                                        )}
                                        {togglingModels.has(model.id) ? 'Clearing...' : 'Clear'}
                                      </Button>
                                    </div>
                                  )}
                                  <div className="text-sm text-slate-400">
                                    {model.enabled
                                      ? "This model is available for selection in preferences."
                                      : "This model is disabled and won't appear in preferences."
                                    }
                                  </div>
                                  <div className="text-sm text-slate-300">
                                    <strong>Usage:</strong> {model.usage}
                                  </div>
                                  {model.localPath && (
                                    <div className="text-sm text-slate-300">
                                      <strong>Local Path:</strong> {model.localPath}
                                    </div>
                                  )}
                                </div>
                              </DialogContent>
                            </Dialog>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </LoadingWrapper>
    </Layout>
  );
};

export default ModelManagement;