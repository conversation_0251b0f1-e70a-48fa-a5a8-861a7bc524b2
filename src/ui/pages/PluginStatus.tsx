
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Layout } from "@/components/Layout";

type Plugin = {
  name: string;
  version: string | null;
  status: 'installed' | 'not-found' | 'outdated';
  statusText: string;
  pluginVersion: string | null;
  pluginStatus: 'installed' | 'not-installed' | 'needs-update';
  lastUpdated: string | null;
  actions: string[];
};


const PluginStatus = () => {
  const [plugins, setPlugins] = useState<Plugin[]>([
    {
      name: "VS Code",
      version: "1.87.0",
      status: "installed",
      statusText: "✅ Installed",
      pluginVersion: "1.2.3",
      pluginStatus: "installed",
      lastUpdated: "2 days ago",
      actions: ["Update", "Uninstall", "Reconfigure"]
    },
    {
      name: "<PERSON>urs<PERSON>",
      version: null,
      status: "not-found",
      statusText: "❌ Not Found",
      pluginVersion: null,
      pluginStatus: "not-installed",
      lastUpdated: null,
      actions: ["Install IDE", "Manual Setup"]
    },
    {
      name: "WinSCP",
      version: "5.21.6",
      status: "outdated",
      statusText: "⚠️ Outdated",
      pluginVersion: "1.1.0",
      pluginStatus: "needs-update",
      lastUpdated: "1 week ago",
      actions: ["Update Plugin", "Update IDE"]
    }
  ]);

  const reScanIDEs = () => {
    console.log("Re-scanning IDEs...");
    // Simulate IDE detection
    setTimeout(() => {
      console.log("IDE scan complete");
    }, 1000);
  };

  const installPlugin = (pluginName: string) => {
    setPlugins(prev => prev.map(plugin => 
      plugin.name === pluginName 
        ? { ...plugin, pluginStatus: "installed", pluginVersion: "1.2.3", lastUpdated: "Just now" }
        : plugin
    ));
  };

  const updatePlugin = (pluginName: string) => {
    setPlugins(prev => prev.map(plugin => 
      plugin.name === pluginName 
        ? { ...plugin, pluginStatus: "installed", pluginVersion: "1.2.3", lastUpdated: "Just now" }
        : plugin
    ));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "installed":
        return "bg-green-600";
      case "not-found":
        return "bg-red-600";
      case "outdated":
        return "bg-yellow-600";
      default:
        return "bg-gray-600";
    }
  };

  const getPluginStatusBadge = (status: string) => {
    switch (status) {
      case "installed":
        return <Badge className="bg-green-600">✅ Plugin Active</Badge>;
      case "not-installed":
        return <Badge className="bg-red-600">❌ Plugin Missing</Badge>;
      case "needs-update":
        return <Badge className="bg-yellow-600">⚠️ Plugin Outdated</Badge>;
      default:
        return <Badge className="bg-gray-600">Unknown</Badge>;
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">IDE Integration Status</h1>
            <p className="text-slate-400">Manage your IDE plugins and extensions</p>
          </div>
          <Button 
            onClick={reScanIDEs}
            className="bg-blue-600 hover:bg-blue-700"
          >
            🔁 Re-scan IDEs
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {plugins.map((plugin) => (
            <Card key={plugin.name} className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center justify-between">
                  <span>{plugin.name}</span>
                  <Badge className={getStatusColor(plugin.status)}>
                    {plugin.statusText}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div>
                    <p className="text-slate-400 text-sm">IDE Version</p>
                    <p className="text-white">{plugin.version || "Not Installed"}</p>
                  </div>
                  
                  <div>
                    <p className="text-slate-400 text-sm">Plugin Status</p>
                    <div className="flex items-center space-x-2">
                      {getPluginStatusBadge(plugin.pluginStatus)}
                    </div>
                  </div>

                  {plugin.pluginVersion && (
                    <div>
                      <p className="text-slate-400 text-sm">Plugin Version</p>
                      <p className="text-white">{plugin.pluginVersion}</p>
                    </div>
                  )}
                </div>
                
                <div className="space-y-2">
                  {plugin.status === "not-found" ? (
                    <div className="space-y-2">
                      <a 
                        href={plugin.name === "VS Code" ? "https://code.visualstudio.com/" : 
                              plugin.name === "Cursor" ? "https://www.cursor.so/" : 
                              "https://winscp.net/"}
                        target="_blank"
                        className="block w-full"
                      >
                        <Button className="w-full bg-blue-600 hover:bg-blue-700">
                          Install {plugin.name}
                        </Button>
                      </a>
                      <Button 
                        variant="outline"
                        size="sm"
                        className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                      >
                        Manual Setup Guide
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {plugin.pluginStatus === "not-installed" && (
                        <Button
                          onClick={() => installPlugin(plugin.name)}
                          className="w-full bg-blue-600 hover:bg-blue-700"
                        >
                          Install Plugin
                        </Button>
                      )}
                      {plugin.pluginStatus === "needs-update" && (
                        <Button
                          onClick={() => updatePlugin(plugin.name)}
                          className="w-full bg-yellow-600 hover:bg-yellow-700"
                        >
                          Update Plugin
                        </Button>
                      )}
                      {plugin.pluginStatus === "installed" && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                          >
                            Reconfigure
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full border-red-600 text-red-300 hover:bg-red-900"
                          >
                            Uninstall
                          </Button>
                        </>
                      )}
                    </div>
                  )}
                </div>

                {plugin.lastUpdated && (
                  <div className="pt-2 border-t border-slate-700">
                    <p className="text-slate-400 text-xs">
                      Last updated: {plugin.lastUpdated}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Installation Instructions */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Plugin Lifecycle Management</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="bg-slate-700 p-4 rounded-lg">
                <h3 className="text-white font-medium mb-2">Automatic Installation</h3>
                <p className="text-slate-300 text-sm mb-2">
                  One-click installation for supported IDEs:
                </p>
                <div className="space-y-1 text-sm text-slate-400">
                  <p>• VS Code: Extension Marketplace</p>
                  <p>• Cursor: Direct plugin injection</p>
                  <p>• WinSCP: Plugin directory copy</p>
                </div>
              </div>
              
              <div className="bg-slate-700 p-4 rounded-lg">
                <h3 className="text-white font-medium mb-2">Manual Installation</h3>
                <p className="text-slate-300 text-sm mb-2">
                  If automatic installation fails:
                </p>
                <div className="bg-slate-900 p-3 rounded font-mono text-sm text-green-400">
                  <p>1. Download: alpineintellect.com/plugins</p>
                  <p>2. Extract to IDE extensions folder</p>
                  <p>3. Restart IDE and configure</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default PluginStatus;
