
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Layout } from "@/components/Layout";

const Search = () => {
  const [query, setQuery] = useState("How does the onboarding wizard inject API keys?");
  const [searchResults, setSearchResults] = useState([
    {
      file: "pluginInstaller.ts",
      line: 42,
      snippet: "config.apiKey = getStoredToken();",
      context: "// Injects the token into plugin config",
      relevance: 0.95,
      project: "Main App"
    },
    {
      file: "auth/tokenManager.js",
      line: 28,
      snippet: "const storeApiKey = (key) => { localStorage.setItem('alpine_key', key); }",
      context: "// Store API key in local storage",
      relevance: 0.87,
      project: "Main App"
    },
    {
      file: "config/setup.ts",
      line: 15,
      snippet: "export const setupApiConnection = (apiKey: string) => {",
      context: "// Setup API connection with provided key",
      relevance: 0.82,
      project: "SDK Documentation"
    }
  ]);

  const handleSearch = () => {
    // Simulate search functionality
    console.log("Searching for:", query);
  };

  const getRelevanceColor = (relevance: number) => {
    if (relevance >= 0.9) return "bg-green-600";
    if (relevance >= 0.8) return "bg-yellow-600";
    return "bg-blue-600";
  };

  return (
    <Layout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Search</h1>
          <p className="text-slate-400">Ask your code anything</p>
        </div>

        {/* Search Input */}
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6">
            <div className="flex space-x-4">
              <Input
                placeholder="Ask your code anything..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="flex-1 bg-slate-700 border-slate-600 text-white placeholder-slate-400"
              />
              <Button onClick={handleSearch} className="bg-blue-600 hover:bg-blue-700">
                Search
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Search Results */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-white">
              Search Results ({searchResults.length})
            </h2>
            <Badge className="bg-slate-700 text-slate-300">
              Found in {new Set(searchResults.map(r => r.project)).size} projects
            </Badge>
          </div>

          {searchResults.map((result, index) => (
            <Card key={index} className="bg-slate-800 border-slate-700">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-white text-lg">{result.file}</CardTitle>
                    <p className="text-slate-400 text-sm">Line {result.line} • {result.project}</p>
                  </div>
                  <Badge className={getRelevanceColor(result.relevance)}>
                    {Math.round(result.relevance * 100)}% match
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="bg-slate-900 p-4 rounded-lg">
                  <p className="text-green-400 text-sm font-mono mb-1">{result.context}</p>
                  <p className="text-blue-300 font-mono">{result.snippet}</p>
                </div>
                <div className="flex space-x-2">
                  <Button size="sm" variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700">
                    Open File
                  </Button>
                  <Button size="sm" variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700">
                    Show Context
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Search Tips */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Search Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-white font-medium mb-2">Natural Language</h4>
                <ul className="space-y-1 text-slate-300 text-sm">
                  <li>• "How does authentication work?"</li>
                  <li>• "Show me database queries"</li>
                  <li>• "Find error handling code"</li>
                </ul>
              </div>
              <div>
                <h4 className="text-white font-medium mb-2">Code Search</h4>
                <ul className="space-y-1 text-slate-300 text-sm">
                  <li>• "function connectDatabase"</li>
                  <li>• "class UserManager"</li>
                  <li>• "const apiKey"</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default Search;
