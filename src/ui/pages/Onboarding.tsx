import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { TitleBar } from "@/components/TitleBar";
import { toast } from "sonner";

function usePluginInstaller({
  pluginUpdateMap,
  refreshData,
}: {
  pluginUpdateMap: Record<string, PluginUpdateInfo>;
  refreshData: () => Promise<void>;
}) {
  const [installingIDEId, setInstallingIDEId] = useState<string | null>(null);
  const [downloading, setDownloading] = useState(false);

  const handleDownloadAndInstall = async (ideId: string) => {
    const pluginInfo = pluginUpdateMap[ideId];
    if (!pluginInfo?.downloadUrl) return;

    setInstallingIDEId(ideId);
    setDownloading(true);

    try {
      const downloadRes = await window.api.downloadPlugin(pluginInfo.downloadUrl);
      if (!downloadRes?.success || !downloadRes.path) {
        throw new Error(downloadRes?.error || "Download failed");
      }

      const installRes = await window.api.installPlugin(downloadRes.path, ideId);
      if (installRes.success) {
        toast.success(`Alpine Intellect plugin successfully installed for ${ideId}!`);
        await refreshData();
      } else {
        throw new Error(installRes?.error || "Installation failed");
      }
    } catch (err: any) {
      toast.error(`Failed to install plugin for ${ideId}: ${err.message}`);
    } finally {
      setDownloading(false);
      setInstallingIDEId(null);
    }
  };

  return {
    handleDownloadAndInstall,
    isDownloading: downloading,
    activeIdeId: installingIDEId,
  };
}


type IDE = {
  ideId: string;
  name: string;
  version: string;
  status: "installed" | "not-installed";
  pluginStatus: "installed" | "not-installed" | "needs-update";
  pluginVersion: string | null;
};

type PluginMap = {
  [ideId: string]: string[] | null;
};

type PluginUpdateInfo = {
  installedVersion: string | null;
  latestVersion: string;
  updateAvailable: boolean;
  downloadUrl: string;
};

const Onboarding = () => {
  const [detectedIDEs, setDetectedIDEs] = useState<IDE[]>([]);
  const [loading, setLoading] = useState(true);
  const [plugins, setPlugins] = useState<PluginMap>({});
  const [pluginUpdateInfo, setPluginUpdateInfo] = useState<PluginUpdateInfo | null>(null);
  const navigate = useNavigate();

  const [pluginUpdateMap, setPluginUpdateMap] = useState<Record<string, PluginUpdateInfo>>({});

const scanIDEs = async () => {
  setLoading(true);
  try {
    const rawIDEs = await window.api.scanIDEs();
    let normalized: IDE[] = rawIDEs.map((item: any) => ({
      ideId: item.ideId,
      name: item.name,
      version: item.version || "–",
      status: item.status === "installed" ? "installed" : "not-installed",
      pluginStatus: item.pluginStatus || "not-installed",
      pluginVersion: item.pluginVersion || null,
    }));

    // Dynamically get update info for each IDE
    const updateMap: Record<string, PluginUpdateInfo> = {};
    for (const ide of normalized) {
      const updateInfo = await window.api.checkPluginUpdate(ide.ideId).catch(() => null);
      if (updateInfo) updateMap[ide.ideId] = updateInfo;
    }

    // Normalize IDE list based on update map
    normalized = normalized.map((ide) => {
      const update = updateMap[ide.ideId];
      if (
        ide.status === "installed" &&
        update?.updateAvailable &&
        ide.pluginStatus === "installed"
      ) {
        return { ...ide, pluginStatus: "needs-update" };
      }
      return ide;
    });

    setDetectedIDEs(normalized);
    setPluginUpdateMap(updateMap);

    // Show success message with IDE count
    const installedIDEs = normalized.filter(ide => ide.status === "installed");
    if (installedIDEs.length > 0) {
      toast.success(`Found ${installedIDEs.length} IDE${installedIDEs.length > 1 ? 's' : ''}: ${installedIDEs.map(ide => ide.name).join(', ')}`);
    } else {
      toast.info("No supported IDEs found. Please install VS Code or Cursor.");
    }

    // Fetch plugin folder list per IDE
    for (const ide of normalized) {
      if (ide.status === "installed") {
        const idePlugins = await window.api.getIDEPlugins(ide.ideId).catch(() => []);
        setPlugins((prev) => ({ ...prev, [ide.ideId]: idePlugins || [] }));
      }
    }
  } catch (err) {
    console.error("IDE scan failed:", err);
    toast.error("Failed to scan IDEs");
    setDetectedIDEs([]);
  }
  setLoading(false);
};


  const { handleDownloadAndInstall, isDownloading, activeIdeId } = usePluginInstaller({
    pluginUpdateMap,
    refreshData: scanIDEs,
  });

  const getPluginStatusBadge = (status: IDE["pluginStatus"]) => {
    switch (status) {
      case "installed":
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/30 hover:bg-green-500/30 text-xs">✅ Installed</Badge>;
      case "not-installed":
        return <Badge className="bg-red-500/20 text-red-400 border-red-500/30 hover:bg-red-500/30 text-xs">❌ Not Installed</Badge>;
      case "needs-update":
        return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30 hover:bg-yellow-500/30 text-xs">⚠️ Update Available</Badge>;
      default:
        return <Badge variant="secondary" className="text-xs">Unknown</Badge>;
    }
  };


  const getActionButton = (ide: IDE) => {
    const isActive = activeIdeId === ide.ideId;
    const isWorking = isDownloading && isActive;

    if (ide.pluginStatus === "installed") {
      return <Button variant="outline" className="text-muted-foreground border-border h-7 text-xs">Installed</Button>;
    }

    if (ide.pluginStatus === "not-installed" || ide.pluginStatus === "needs-update") {
      return (
        <Button
          onClick={() => handleDownloadAndInstall(ide.ideId)}
          disabled={isWorking}
          size="sm"
          className={`${ide.pluginStatus === "needs-update" ? "bg-yellow-600 hover:bg-yellow-700" : "bg-blue-600 hover:bg-blue-700"} text-white h-7 text-xs`}
        >
          {isWorking ? "Installing..." : ide.pluginStatus === "needs-update" ? "Update Plugin" : "Install Plugin"}
        </Button>
      );
    }

    return null;
  };

  const installedIDEs = detectedIDEs.filter((ide) => ide.status === "installed");
  const hasInstalledPlugins = installedIDEs.some((ide) => ide.pluginStatus === "installed");
  const continueEnabled = hasInstalledPlugins;

  useEffect(() => {
    scanIDEs();
  }, []);

  return (
    <div className="min-h-screen bg-slate-900 text-foreground flex flex-col">

      {/* Title Bar */}
      <TitleBar title="Setup Wizard - Alpine Intellect" />
      
      <div className="flex-1 flex items-center justify-center p-3 overflow-hidden">
        <div className="w-full max-w-4xl mx-auto max-h-[calc(100vh-4rem)] overflow-y-auto">

          {/* Main Setup Card */}
          <Card className="bg-slate-800 border-slate-700 mb-3">
            <CardHeader className="text-center pb-3">
              <CardTitle className="text-lg text-card-foreground">IDE Plugin Status</CardTitle>
              <p className="text-muted-foreground text-xs mt-1">We've detected the following IDEs</p>
            </CardHeader>
            <CardContent className="px-3 pb-3">
              {loading ? (
                <p className="text-center text-muted-foreground py-4">🔍 Scanning IDEs...</p>
              ) : installedIDEs.length === 0 ? (
                <div className="text-center space-y-3 py-4">
                  <h3 className="text-base font-semibold text-card-foreground">No Supported IDEs Found</h3>
                  <p className="text-muted-foreground text-xs">Please install VS Code, Cursor.</p>
                  <Button onClick={scanIDEs} className="bg-blue-600 hover:bg-blue-700 text-white h-8 text-sm">🔁 Retry Scan</Button>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="rounded-lg border border-slate-700 overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow className="bg-slate-700/30">
                          <TableHead className="text-sm">IDE</TableHead>
                          <TableHead className="text-sm hidden sm:table-cell">Version</TableHead>
                          <TableHead className="text-sm">Plugin Status</TableHead>
                          <TableHead className="text-sm text-center">Action</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {installedIDEs.map((ide) => (
                          <TableRow key={ide.name} className="hover:bg-slate-700/20 border-slate-700 h-10">
                            <TableCell className="py-2">
                              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-3">
                                <span className="font-medium text-foreground text-sm">{ide.name}</span>
                                <span className="text-xs text-muted-foreground sm:hidden">v{ide.version}</span>
                              </div>
                            </TableCell>
                            <TableCell className="text-muted-foreground hidden sm:table-cell text-xs py-2">
                              v{ide.version}
                            </TableCell>
                            <TableCell className="py-2">
                              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2">
                                {getPluginStatusBadge(ide.pluginStatus)}
                                {ide.pluginVersion && (
                                  <span className="text-muted-foreground text-xs mt-1 sm:mt-0">v{ide.pluginVersion}</span>
                                )}
                              </div>
                            </TableCell>
                            <TableCell className="text-center py-2">
                              {getActionButton(ide)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Alert if no plugins installed */}
                  {!hasInstalledPlugins && (
                    <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-2">
                      <div className="flex items-start space-x-2">
                        <span className="text-yellow-400 text-base">🚫</span>
                        <div>
                          <h4 className="font-medium text-yellow-400 text-xs">Plugin Installation Required</h4>
                          <p className="text-yellow-300/80 text-xs mt-1">
                            None of your installed IDEs have the Alpine Intellect plugin. Please install the plugin in at least one IDE to continue.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Re-scan Button */}
                  <div className="flex justify-center">
                    <Button onClick={scanIDEs} variant="outline" size="sm" className="h-7 text-xs">
                      🔁 Retry Scan
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {installedIDEs.length > 0 && (
            <Card className="bg-slate-800 border-slate-700 mb-3">
              <CardContent className="p-3">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                  <span className="text-xs">
                    {continueEnabled
                      ? "✅ Setup Complete - Ready to continue!"
                      : "⚠️ Please install at least one plugin to continue"}
                  </span>
                  <Button
                    onClick={() => navigate("/dashboard")}
                    disabled={!continueEnabled}
                    className="bg-green-600 hover:bg-green-700 text-white disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto h-8 text-sm"
                  >
                    ✅ Continue to Dashboard
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Help Link */}
          <div className="text-center">
            <a href="#" className="text-muted-foreground hover:text-primary text-xs transition-colors">
              Need help? Contact Support
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
