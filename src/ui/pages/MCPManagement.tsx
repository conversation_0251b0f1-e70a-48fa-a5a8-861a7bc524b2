import React, { useState, useEffect } from 'react';
import { Layout } from '../components/Layout';
import ServerDirectory from '../components/MCPServerStore/ServerDirectory';
import ConnectedServers from '../components/ServerManagement/ConnectedServers';
import ToolsDiscovery from '../components/ToolsDiscovery/ToolsDiscovery';
import StatusDashboard from '../components/StatusMonitoring/StatusDashboard';
import { useMCPServers } from '../hooks/useMCPServers';
import { useServerStatus } from '../hooks/useServerStatus';
import { useToolsDiscovery } from '../hooks/useToolsDiscovery';
import { Button } from '../components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Alert<PERSON><PERSON>cle, CheckCircle, Clock, Zap } from 'lucide-react';
import { toast } from 'sonner';

interface MCPServer {
  id: string;
  name: string;
  description: string;
  category: string;
  status: 'stopped' | 'starting' | 'running' | 'error' | 'connected' | 'disconnected';
  installed: boolean;
  configured: boolean;
  version?: string;
  author?: string;
  capabilities: string[];
  toolsCount: number;
  lastSeen?: Date;
  health?: {
    responsive: boolean;
    latency: number;
    errorRate: number;
  };
}

interface Tool {
  id: string;
  name: string;
  description: string;
  serverId: string;
  serverName: string;
  category: string;
  usageCount: number;
  lastUsed?: Date;
  parameters: any[];
}

const MCPManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedServer, setSelectedServer] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // Custom hooks for data management
  const { 
    servers, 
    connectedServers, 
    loading: serversLoading, 
    error: serversError,
    installServer,
    configureServer,
    connectServer,
    disconnectServer,
    startServer,
    stopServer,
    removeServer
  } = useMCPServers();

  const {
    serverStatuses,
    loading: statusLoading,
    refreshStatus
  } = useServerStatus(connectedServers);

  const {
    tools,
    loading: toolsLoading,
    refreshTools
  } = useToolsDiscovery(connectedServers);

  // Refresh data periodically
  useEffect(() => {
    const interval = setInterval(() => {
      refreshStatus();
      refreshTools();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [refreshStatus, refreshTools]);

  // Handle server actions
  const handleServerAction = async (action: string, serverId: string) => {
    try {
      switch (action) {
        case 'connect':
          await connectServer(serverId);
          toast.success('Server connected successfully');
          break;
        case 'disconnect':
          await disconnectServer(serverId);
          toast.success('Server disconnected');
          break;
        case 'start':
          await startServer(serverId);
          toast.success('Server started');
          break;
        case 'stop':
          await stopServer(serverId);
          toast.success('Server stopped');
          break;
        case 'configure':
          // Open configuration modal
          setSelectedServer(serverId);
          break;
        case 'remove':
          await removeServer(serverId);
          toast.success('Server removed');
          break;
        default:
          console.warn('Unknown action:', action);
      }
      
      // Refresh data after action
      setRefreshKey(prev => prev + 1);
      refreshStatus();
      refreshTools();
    } catch (error: any) {
      toast.error(error.message || 'Action failed');
    }
  };

  // Calculate summary statistics
  const stats = {
    totalServers: servers.length,
    connectedServers: connectedServers.length,
    availableTools: tools.length,
    healthyServers: Object.values(serverStatuses).filter(s => s.health?.responsive).length
  };

  if (serversLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-slate-400">Loading MCP servers...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (serversError) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-white mb-2">Failed to Load MCP Servers</h2>
            <p className="text-slate-400 mb-4">{serversError}</p>
            <Button onClick={() => setRefreshKey(prev => prev + 1)}>
              Try Again
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="h-full bg-slate-900 text-white">
        {/* Header */}
        <div className="border-b border-slate-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white mb-2">MCP Server Management</h1>
              <p className="text-slate-400">
                Manage your Model Context Protocol integrations and server connections
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => {
                  refreshStatus();
                  refreshTools();
                  setRefreshKey(prev => prev + 1);
                }}
                disabled={statusLoading || toolsLoading}
              >
                {statusLoading || toolsLoading ? (
                  <div className="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2" />
                ) : null}
                Refresh
              </Button>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-4 gap-4 mt-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400">Total Servers</p>
                    <p className="text-2xl font-bold text-white">{stats.totalServers}</p>
                  </div>
                  <Zap className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400">Connected</p>
                    <p className="text-2xl font-bold text-green-500">{stats.connectedServers}</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400">Available Tools</p>
                    <p className="text-2xl font-bold text-purple-500">{stats.availableTools}</p>
                  </div>
                  <Clock className="w-8 h-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400">Healthy</p>
                    <p className="text-2xl font-bold text-yellow-500">{stats.healthyServers}</p>
                  </div>
                  <AlertCircle className="w-8 h-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
            <TabsList className="grid w-full grid-cols-4 bg-slate-800">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="servers">Server Store</TabsTrigger>
              <TabsTrigger value="tools">Tools</TabsTrigger>
              <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-6 h-full">
              <div className="grid grid-cols-2 gap-6 h-full">
                <div className="space-y-6">
                  <ServerDirectory
                    servers={servers}
                    onServerAction={handleServerAction}
                    compact={true}
                  />
                </div>
                <div className="space-y-6">
                  <ConnectedServers
                    servers={connectedServers.map(id => servers.find(s => s.id === id)!).filter(Boolean)}
                    serverStatuses={serverStatuses}
                    onServerAction={handleServerAction}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="servers" className="mt-6 h-full">
              <ServerDirectory
                servers={servers}
                onServerAction={handleServerAction}
                compact={false}
              />
            </TabsContent>

            <TabsContent value="tools" className="mt-6 h-full">
              <ToolsDiscovery
                tools={tools}
                servers={servers}
                loading={toolsLoading}
              />
            </TabsContent>

            <TabsContent value="monitoring" className="mt-6 h-full">
              <StatusDashboard
                servers={connectedServers.map(id => servers.find(s => s.id === id)!).filter(Boolean)}
                serverStatuses={serverStatuses}
                tools={tools}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
};

export default MCPManagement;
