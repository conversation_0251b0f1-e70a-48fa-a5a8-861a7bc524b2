
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Layout } from "@/components/Layout";
import { Activity, Clock } from "lucide-react";
import { toast } from "sonner";

const Dashboard = () => {
  const handleStartWizard = () => {
    toast.info("Wizard feature coming soon!");
  };

  const handleInstallPlugin = () => {
    toast.info("Please use the IDE Integrator page to install plugins");
  };

  return (
    <Layout title="Dashboard - Alpine Intellect">
      {false?(
        <div className="space-y-4">
          <div>
            <h1 className="text-2xl font-bold text-white mb-1 font-titling">Welcome, Ashok!</h1>
            <p className="text-slate-400 text-sm">Here's your Alpine Intellect Assistant status</p>
          </div>

          {/* Status Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader className="pb-3">
                <CardTitle className="text-white flex items-center space-x-2 text-base font-titling">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Plugin Status</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300 text-sm">VS Code</span>
                    <Badge className="bg-green-600 text-xs">✅ Installed</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300 text-sm">Cursor</span>
                    <Badge variant="secondary" className="bg-yellow-600 text-xs">⚠️ Needs Install</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader className="pb-3">
                <CardTitle className="text-white flex items-center space-x-2 text-base font-titling">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Model Status</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300 text-sm">Titan Pro</span>
                    <Badge className="bg-green-600 text-xs">🟢 Running</Badge>
                  </div>
                  <div className="text-xs text-slate-400">
                    CPU: 38% | RAM: 1.2 GB
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader className="pb-3">
                <CardTitle className="text-white text-base font-titling">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 pt-0">
                <Button
                  onClick={handleStartWizard}
                  className="w-full bg-blue-600 hover:bg-blue-700 h-8 text-sm"
                >
                  Start Wizard
                </Button>
                <Button
                  onClick={handleInstallPlugin}
                  variant="outline"
                  className="w-full border-slate-600 text-slate-300 hover:bg-slate-700 h-8 text-sm"
                >
                  Install Plugin
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="pb-3">
              <CardTitle className="text-white text-base font-titling">Recent Activity</CardTitle>
              <CardDescription className="text-slate-400 text-sm">Latest system events and updates</CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                <div className="flex items-center space-x-3 p-2 bg-slate-700 rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
                  <div>
                    <p className="text-white text-sm">VS Code plugin successfully updated</p>
                    <p className="text-slate-400 text-xs">2 minutes ago</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3 p-2 bg-slate-700 rounded-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                  <div>
                    <p className="text-white text-sm">Model sync completed for /projects/app</p>
                    <p className="text-slate-400 text-xs">15 minutes ago</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3 p-2 bg-slate-700 rounded-lg">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full flex-shrink-0"></div>
                  <div>
                    <p className="text-white text-sm">Cursor plugin installation pending</p>
                    <p className="text-slate-400 text-xs">1 hour ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ):(
        <div className="flex items-center justify-center min-h-[60vh]">
          <Card className="bg-slate-800 border-slate-700 max-w-md w-full mx-4">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mb-4">
                <Activity className="w-8 h-8 text-blue-500" />
              </div>
              <CardTitle className="text-white text-xl font-titling">Dashboard</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <div className="flex items-center justify-center mb-4">
                <Clock className="w-5 h-5 text-slate-400 mr-2" />
                <span className="text-slate-300 font-medium">Coming Soon</span>
              </div>
              <p className="text-slate-400 text-sm leading-relaxed">
                We're working hard to bring you an amazing dashboard experience with real-time monitoring and system insights. 
                Stay tuned for updates!
              </p>
            </CardContent>
          </Card>
        </div>
    )}
      
    </Layout>
  );
};

export default Dashboard;
