
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Layout } from "@/components/Layout";
import { Clock, SettingsIcon } from "lucide-react";
import { toast } from "sonner";

const Settings = () => {
  const [darkMode, setDarkMode] = useState(true);
  const [autoSync, setAutoSync] = useState(true);
  const [notifications, setNotifications] = useState(true);

  const tokenInfo = {
    expiry: "2025-07-15",
    lastRefresh: "5 mins ago",
    status: "Active"
  };

  // Handler functions for buttons
  const handleViewToken = () => {
    toast.info("Token viewing feature coming soon!");
  };

  const handleRefreshToken = () => {
    toast.success("Token refreshed successfully!");
  };

  const handleLogout = () => {
    toast.info("Logout functionality coming soon!");
  };

  const handleViewDebugLogs = () => {
    toast.info("Debug logs viewer coming soon!");
  };

  const handleExportLogs = () => {
    toast.success("Logs exported successfully!");
  };

  const handleClearCache = () => {
    toast.success("Cache cleared successfully!");
  };

  // Handler functions for switches
  const handleDarkModeChange = (checked: boolean) => {
    setDarkMode(checked);
    toast.success(`Dark mode ${checked ? 'enabled' : 'disabled'}`);
  };

  const handleAutoSyncChange = (checked: boolean) => {
    setAutoSync(checked);
    toast.success(`Auto sync ${checked ? 'enabled' : 'disabled'}`);
  };

  const handleNotificationsChange = (checked: boolean) => {
    setNotifications(checked);
    toast.success(`Notifications ${checked ? 'enabled' : 'disabled'}`);
  };

  return (
    <Layout>

      {false ? (
        <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2 font-titling">Settings</h1>
          <p className="text-slate-400">Manage your Alpine Intellect Assistant preferences</p>
        </div>

        {/* Token Management */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white font-titling">Token Management</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-slate-400 text-sm">Token Status</p>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge className="bg-green-600">{tokenInfo.status}</Badge>
                </div>
              </div>
              <div>
                <p className="text-slate-400 text-sm">Expires</p>
                <p className="text-white">{tokenInfo.expiry}</p>
              </div>
              <div>
                <p className="text-slate-400 text-sm">Last Refresh</p>
                <p className="text-white">{tokenInfo.lastRefresh}</p>
              </div>
            </div>
            
            <div className="flex space-x-3">
              <Button
                onClick={handleViewToken}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                View Token
              </Button>
              <Button
                onClick={handleRefreshToken}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Refresh Token
              </Button>
              <Button
                onClick={handleLogout}
                variant="destructive"
                className="bg-red-600 hover:bg-red-700"
              >
                Logout
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Appearance */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white font-titling">Appearance</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Dark Mode</p>
                <p className="text-slate-400 text-sm">Use dark theme throughout the application</p>
              </div>
              <Switch
                checked={darkMode}
                onCheckedChange={handleDarkModeChange}
              />
            </div>
          </CardContent>
        </Card>

        {/* Sync Settings */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white font-titling">Sync Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Auto Sync</p>
                <p className="text-slate-400 text-sm">Automatically sync project changes</p>
              </div>
              <Switch
                checked={autoSync}
                onCheckedChange={handleAutoSyncChange}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Notifications</p>
                <p className="text-slate-400 text-sm">Show sync and update notifications</p>
              </div>
              <Switch
                checked={notifications}
                onCheckedChange={handleNotificationsChange}
              />
            </div>
          </CardContent>
        </Card>

        {/* Debug Logs */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white font-titling">Debug Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-slate-400 text-sm">Application Version</p>
                <p className="text-white">v1.2.3</p>
              </div>
              <div>
                <p className="text-slate-400 text-sm">Last Sync</p>
                <p className="text-white">5 mins ago</p>
              </div>
              <div>
                <p className="text-slate-400 text-sm">Log Level</p>
                <p className="text-white">INFO</p>
              </div>
              <div>
                <p className="text-slate-400 text-sm">Plugin Version</p>
                <p className="text-white">v2.1.0</p>
              </div>
            </div>
            
            <div className="flex space-x-3">
              <Button
                onClick={handleViewDebugLogs}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                View Debug Logs
              </Button>
              <Button
                onClick={handleExportLogs}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                Export Logs
              </Button>
              <Button
                onClick={handleClearCache}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                Clear Cache
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* System Information */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white font-titling">System Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-slate-900 p-4 rounded-lg font-mono text-sm">
              <div className="space-y-1 text-slate-300">
                <div>OS: macOS 14.2.1</div>
                <div>Node.js: v20.10.0</div>
                <div>Electron: v28.1.0</div>
                <div>Memory: 16 GB</div>
                <div>Storage: 512 GB SSD</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      ) : (
        <div className="flex items-center justify-center min-h-[60vh]">
          <Card className="bg-slate-800 border-slate-700 max-w-md w-full mx-4">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mb-4">
                <SettingsIcon className="w-8 h-8 text-blue-500" />
              </div>
              <CardTitle className="text-white text-xl font-titling">Settings</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <div className="flex items-center justify-center mb-4">
                <Clock className="w-5 h-5 text-slate-400 mr-2" />
                <span className="text-slate-300 font-medium">Coming Soon</span>
              </div>
              <p className="text-slate-400 text-sm leading-relaxed">
                We're working hard to bring you comprehensive settings and customization options. 
                Stay tuned for updates!
              </p>
            </CardContent>
          </Card>
        </div>
      )}
    </Layout>
  );
};

export default Settings;
