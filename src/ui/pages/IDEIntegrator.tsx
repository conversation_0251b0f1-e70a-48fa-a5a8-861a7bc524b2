
import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Layout } from "@/components/Layout";
import { toast } from "sonner";
import { usePluginInstaller } from "@/hooks/usePluginDownloaderInstaller";
import { TitleBar } from "@/components/TitleBar";
import { LoadingWrapper } from "@/components/LoadingWrapper";
import { Spinner } from "@/components/ui/spinner";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, RefreshCw } from "lucide-react";
import { useLoading } from "@/hooks/useLoading";
type IDEStatus = "installed" | "not-installed" | "not-found" | "outdated";
type PluginStatus = "installed" | "not-installed" | "needs-update";

interface IDEInfo {
  ideId:string;
  name: string;
  version: string;
  status: IDEStatus;
  pluginStatus: PluginStatus;
  pluginVersion: string | null;
  lastUpdated: string | null;
}
type PluginUpdateInfo = {
  installedVersion: string | null;
  latestVersion: string;
  updateAvailable: boolean;
  downloadUrl: string;
};

type IDE = {
  ideId: string;
  name: string;
  version: string;
  status: "installed" | "not-installed";
  pluginStatus: "installed" | "not-installed" | "needs-update";
  pluginVersion: string | null;
};

type PluginMap = {
  [ideId: string]: string[] | null;
};

const IDEIntegrator = () => {
  // const [detectedIDEs, setDetectedIDEs] = useState([
  //   {
  //     name: "VS Code",
  //     version: "1.87.0",
  //     status: "installed",
  //     pluginStatus: "installed",
  //     pluginVersion: "1.2.3",
  //     lastUpdated: "2 days ago"
  //   },
  //   {
  //     name: "Cursor",
  //     version: "1.45.2",
  //     status: "installed",
  //     pluginStatus: "not-installed",
  //     pluginVersion: null,
  //     lastUpdated: null
  //   },
  //   {
  //     name: "WinSCP",
  //     version: "5.21.6",
  //     status: "outdated",
  //     pluginStatus: "needs-update",
  //     pluginVersion: "1.1.0",
  //     lastUpdated: "1 week ago"
  //   }
  // ]);

  const [detectedIDEs, setDetectedIDEs] = useState<IDEInfo[]>([]);
  const [pluginUpdateInfo, setPluginUpdateInfo] = useState<PluginUpdateInfo | null>(null);
  // const [loading, setLoading] = useState(true);

  useEffect(() => {
    scanIDEs();  
  }, []);

  const [loading, setLoading] = useState(true);
  const [plugins, setPlugins] = useState<PluginMap>({});
  const { isLoading: isScanning, withLoading } = useLoading();
  const navigate = useNavigate();

  const [pluginUpdateMap, setPluginUpdateMap] = useState<Record<string, PluginUpdateInfo>>({});
  

  const scanIDEs = async () => {
    setLoading(true);
    try {
      const rawIDEs = await window.api.scanIDEs();
      let normalized: IDE[] = rawIDEs.map((item: any) => ({
        ideId: item.ideId,
        name: item.name,
        version: item.version || "–",
        status: item.status === "installed" ? "installed" : "not-installed",
        pluginStatus: item.pluginStatus || "not-installed",
        pluginVersion: item.pluginVersion || null,
      }));
  
      // Dynamically get update info for each IDE
      const updateMap: Record<string, PluginUpdateInfo> = {};
      for (const ide of normalized) {
        const updateInfo = await window.api.checkPluginUpdate(ide.ideId).catch(() => null);
        if (updateInfo) updateMap[ide.ideId] = updateInfo;
      }
  
      // Normalize IDE list based on update map
      normalized = normalized.map((ide) => {
        const update = updateMap[ide.ideId];
        if (
          ide.status === "installed" &&
          update?.updateAvailable &&
          ide.pluginStatus === "installed"
        ) {
          return { ...ide, pluginStatus: "needs-update" };
        }
        return ide;
      });
  
      setDetectedIDEs(normalized as any);
      setPluginUpdateMap(updateMap);
  
      // Fetch plugin folder list per IDE
      for (const ide of normalized) {
        if (ide.status === "installed") {
          const idePlugins = await window.api.getIDEPlugins(ide.ideId).catch(() => []);
          setPlugins((prev) => ({ ...prev, [ide.ideId]: idePlugins || [] }));
        }
      }
    } catch (err) {
      console.error("IDE scan failed:", err);
      toast.error("Failed to scan IDEs");
      setDetectedIDEs([]);
    }
    setLoading(false);
  };


  const [showWizard, setShowWizard] = useState(false);
  const [wizardStep, setWizardStep] = useState(1);
  const [currentIDE, setCurrentIDE] = useState<string>("");
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadPath, setDownloadPath] = useState<string | null>(null);
  const [isInstalled, setIsInstalled] = useState(false);
  const [stepStatus, setStepStatus] = useState<"idle" | "downloading" | "downloaded" | "installing" | "installed">("idle");

  const reScanIDEs = async () => {
    await withLoading(async () => {
      console.log("Re-scanning IDEs...");
       await scanIDEs();
      // Simulate async operation
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log("IDE scan complete");
    });
  };

  const launchWizard = (ide: any) => {
    console.log("lunchWizard ",ide);
    setCurrentIDE(ide);
    setShowWizard(true);
    setWizardStep(1);
  };

  const completeWizard = () => {
    reScanIDEs();
    setShowWizard(false);
    setCurrentIDE("");
    setWizardStep(1);
  };

  const updatePlugin = async (ideId: string) => {
    console.log("updatePlugin");
    if (!pluginUpdateMap[ideId]?.downloadUrl) return;
      const downloadRes = await window.api.downloadPlugin(pluginUpdateMap[ideId].downloadUrl);
      if (downloadRes?.success && downloadRes.path) {
        const installRes = await window.api.installPlugin(downloadRes.path, ideId);
        if (installRes.success) {
          toast.success("Plugin Installed!");
          await scanIDEs();
        } else {
          console.error(`Install Failed: ${installRes.error}`);
          toast.error(`Install Failed: ${installRes.error}`);
        }
      } else {
        toast.error(`Download Failed: ${downloadRes.error}`);
      }
  };


  // const handleNextStep = async () => {
  //   if (wizardStep === 1) {
  //     // Move from Intro to Download step

  //     setWizardStep(2);
  //   } 
    
  //   else if (wizardStep === 2) {
  //     setIsDownloading(true);

  //     try {
  //       if (pluginUpdateInfo?.downloadUrl) {
  //         // Simulate download delay (e.g., 2 seconds)
  //         setTimeout(async () => {
  //           const result = await window.api.downloadPlugin(pluginUpdateInfo.downloadUrl);
  //           console.log("Plugin downloaded result:", result);

  //           if (result.success && result.path) {
  //             const pluginPath = result.path; // `pluginPath` is now a definite string
  //             setDownloadPath(pluginPath);

  //             // Simulate install delay (e.g., 1.5 seconds)
  //             setTimeout(async () => {
  //               try {
  //                 console.log("Installing plugin from path:", pluginPath);
  //                 const installResult = await window.api.installPlugin(pluginPath);
  //                 console.log("Plugin install result:", installResult);

  //                 if (installResult.success) {
  //                   setIsInstalled(true);
  //                   setWizardStep(3); // Go to completion step
  //                 } else {
  //                   throw new Error(installResult.error || "Installation failed");
  //                 }
  //               } catch (installErr) {
  //                 console.error("Installation failed:", installErr);
  //               } finally {
  //                 setIsDownloading(false);
  //               }
  //             }, 1500); // install simulation delay
  //           } else {
  //             throw new Error(result.error || "Plugin download failed");
  //           }
  //         }, 2000); // download simulation delay
  //       } else {
  //         throw new Error("Missing download URL");
  //       }
  //     } catch (err) {
  //       console.error("Download failed:", err);
  //       setIsDownloading(false);
  //     }
  //   }

  //   else if (wizardStep === 3) {
  //     completeWizard();
  //   }
  // };

  const handleNextStep = async () => {
  
  if (wizardStep === 1) {
    console.log("Plugin downloading:");
    setWizardStep(2);
    setIsDownloading(true);
    setStepStatus("downloading");

    try {
      if (pluginUpdateMap[currentIDE]?.downloadUrl) {
        setTimeout(async () => {
          const result = await window.api.downloadPlugin(pluginUpdateMap[currentIDE].downloadUrl);
          console.log("Plugin downloaded result:", result);
          setStepStatus("downloaded");
          if (result.success && result.path) {
            const pluginPath = result.path;
            setDownloadPath(pluginPath);
            setStepStatus("installing");

            setTimeout(async () => {
              try {
                const installResult = await window.api.installPlugin(pluginPath, currentIDE);
                if (installResult.success) {
                  setIsInstalled(true);
                  console.log("Plugin installed result:", result);
                  setStepStatus("installed");
                  setWizardStep(3);
                } else {
                  throw new Error(installResult.error);
                }
              } catch (installErr) {
                console.error("Installation failed:", installErr);
              } finally {
                setIsDownloading(false);
              }
            }, 1500);
          } else {
            throw new Error(result.error || "Download failed");
          }
        }, 2000);
      }
    } catch (err) {
      console.error("Download failed:", err);
      setIsDownloading(false);
    }
  }
  if (wizardStep === 2) {
    setWizardStep(3);
  } 
  else if (wizardStep === 3) {
    completeWizard();
  }
};

// const {
//     handleDownloadAndInstall,
//     isDownloading,
//     activeIdeId
//   } = usePluginInstaller({
//     pluginUpdateMap,
//     refreshData: scanIDEs,
//     onProgress: (status, path) => {
//       setStepStatus(status as any);
//       if (status === "downloaded" && path) setDownloadPath(path);
//       if (status === "installed") {
//         setIsInstalled(true);
//         setWizardStep(3); // Auto-move to next step after install
//       }
//     },
//   });

//   const handleNextStep = async () => {
//     if (wizardStep === 1) {
//       setWizardStep(2);
//       await handleDownloadAndInstall(currentIDE);
//       return;
//     }

//     if (wizardStep === 2) {
//       setWizardStep(3);
//       return;
//     }

//     if (wizardStep === 3) {
//       completeWizard();
//     }
//   };



  const getStatusBadge = (status: string) => {
    switch (status) {
      case "installed":
        return <Badge className="bg-green-600">✅ Installed</Badge>;
      case "not-installed":
        return <Badge className="bg-red-600">❌ Not Found</Badge>;
      case "outdated":
        return <Badge className="bg-yellow-600">⚠️ Outdated</Badge>;
      default:
        return <Badge className="bg-gray-600">Unknown</Badge>;
    }
  };

  const getPluginStatusBadge = (status: string) => {
    switch (status) {
      case "installed":
        return <Badge className="bg-green-600">✅ Plugin Active</Badge>;
      case "not-installed":
        return <Badge className="bg-red-600">❌ Plugin Missing</Badge>;
      case "needs-update":
        return <Badge className="bg-yellow-600">⚠️ Plugin Outdated</Badge>;
      default:
        return <Badge className="bg-gray-600">Unknown</Badge>;
    }
  };

  const hasInstalledIDEs = detectedIDEs.some(ide => ide.status === "installed" || ide.status === "outdated");

  // if (showWizard) {
  //   return (
  //     <div className="min-h-screen bg-slate-900 text-white flex flex-col justify-center p-4">

  //       <div className="max-w-2xl mx-auto w-full">
  //         <div className="text-center mb-6">
  //           <h1 className="text-2xl font-bold text-white mb-2">Alpine Intellect Plugin Setup</h1>
  //           <p className="text-slate-400 mb-4">Installing plugin for {currentIDE}</p>
  //           <div className="flex justify-center items-center space-x-2 mb-4">
  //             {[1, 2, 3].map((step) => (
  //               <div key={step} className="flex items-center">
  //                 <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
  //                   wizardStep >= step ? 'bg-blue-600 text-white' : 'bg-slate-700 text-slate-400'
  //                 }`}>
  //                   {step}
  //                 </div>
  //                 {step < 3 && <div className={`w-8 h-px mx-2 ${wizardStep > step ? 'bg-blue-600' : 'bg-slate-600'}`} />}
  //               </div>
  //             ))}
  //           </div>
  //         </div>

  //         <Card className="bg-slate-800 border-slate-700">
  //           <CardHeader>
  //             <CardTitle className="text-white text-center">
  //               {wizardStep === 1 && "Intro & Benefits"}
  //               {wizardStep === 2 && "Plugin Installation"}
  //               {wizardStep === 3 && "Completion"}
  //             </CardTitle>
  //           </CardHeader>
  //           <CardContent className="text-center space-y-4">
  //             {wizardStep === 1 && (
  //               <div>
  //                 <p className="text-slate-300 mb-4">
  //                   Alpine Intellect plugins enhance your IDE with AI-powered development features.
  //                 </p>
  //                 <ul className="text-left text-slate-300 space-y-2 mb-6">
  //                   <li>• Code completion and suggestions</li>
  //                   <li>• Intelligent refactoring</li>
  //                   <li>• Real-time code analysis</li>
  //                   <li>• Seamless project integration</li>
  //                 </ul>
  //               </div>
  //             )}

  //             {wizardStep === 2 && (
  //               <div>
  //                 <p className="text-slate-300 mb-4">Installing Alpine Intellect plugin for {currentIDE}...</p>
  //                 <div className="space-y-2">
  //                   <div className="flex items-center justify-between p-3 bg-slate-700 rounded">
  //                     <span>{currentIDE}</span>
  //                     {stepStatus === "downloading" && <Badge className="bg-blue-600">Downloading...</Badge>}
  //                     {stepStatus === "installing" && <Badge className="bg-yellow-600">Installing...</Badge>}
  //                     {stepStatus === "installed" && <Badge className="bg-green-600">Installation Done</Badge>}
  //                   </div>
  //                 </div>
  //                 <div className="mt-4 p-3 bg-slate-700 rounded text-left text-sm text-slate-300">
  //                   <p className="mb-2">Installation steps:</p>
  //                   <div className="space-y-1">

  //                     {/* {isDownloading && !downloadPath ? (
  //                         <div className="flex items-center space-x-2">
  //                         <span className="text-green-400">✓</span>
  //                         <span>Downloading plugin package</span>
  //                       </div>
  //                     ):
  //                     (
  //                         <div className="flex items-center space-x-2">
  //                         <span className="text-green-400">✓</span>
  //                         <span>Downloaded plugin package</span>
  //                       </div>
  //                     )}

  //                     {(downloadPath && !isInstalled) ? (
  //                         <div className="flex items-center space-x-2">
  //                         <span className="text-green-400">✓</span>
  //                         <span>Installing Plugin</span>
  //                       </div>
  //                     ):(<div className="flex items-center space-x-2">
  //                         <span className="text-green-400">✓</span>
  //                         <span>Installed Plugin</span>
  //                       </div>)} */}

  //                       {stepStatus === "downloading" && (
  //                       <div className="flex items-center space-x-2 animate-pulse">
  //                         <span className="text-yellow-400">⬇️</span>
  //                         <span>Downloading plugin package...</span>
  //                       </div>
  //                     )}

  //                     {stepStatus === "downloaded" && (
  //                       <div className="flex items-center space-x-2">
  //                         <span className="text-green-400">✓</span>
  //                         <span>Downloaded plugin package</span>
  //                       </div>
  //                     )}

  //                     {stepStatus === "installing" && (
  //                       <div className="flex items-center space-x-2 animate-pulse">
  //                         <span className="text-yellow-400">⚙️</span>
  //                         <span>Installing plugin...</span>
  //                       </div>
  //                     )}

  //                     {stepStatus === "installed" && (
  //                       <div className="flex items-center space-x-2">
  //                         <span className="text-green-400">✓</span>
  //                         <span>Installed plugin</span>
  //                       </div>
  //                     )}
  //                   </div>
  //                 </div>
  //               </div>
  //             )}

  //             {wizardStep === 3 && (
  //               <div>
  //                 <div className="text-6xl mb-4">🎉</div>
  //                 <p className="text-slate-300 mb-4">
  //                   Alpine Intellect plugin has been successfully installed for {currentIDE}!
  //                 </p>
  //                 <div className="bg-slate-700 p-3 rounded mb-4">
  //                   <p className="text-green-400 text-sm font-medium mb-2">Installation Complete</p>
  //                   <p className="text-slate-300 text-sm">
  //                     Your {currentIDE} IDE is now ready to use Alpine Intellect features.
  //                   </p>
  //                 </div>
  //               </div>
  //             )}

  //             <div className="flex justify-between pt-4">
  //               <Button
  //                 variant="outline"
  //                 onClick={() => setWizardStep(Math.max(1, wizardStep - 1))}
  //                 disabled={wizardStep === 1}
  //                 className="border-slate-600 bg-slate-700 text-slate-300 hover:bg-slate-600"
  //               >
  //                 Previous
  //               </Button>

  //               {wizardStep < 3 ? (
  //                 <Button
  //                   onClick={() => handleNextStep()}
  //                   className="bg-blue-600 hover:bg-blue-700"
  //                 >
  //                   {wizardStep === 1 ? "Start Installation" : "Continue"}
  //                 </Button>
  //               ) : (
  //                 <Button
  //                   onClick={completeWizard}
  //                   className="bg-green-600 hover:bg-green-700"
  //                 >
  //                   Finish Setup
  //                 </Button>
  //               )}
  //             </div>
  //           </CardContent>
  //         </Card>
  //       </div>
  //     </div>
  //   );
  // }

  if (showWizard) {
    return (
      <div className="min-h-screen bg-slate-900 text-white flex flex-col">

        {/* Title Bar */}
        <TitleBar title={`Alpine Intellect - Plugin Setup (${currentIDE})`} />
        
        <div className="flex-1 flex flex-col justify-center p-4">
          {/* Back Button */}
          <div className="mb-4">
            <Button
              variant="ghost"
              onClick={() => navigate("/dashboard")}
              className="text-slate-300 hover:text-white hover:bg-slate-700"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>

        <div className="max-w-2xl mx-auto w-full">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-white mb-2">Alpine Intellect Plugin Setup</h1>
            <p className="text-slate-400 mb-4">Installing plugin for {currentIDE}</p>
            <div className="flex justify-center items-center space-x-2 mb-4">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    wizardStep >= step ? 'bg-blue-600 text-white' : 'bg-slate-700 text-slate-400'
                  }`}>
                    {step}
                  </div>
                  {step < 3 && <div className={`w-8 h-px mx-2 ${wizardStep > step ? 'bg-blue-600' : 'bg-slate-600'}`} />}
                </div>
              ))}
            </div>
          </div>

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white text-center">
                {wizardStep === 1 && "Intro & Benefits"}
                {wizardStep === 2 && "Plugin Installation"}
                {wizardStep === 3 && "Completion"}
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              {wizardStep === 1 && (
                <div>
                  <p className="text-slate-300 mb-4">
                    Alpine Intellect plugins enhance your IDE with AI-powered development features.
                  </p>
                  <ul className="text-left text-slate-300 space-y-2 mb-6">
                    <li>• Code completion and suggestions</li>
                    <li>• Intelligent refactoring</li>
                    <li>• Real-time code analysis</li>
                    <li>• Seamless project integration</li>
                  </ul>
                </div>
              )}

              {wizardStep === 2 && (
                <div>
                  <p className="text-slate-300 mb-4">Installing Alpine Intellect plugin for {currentIDE}...</p>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 bg-slate-700 rounded">
                      <span>{currentIDE}</span>
                      {stepStatus === "downloading" && <Badge className="bg-blue-600">Downloading...</Badge>}
                      {stepStatus === "installing" && <Badge className="bg-yellow-600">Installing...</Badge>}
                      {stepStatus === "installed" && <Badge className="bg-green-600">Installation Done</Badge>}
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-slate-700 rounded text-left text-sm text-slate-300">
                    <p className="mb-2">Installation steps:</p>
                    <div className="space-y-1">

                      {/* {isDownloading && !downloadPath ? (
                          <div className="flex items-center space-x-2">
                          <span className="text-green-400">✓</span>
                          <span>Downloading plugin package</span>
                        </div>
                      ):
                      (
                          <div className="flex items-center space-x-2">
                          <span className="text-green-400">✓</span>
                          <span>Downloaded plugin package</span>
                        </div>
                      )}

                      {(downloadPath && !isInstalled) ? (
                          <div className="flex items-center space-x-2">
                          <span className="text-green-400">✓</span>
                          <span>Installing Plugin</span>
                        </div>
                      ):(<div className="flex items-center space-x-2">
                          <span className="text-green-400">✓</span>
                          <span>Installed Plugin</span>
                        </div>)} */}

                        {stepStatus === "downloading" && (
                        <div className="flex items-center space-x-2 animate-pulse">
                          <span className="text-yellow-400">⬇️</span>
                          <span>Downloading plugin package...</span>
                        </div>
                      )}

                      {stepStatus === "downloaded" && (
                        <div className="flex items-center space-x-2">
                          <span className="text-green-400">✓</span>
                          <span>Downloaded plugin package</span>
                        </div>
                      )}

                      {stepStatus === "installing" && (
                        <div className="flex items-center space-x-2 animate-pulse">
                          <span className="text-yellow-400">⚙️</span>
                          <span>Installing plugin...</span>
                        </div>
                      )}

                      {stepStatus === "installed" && (
                        <div className="flex items-center space-x-2">
                          <span className="text-green-400">✓</span>
                          <span>Installed plugin</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {wizardStep === 3 && (
                <div>
                  <div className="text-6xl mb-4">🎉</div>
                  <p className="text-slate-300 mb-4">
                    Alpine Intellect plugin has been successfully installed for {currentIDE}!
                  </p>
                  <div className="bg-slate-700 p-3 rounded mb-4">
                    <p className="text-green-400 text-sm font-medium mb-2">Installation Complete</p>
                    <p className="text-slate-300 text-sm">
                      Your {currentIDE} IDE is now ready to use Alpine Intellect features.
                    </p>
                  </div>
                </div>
              )}

              <div className="flex justify-between pt-4">
                <Button
                  variant="outline"
                  onClick={() => setWizardStep(Math.max(1, wizardStep - 1))}
                  disabled={wizardStep === 1}
                  className="border-slate-600 bg-slate-700 text-slate-300 hover:bg-slate-600"
                >
                  Previous
                </Button>

                {wizardStep < 3 ? (
                  <Button
                    onClick={() => handleNextStep()}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {wizardStep === 1 ? "Start Installation" : "Continue"}
                  </Button>
                ) : (
                  <Button
                    onClick={completeWizard}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    Finish Setup
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      </div>
    );
  }

  return (
    <Layout>
      <LoadingWrapper
        isLoading={loading || isScanning}
        skeletonProps={{
          variant: "ide-integrator"
        }}
      >
        <div className="p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">IDE Integrator</h1>
              <p className="text-slate-400">Alpine Intellect – IDE Integration Module</p>
            </div>
            <Button
              onClick={reScanIDEs}
              disabled={isScanning}
              className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            >
              {isScanning ? (
                <Spinner size="sm" className="mr-2" />
              ) : (
                <RefreshCw className="w-4 h-4 mr-2" />
              )}
              {isScanning ? "Scanning..." : "Re-scan IDEs"}
            </Button>
          </div>

          {!hasInstalledIDEs ? (
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="text-center py-12">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-semibold text-white mb-4">
                  No Supported IDEs Detected
                </h3>
                <p className="text-slate-400 mb-6">
                  Please install VS Code or Cursor to use Alpine Intellect features.
                </p>
                <div className="flex justify-center space-x-4 mb-6">
                  <a
                    href="https://code.visualstudio.com/"
                    target="_blank"
                    className="text-blue-400 hover:text-blue-300 underline"
                  >
                    Download VS Code
                  </a>
                  <a
                    href="https://www.cursor.so/"
                    target="_blank"
                    className="text-blue-400 hover:text-blue-300 underline"
                  >
                    Download Cursor
                  </a>
                </div>
                <Button
                  onClick={reScanIDEs}
                  disabled={isScanning}
                  className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  {isScanning ? (
                    <Spinner size="sm" className="mr-2" />
                  ) : (
                    <RefreshCw className="w-4 h-4 mr-2" />
                  )}
                  {isScanning ? "Scanning..." : "Re-scan IDEs"}
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {detectedIDEs.map((ide) => (
                <Card key={ide.name} className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center justify-between">
                      <span>{ide.name}</span>
                      {getStatusBadge(ide.status)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div>
                        <p className="text-slate-400 text-sm">IDE Version</p>
                        <p className="text-white">{ide.version || "Not Installed"}</p>
                      </div>

                      <div>
                        <p className="text-slate-400 text-sm">Plugin Status</p>
                        <div className="flex items-center space-x-2 mb-2">
                          {getPluginStatusBadge(ide.pluginStatus)}
                        </div>
                        {ide.pluginStatus === "not-installed" &&
                          ide.status === "installed" && (
                            <div className="p-2 bg-blue-900/30 border border-blue-700 rounded text-sm">
                              <p className="text-blue-300 mb-2">
                                We detected {ide.name} but the Alpine Intellect plugin is not installed.
                              </p>
                              <p className="text-blue-200 text-xs">
                                Click "Install Plugin" to begin the setup process.
                              </p>
                            </div>
                          )}
                      </div>

                      {ide.pluginVersion && (
                        <div>
                          <p className="text-slate-400 text-sm">Plugin Version</p>
                          <p className="text-white">{ide.pluginVersion}</p>
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      {ide.status === "not-installed" ? (
                        <a
                          href={
                            ide.name === "VS Code"
                              ? "https://code.visualstudio.com/"
                              : "https://www.cursor.so/"
                          }
                          target="_blank"
                          className="block w-full"
                        >
                          <Button className="w-full bg-blue-600 hover:bg-blue-700">
                            Install {ide.name}
                          </Button>
                        </a>
                      ) : (
                        <div className="space-y-2">
                          {ide.pluginStatus === "not-installed" && (
                            <Button
                              onClick={() => launchWizard(ide.ideId)}
                              className="w-full bg-blue-600 hover:bg-blue-700 font-medium"
                            >
                              🔧 Install Plugin
                            </Button>
                          )}
                          {ide.pluginStatus === "needs-update" && (
                            <Button
                              onClick={() => updatePlugin(ide.ideId)}
                              className="w-full bg-yellow-600 hover:bg-yellow-700"
                            >
                              Update Plugin
                            </Button>
                          )}
                        </div>
                      )}
                    </div>

                    {ide.lastUpdated && (
                      <div className="pt-2 border-t border-slate-700">
                        <p className="text-slate-400 text-xs">
                          Last updated: {ide.lastUpdated}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </LoadingWrapper>
    </Layout>
  );
};

export default IDEIntegrator;