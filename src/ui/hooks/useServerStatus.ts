import { useState, useEffect, useCallback } from 'react';

interface ServerStatus {
  status: 'stopped' | 'starting' | 'running' | 'error' | 'connecting' | 'connected' | 'disconnected';
  pid?: number;
  uptime?: number;
  lastError?: string;
  lastSeen?: Date;
  health: {
    responsive: boolean;
    latency: number;
    errorRate: number;
  };
  resources?: {
    cpu: number;
    memory: number;
  };
  metrics?: {
    requestCount: number;
    successRate: number;
    avgResponseTime: number;
  };
}

interface UseServerStatusReturn {
  serverStatuses: Record<string, ServerStatus>;
  loading: boolean;
  error: string | null;
  refreshStatus: () => Promise<void>;
  getServerHealth: (serverId: string) => 'healthy' | 'warning' | 'error' | 'unknown';
}

export const useServerStatus = (connectedServers: string[]): UseServerStatusReturn => {
  const [serverStatuses, setServerStatuses] = useState<Record<string, ServerStatus>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load server statuses from the backend
  const loadServerStatuses = useCallback(async () => {
    if (connectedServers.length === 0) {
      setServerStatuses({});
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const statuses: Record<string, ServerStatus> = {};

      // Get status for each connected server
      for (const serverId of connectedServers) {
        try {
          const status = await window.api?.getMCPServerStatus?.(serverId);
          if (status) {
            statuses[serverId] = status;
          }
        } catch (err) {
          console.error(`Failed to get status for server ${serverId}:`, err);
          // Set error status for this server
          statuses[serverId] = {
            status: 'error',
            lastError: err instanceof Error ? err.message : 'Unknown error',
            health: {
              responsive: false,
              latency: 0,
              errorRate: 1.0
            }
          };
        }
      }

      setServerStatuses(statuses);
    } catch (err: any) {
      console.error('Failed to load server statuses:', err);
      setError(err.message || 'Failed to load server statuses');
    } finally {
      setLoading(false);
    }
  }, [connectedServers]);

  // Refresh server statuses
  const refreshStatus = useCallback(async () => {
    await loadServerStatuses();
  }, [loadServerStatuses]);

  // Determine server health based on status and metrics
  const getServerHealth = useCallback((serverId: string): 'healthy' | 'warning' | 'error' | 'unknown' => {
    const status = serverStatuses[serverId];
    if (!status) return 'unknown';

    // Error states
    if (status.status === 'error' || status.status === 'disconnected') {
      return 'error';
    }

    // Not responsive
    if (!status.health.responsive) {
      return 'error';
    }

    // High error rate
    if (status.health.errorRate > 0.1) {
      return 'error';
    }

    // High latency or moderate error rate
    if (status.health.latency > 2000 || status.health.errorRate > 0.05) {
      return 'warning';
    }

    // All good
    if (status.status === 'connected' || status.status === 'running') {
      return 'healthy';
    }

    return 'unknown';
  }, [serverStatuses]);

  // Load statuses when connected servers change
  useEffect(() => {
    loadServerStatuses();
  }, [loadServerStatuses]);

  // Set up periodic status updates
  useEffect(() => {
    if (connectedServers.length === 0) return;

    const interval = setInterval(() => {
      loadServerStatuses();
    }, 15000); // Update every 15 seconds

    return () => clearInterval(interval);
  }, [connectedServers, loadServerStatuses]);

  // Mock data for development (remove when backend is ready)
  useEffect(() => {
    if (!window.api?.getMCPServerStatus && connectedServers.length > 0) {
      const mockStatuses: Record<string, ServerStatus> = {};

      connectedServers.forEach(serverId => {
        switch (serverId) {
          case 'github':
            mockStatuses[serverId] = {
              status: 'connected',
              pid: 12345,
              uptime: 3600000, // 1 hour
              lastSeen: new Date(),
              health: {
                responsive: true,
                latency: 120,
                errorRate: 0.02
              },
              resources: {
                cpu: 5.2,
                memory: 45.8
              },
              metrics: {
                requestCount: 156,
                successRate: 0.98,
                avgResponseTime: 120
              }
            };
            break;
          case 'slack':
            mockStatuses[serverId] = {
              status: 'error',
              lastError: 'Authentication failed',
              lastSeen: new Date(Date.now() - 300000), // 5 minutes ago
              health: {
                responsive: false,
                latency: 5000,
                errorRate: 0.15
              },
              resources: {
                cpu: 0,
                memory: 0
              },
              metrics: {
                requestCount: 23,
                successRate: 0.85,
                avgResponseTime: 2500
              }
            };
            break;
          case 'figma':
            mockStatuses[serverId] = {
              status: 'connected',
              pid: 12346,
              uptime: 1800000, // 30 minutes
              lastSeen: new Date(),
              health: {
                responsive: true,
                latency: 800,
                errorRate: 0.01
              },
              resources: {
                cpu: 2.1,
                memory: 32.4
              },
              metrics: {
                requestCount: 45,
                successRate: 0.99,
                avgResponseTime: 800
              }
            };
            break;
          default:
            mockStatuses[serverId] = {
              status: 'connected',
              health: {
                responsive: true,
                latency: 200,
                errorRate: 0.01
              }
            };
        }
      });

      setServerStatuses(mockStatuses);
      setLoading(false);
    }
  }, [connectedServers]);

  return {
    serverStatuses,
    loading,
    error,
    refreshStatus,
    getServerHealth
  };
};
