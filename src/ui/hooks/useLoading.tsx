
import { useState } from "react"

export const useLoading = (initialState: boolean = false) => {
  const [isLoading, setIsLoading] = useState(initialState)

  const startLoading = () => setIsLoading(true)
  const stopLoading = () => setIsLoading(false)
  const toggleLoading = () => setIsLoading(prev => !prev)

  const withLoading = async <T,>(asyncFn: () => Promise<T>): Promise<T> => {
    try {
      startLoading()
      return await asyncFn()
    } finally {
      stopLoading()
    }
  }

  return {
    isLoading,
    startLoading,
    stopLoading,
    toggleLoading,
    withLoading
  }
}