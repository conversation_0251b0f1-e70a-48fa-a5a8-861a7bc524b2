import { useState, useEffect, useCallback } from 'react';

interface MCPServer {
  id: string;
  name: string;
  description: string;
  category: string;
  status: 'stopped' | 'starting' | 'running' | 'error' | 'connected' | 'disconnected';
  installed: boolean;
  configured: boolean;
  version?: string;
  author?: string;
  capabilities: string[];
  toolsCount: number;
  lastSeen?: Date;
  health?: {
    responsive: boolean;
    latency: number;
    errorRate: number;
  };
}

interface UseMCPServersReturn {
  servers: MCPServer[];
  connectedServers: string[];
  loading: boolean;
  error: string | null;
  installServer: (serverId: string, config?: any) => Promise<void>;
  configureServer: (serverId: string, config: any) => Promise<void>;
  connectServer: (serverId: string) => Promise<void>;
  disconnectServer: (serverId: string) => Promise<void>;
  startServer: (serverId: string) => Promise<void>;
  stopServer: (serverId: string) => Promise<void>;
  removeServer: (serverId: string) => Promise<void>;
  refreshServers: () => Promise<void>;
}

export const useMCPServers = (): UseMCPServersReturn => {
  const [servers, setServers] = useState<MCPServer[]>([]);
  const [connectedServers, setConnectedServers] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load available servers from the backend
  const loadServers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Get available servers from the backend
      const availableServers = await window.api?.getMCPServers?.() || [];
      
      // Get connected servers
      const connected = await window.api?.getConnectedMCPServers?.() || [];
      
      // Merge server data with connection status
      const serversWithStatus = availableServers.map((server: any) => ({
        ...server,
        status: connected.includes(server.id) ? 'connected' : 'disconnected',
        toolsCount: server.toolsCount || 0
      }));

      setServers(serversWithStatus);
      setConnectedServers(connected);
    } catch (err: any) {
      console.error('Failed to load MCP servers:', err);
      setError(err.message || 'Failed to load MCP servers');
    } finally {
      setLoading(false);
    }
  }, []);

  // Install a new MCP server
  const installServer = useCallback(async (serverId: string, config?: any) => {
    try {
      const result = await window.api?.installMCPServer?.(serverId, config);
      if (!result?.success) {
        throw new Error(result?.error || 'Installation failed');
      }

      // Update server status
      setServers(prev => prev.map(server => 
        server.id === serverId 
          ? { ...server, installed: true, configured: !!config }
          : server
      ));
    } catch (err: any) {
      console.error('Failed to install server:', err);
      throw err;
    }
  }, []);

  // Configure an MCP server
  const configureServer = useCallback(async (serverId: string, config: any) => {
    try {
      const result = await window.api?.configureMCPServer?.(serverId, config);
      if (!result?.success) {
        throw new Error(result?.error || 'Configuration failed');
      }

      // Update server status
      setServers(prev => prev.map(server => 
        server.id === serverId 
          ? { ...server, configured: true }
          : server
      ));
    } catch (err: any) {
      console.error('Failed to configure server:', err);
      throw err;
    }
  }, []);

  // Connect to an MCP server
  const connectServer = useCallback(async (serverId: string) => {
    try {
      // Update status optimistically
      setServers(prev => prev.map(server => 
        server.id === serverId 
          ? { ...server, status: 'starting' }
          : server
      ));

      const result = await window.api?.connectMCPServer?.(serverId);
      if (!result?.success) {
        throw new Error(result?.error || 'Connection failed');
      }

      // Update connected servers list
      setConnectedServers(prev => [...prev, serverId]);
      setServers(prev => prev.map(server => 
        server.id === serverId 
          ? { ...server, status: 'connected', lastSeen: new Date() }
          : server
      ));
    } catch (err: any) {
      // Revert optimistic update
      setServers(prev => prev.map(server => 
        server.id === serverId 
          ? { ...server, status: 'error' }
          : server
      ));
      console.error('Failed to connect to server:', err);
      throw err;
    }
  }, []);

  // Disconnect from an MCP server
  const disconnectServer = useCallback(async (serverId: string) => {
    try {
      const result = await window.api?.disconnectMCPServer?.(serverId);
      if (!result?.success) {
        throw new Error(result?.error || 'Disconnection failed');
      }

      // Update connected servers list
      setConnectedServers(prev => prev.filter(id => id !== serverId));
      setServers(prev => prev.map(server => 
        server.id === serverId 
          ? { ...server, status: 'disconnected' }
          : server
      ));
    } catch (err: any) {
      console.error('Failed to disconnect from server:', err);
      throw err;
    }
  }, []);

  // Start an MCP server process
  const startServer = useCallback(async (serverId: string) => {
    try {
      setServers(prev => prev.map(server => 
        server.id === serverId 
          ? { ...server, status: 'starting' }
          : server
      ));

      const result = await window.api?.startMCPServer?.(serverId);
      if (!result?.success) {
        throw new Error(result?.error || 'Failed to start server');
      }

      setServers(prev => prev.map(server => 
        server.id === serverId 
          ? { ...server, status: 'running' }
          : server
      ));
    } catch (err: any) {
      setServers(prev => prev.map(server => 
        server.id === serverId 
          ? { ...server, status: 'error' }
          : server
      ));
      console.error('Failed to start server:', err);
      throw err;
    }
  }, []);

  // Stop an MCP server process
  const stopServer = useCallback(async (serverId: string) => {
    try {
      const result = await window.api?.stopMCPServer?.(serverId);
      if (!result?.success) {
        throw new Error(result?.error || 'Failed to stop server');
      }

      setServers(prev => prev.map(server => 
        server.id === serverId 
          ? { ...server, status: 'stopped' }
          : server
      ));
    } catch (err: any) {
      console.error('Failed to stop server:', err);
      throw err;
    }
  }, []);

  // Remove an MCP server
  const removeServer = useCallback(async (serverId: string) => {
    try {
      const result = await window.api?.removeMCPServer?.(serverId);
      if (!result?.success) {
        throw new Error(result?.error || 'Failed to remove server');
      }

      // Remove from connected servers if present
      setConnectedServers(prev => prev.filter(id => id !== serverId));
      
      // Update server status
      setServers(prev => prev.map(server => 
        server.id === serverId 
          ? { ...server, installed: false, configured: false, status: 'disconnected' }
          : server
      ));
    } catch (err: any) {
      console.error('Failed to remove server:', err);
      throw err;
    }
  }, []);

  // Refresh servers data
  const refreshServers = useCallback(async () => {
    await loadServers();
  }, [loadServers]);

  // Load servers on mount
  useEffect(() => {
    loadServers();
  }, [loadServers]);

  // Mock data for development (remove when backend is ready)
  useEffect(() => {
    if (!window.api?.getMCPServers) {
      const mockServers: MCPServer[] = [
        {
          id: 'github',
          name: 'GitHub Integration',
          description: 'Repository management and GitHub API access',
          category: 'development',
          status: 'connected',
          installed: true,
          configured: true,
          version: '1.0.0',
          author: 'ModelContextProtocol',
          capabilities: ['repository_management', 'issue_tracking', 'pull_requests'],
          toolsCount: 8,
          lastSeen: new Date(),
          health: {
            responsive: true,
            latency: 120,
            errorRate: 0.02
          }
        },
        {
          id: 'figma',
          name: 'Figma Design System',
          description: 'Access design tokens and components from Figma',
          category: 'design',
          status: 'disconnected',
          installed: false,
          configured: false,
          version: '0.9.0',
          author: 'Figma Inc.',
          capabilities: ['design_tokens', 'component_specs', 'asset_export'],
          toolsCount: 0
        },
        {
          id: 'slack',
          name: 'Slack Integration',
          description: 'Team communication and workspace management',
          category: 'communication',
          status: 'error',
          installed: true,
          configured: true,
          version: '1.2.0',
          author: 'Slack Technologies',
          capabilities: ['messaging', 'channel_management', 'user_management'],
          toolsCount: 5,
          health: {
            responsive: false,
            latency: 5000,
            errorRate: 0.15
          }
        }
      ];

      setServers(mockServers);
      setConnectedServers(['github']);
      setLoading(false);
    }
  }, []);

  return {
    servers,
    connectedServers,
    loading,
    error,
    installServer,
    configureServer,
    connectServer,
    disconnectServer,
    startServer,
    stopServer,
    removeServer,
    refreshServers
  };
};
