import { useState } from "react";
import { toast } from "react-toastify";

type PluginUpdateInfo = {
  downloadUrl: string;
};

export function usePluginInstaller({
  pluginUpdateMap,
  refreshData,
  onProgress,
}: {
  pluginUpdateMap: Record<string, PluginUpdateInfo>;
  refreshData: () => Promise<void>;
  onProgress?: (status: string, pluginPath?: string) => void;
}) {
  const [installingIDEId, setInstallingIDEId] = useState<string | null>(null);
  const [downloading, setDownloading] = useState(false);

  const handleDownloadAndInstall = async (ideId: string) => {
    const pluginInfo = pluginUpdateMap[ideId];
    if (!pluginInfo?.downloadUrl) return;

    setInstallingIDEId(ideId);
    setDownloading(true);
    onProgress?.("downloading");

    try {
      const downloadRes = await window.api.downloadPlugin(pluginInfo.downloadUrl);
      if (!downloadRes?.success || !downloadRes.path) {
        throw new Error(downloadRes?.error || "Download failed");
      }

      onProgress?.("downloaded", downloadRes.path);

      const installRes = await window.api.installPlugin(downloadRes.path, ideId);
      if (installRes.success) {
        toast.success(`✅ Plugin installed for ${ideId}`);
        onProgress?.("installed");
        await refreshData();
      } else {
        throw new Error(installRes?.error || "Installation failed");
      }
    } catch (err: any) {
      onProgress?.("error");
      toast.error(`❌ ${err.message}`);
    } finally {
      setDownloading(false);
      setInstallingIDEId(null);
    }
  };

  return {
    handleDownloadAndInstall,
    isDownloading: downloading,
    activeIdeId: installingIDEId,
  };
}
