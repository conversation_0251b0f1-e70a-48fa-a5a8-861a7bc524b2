import { useState, useEffect, useCallback } from 'react';

interface ToolParameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  default?: any;
  enum?: string[];
}

interface ToolExample {
  name: string;
  description: string;
  parameters: Record<string, any>;
  expectedResult?: string;
}

interface Tool {
  id: string;
  name: string;
  description: string;
  serverId: string;
  serverName: string;
  category: string;
  tags: string[];
  parameters: ToolParameter[];
  examples: ToolExample[];
  usageCount: number;
  lastUsed?: Date;
  averageExecutionTime?: number;
  successRate?: number;
}

interface UseToolsDiscoveryReturn {
  tools: Tool[];
  loading: boolean;
  error: string | null;
  refreshTools: () => Promise<void>;
  executeTool: (toolId: string, parameters: Record<string, any>) => Promise<any>;
  getToolsByServer: (serverId: string) => Tool[];
  getToolsByCategory: (category: string) => Tool[];
  searchTools: (query: string) => Tool[];
}

export const useToolsDiscovery = (connectedServers: string[]): UseToolsDiscoveryReturn => {
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load available tools from connected servers
  const loadTools = useCallback(async () => {
    if (connectedServers.length === 0) {
      setTools([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const allTools: Tool[] = [];

      // Get tools from each connected server
      for (const serverId of connectedServers) {
        try {
          const serverTools = await window.api?.getMCPServerTools?.(serverId);
          if (serverTools && Array.isArray(serverTools)) {
            allTools.push(...serverTools);
          }
        } catch (err) {
          console.error(`Failed to get tools from server ${serverId}:`, err);
        }
      }

      setTools(allTools);
    } catch (err: any) {
      console.error('Failed to load tools:', err);
      setError(err.message || 'Failed to load tools');
    } finally {
      setLoading(false);
    }
  }, [connectedServers]);

  // Refresh tools
  const refreshTools = useCallback(async () => {
    await loadTools();
  }, [loadTools]);

  // Execute a tool
  const executeTool = useCallback(async (toolId: string, parameters: Record<string, any>) => {
    try {
      const result = await window.api?.executeMCPTool?.(toolId, parameters);
      
      // Update usage statistics
      setTools(prev => prev.map(tool => 
        tool.id === toolId 
          ? { 
              ...tool, 
              usageCount: tool.usageCount + 1,
              lastUsed: new Date()
            }
          : tool
      ));

      return result;
    } catch (err: any) {
      console.error('Failed to execute tool:', err);
      throw err;
    }
  }, []);

  // Get tools by server
  const getToolsByServer = useCallback((serverId: string): Tool[] => {
    return tools.filter(tool => tool.serverId === serverId);
  }, [tools]);

  // Get tools by category
  const getToolsByCategory = useCallback((category: string): Tool[] => {
    return tools.filter(tool => tool.category === category);
  }, [tools]);

  // Search tools
  const searchTools = useCallback((query: string): Tool[] => {
    if (!query.trim()) return tools;

    const lowercaseQuery = query.toLowerCase();
    return tools.filter(tool => 
      tool.name.toLowerCase().includes(lowercaseQuery) ||
      tool.description.toLowerCase().includes(lowercaseQuery) ||
      tool.category.toLowerCase().includes(lowercaseQuery) ||
      tool.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
      tool.serverName.toLowerCase().includes(lowercaseQuery)
    );
  }, [tools]);

  // Load tools when connected servers change
  useEffect(() => {
    loadTools();
  }, [loadTools]);

  // Mock data for development (remove when backend is ready)
  useEffect(() => {
    if (!window.api?.getMCPServerTools && connectedServers.length > 0) {
      const mockTools: Tool[] = [];

      connectedServers.forEach(serverId => {
        switch (serverId) {
          case 'github':
            mockTools.push(
              {
                id: 'github-create-repo',
                name: 'Create Repository',
                description: 'Create a new GitHub repository with specified settings',
                serverId: 'github',
                serverName: 'GitHub Integration',
                category: 'repository',
                tags: ['git', 'repository', 'create'],
                parameters: [
                  {
                    name: 'name',
                    type: 'string',
                    description: 'Repository name',
                    required: true
                  },
                  {
                    name: 'description',
                    type: 'string',
                    description: 'Repository description',
                    required: false
                  },
                  {
                    name: 'private',
                    type: 'boolean',
                    description: 'Make repository private',
                    required: false,
                    default: false
                  }
                ],
                examples: [
                  {
                    name: 'Create public repository',
                    description: 'Create a public repository with README',
                    parameters: {
                      name: 'my-awesome-project',
                      description: 'An awesome project',
                      private: false
                    }
                  }
                ],
                usageCount: 12,
                lastUsed: new Date(Date.now() - 3600000),
                averageExecutionTime: 2500,
                successRate: 0.95
              },
              {
                id: 'github-search-repos',
                name: 'Search Repositories',
                description: 'Search for repositories on GitHub',
                serverId: 'github',
                serverName: 'GitHub Integration',
                category: 'search',
                tags: ['search', 'repository', 'discover'],
                parameters: [
                  {
                    name: 'query',
                    type: 'string',
                    description: 'Search query',
                    required: true
                  },
                  {
                    name: 'sort',
                    type: 'string',
                    description: 'Sort order',
                    required: false,
                    enum: ['stars', 'forks', 'updated']
                  }
                ],
                examples: [
                  {
                    name: 'Search React repositories',
                    description: 'Find popular React repositories',
                    parameters: {
                      query: 'react',
                      sort: 'stars'
                    }
                  }
                ],
                usageCount: 8,
                lastUsed: new Date(Date.now() - 7200000),
                averageExecutionTime: 1200,
                successRate: 0.98
              },
              {
                id: 'github-get-file',
                name: 'Get File Contents',
                description: 'Retrieve the contents of a file from a repository',
                serverId: 'github',
                serverName: 'GitHub Integration',
                category: 'file',
                tags: ['file', 'content', 'read'],
                parameters: [
                  {
                    name: 'owner',
                    type: 'string',
                    description: 'Repository owner',
                    required: true
                  },
                  {
                    name: 'repo',
                    type: 'string',
                    description: 'Repository name',
                    required: true
                  },
                  {
                    name: 'path',
                    type: 'string',
                    description: 'File path',
                    required: true
                  }
                ],
                examples: [
                  {
                    name: 'Get README file',
                    description: 'Retrieve README.md from a repository',
                    parameters: {
                      owner: 'facebook',
                      repo: 'react',
                      path: 'README.md'
                    }
                  }
                ],
                usageCount: 15,
                lastUsed: new Date(Date.now() - 1800000),
                averageExecutionTime: 800,
                successRate: 0.97
              }
            );
            break;
          case 'slack':
            mockTools.push(
              {
                id: 'slack-send-message',
                name: 'Send Message',
                description: 'Send a message to a Slack channel or user',
                serverId: 'slack',
                serverName: 'Slack Integration',
                category: 'messaging',
                tags: ['message', 'communication', 'send'],
                parameters: [
                  {
                    name: 'channel',
                    type: 'string',
                    description: 'Channel ID or name',
                    required: true
                  },
                  {
                    name: 'text',
                    type: 'string',
                    description: 'Message text',
                    required: true
                  }
                ],
                examples: [
                  {
                    name: 'Send notification',
                    description: 'Send a notification to the team channel',
                    parameters: {
                      channel: '#general',
                      text: 'Deployment completed successfully!'
                    }
                  }
                ],
                usageCount: 5,
                lastUsed: new Date(Date.now() - 10800000),
                averageExecutionTime: 1500,
                successRate: 0.92
              }
            );
            break;
        }
      });

      setTools(mockTools);
      setLoading(false);
    }
  }, [connectedServers]);

  return {
    tools,
    loading,
    error,
    refreshTools,
    executeTool,
    getToolsByServer,
    getToolsByCategory,
    searchTools
  };
};
