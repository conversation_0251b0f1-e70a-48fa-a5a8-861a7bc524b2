/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_CONSOLE_URL: string;
  // add other env variables here
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

declare global {
  interface Window {
    api: {
      createLoginSession: (source: string, redirectUri: string) => Promise<string>;
      openExternal: (url: string) => void;
      openFolderDialog: () => Promise<string | null>;
      getSourceFiles: (folderPath: string) => Promise<{ name: string; content: string; }[]>;
      saveFile: (folderPath: string, fileName: string, content: string) => Promise<{ success: boolean; path?: string; error?: string }>;
      onAuthSuccess: (callback: (token: string) => void) => void;
      removeAuthListeners: () => void;
      scanIDEs: () => Promise<{ name: string, version: string | null, status: string }[]>;
      getAuthToken: () => Promise<string | null>;
      logout: () => Promise<{ success: boolean; error?: any }>;
      getIDEPlugins: (ideName: string) => Promise<string[]>;
      checkPluginUpdate: (ide: string) => Promise<{
        installedVersion: string | null;
        latestVersion: string;
        updateAvailable: boolean;
        downloadUrl: string;
      }>;
      downloadPlugin: (url: string) => Promise<{ success: boolean; path?: string; error?: string }>;
      installPlugin: (filePath: string, ide: string) => Promise<{ success: boolean; error?: string }>;
      onIndexingProgress: (callback: (data: any) => void) => void;
      onIndexingMeta: (callback: (data: any) => void) => void;
      loadExistingMeta?: () => Promise<MetaUpdate>;
      removeIndex: (path: string) => Promise<{ success: boolean; error?: string }>;
      reindex: (dirPath: string) => Promise<{ success: boolean; error?: string }>;
      resumeIndexing: (dirPath: string) => Promise<{ success: boolean; error?: string }>;
      pauseIndexing: (dirPath: string, curProgress: number) => Promise<{ success: boolean; error?: string }>;
      getModelConfiguration: () => Promise<ModelConfiguration>;
      saveModelConfiguration: (config: ModelConfiguration) => Promise<{ success: boolean }>;
      toggleModel: (modelId: string, modelType: 'llm' | 'embedding') => Promise<{ success: boolean }>;
      setPreferredModel: (type: string, modelId: string) => Promise<{ success: boolean }>;
      syncWithPlugins: () => Promise<{ success: boolean }>;
      clearModelCache: (modelId: string) => Promise<{ success: boolean }>;

      // MCP Management APIs
      mcpGetServers: () => Promise<any>;
      mcpGetServerStatus: (serverId: string) => Promise<any>;
      mcpConnectServer: (serverId: string, credentials?: Record<string, string>) => Promise<any>;
      mcpDisconnectServer: (serverId: string) => Promise<any>;
      mcpGetTools: (serverId: string) => Promise<any>;
      mcpExecuteTool: (request: { serverId: string; toolName: string; parameters: Record<string, any> }) => Promise<any>;
      mcpRestartServer: (serverId: string) => Promise<any>;
      mcpGetAllTools: () => Promise<any>;

      // GitHub MCP APIs
      githubMCPConfigurePAT: (pat: string) => Promise<any>;
      githubMCPValidatePAT: (pat?: string) => Promise<any>;
      githubMCPGetUser: () => Promise<any>;
      githubMCPGetAuthStatus: () => Promise<any>;
      githubMCPClearCredentials: () => Promise<any>;
      githubMCPRefreshUser: () => Promise<any>;
      githubMCPHasCredentials: () => Promise<any>;

      // MCP Event Listeners
      onMCPServerStatusChanged: (callback: (status: any) => void) => void;
      onMCPServerError: (callback: (data: any) => void) => void;
      onMCPToolsUpdated: (callback: (data: any) => void) => void;
      removeMCPListeners: () => void;
    };
  }
}

export {};