
import { useState, useEffect } from "react"
import { PageSkeleton } from "@/components/ui/page-skeleton"

interface LoadingWrapperProps {
  children: React.ReactNode
  isLoading?: boolean
  skeletonProps?: {
    variant?: "dashboard" | "ide-integrator" | "models" | "projects" | "search" | "settings" | "default"
    showHeader?: boolean
    showStats?: boolean
    showTabs?: boolean
    showTable?: boolean
    showCards?: boolean
    showWizard?: boolean
  }
}

const LoadingWrapper = ({ children, isLoading = false, skeletonProps }: LoadingWrapperProps) => {
  const [showSkeleton, setShowSkeleton] = useState(true)

  useEffect(() => {
    // Simulate initial page load
    const timer = setTimeout(() => {
      setShowSkeleton(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  if (showSkeleton || isLoading) {
    return <PageSkeleton {...skeletonProps} />
  }

  return <>{children}</>
}

export { LoadingWrapper }