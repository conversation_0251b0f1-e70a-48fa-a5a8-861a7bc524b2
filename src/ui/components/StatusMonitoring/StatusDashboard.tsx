import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../ui/tabs';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  TrendingDown,
  Zap,
  Server,
  Cpu,
  MemoryStick,
  Network,
  AlertCircle
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface MCPServer {
  id: string;
  name: string;
  description: string;
  category: string;
  status: string;
  toolsCount: number;
}

interface ServerStatus {
  status: 'stopped' | 'starting' | 'running' | 'error' | 'connecting' | 'connected' | 'disconnected';
  pid?: number;
  uptime?: number;
  lastError?: string;
  lastSeen?: Date;
  health: {
    responsive: boolean;
    latency: number;
    errorRate: number;
  };
  resources?: {
    cpu: number;
    memory: number;
  };
  metrics?: {
    requestCount: number;
    successRate: number;
    avgResponseTime: number;
  };
}

interface Tool {
  id: string;
  name: string;
  serverId: string;
  serverName: string;
  usageCount: number;
  lastUsed?: Date;
  averageExecutionTime?: number;
  successRate?: number;
}

interface StatusDashboardProps {
  servers: MCPServer[];
  serverStatuses: Record<string, ServerStatus>;
  tools: Tool[];
}

const StatusDashboard: React.FC<StatusDashboardProps> = ({
  servers,
  serverStatuses,
  tools
}) => {
  const [selectedTimeRange, setSelectedTimeRange] = useState('1h');

  // Calculate overall statistics
  const stats = {
    totalServers: servers.length,
    healthyServers: Object.values(serverStatuses).filter(s => s.health?.responsive && s.status === 'connected').length,
    totalTools: tools.length,
    totalRequests: Object.values(serverStatuses).reduce((sum, s) => sum + (s.metrics?.requestCount || 0), 0),
    avgLatency: Object.values(serverStatuses).reduce((sum, s) => sum + (s.health?.latency || 0), 0) / Math.max(Object.values(serverStatuses).length, 1),
    avgSuccessRate: Object.values(serverStatuses).reduce((sum, s) => sum + (s.metrics?.successRate || 0), 0) / Math.max(Object.values(serverStatuses).length, 1)
  };

  const getHealthColor = (status: ServerStatus) => {
    if (!status.health.responsive || status.status === 'error') {
      return 'text-red-500';
    }
    if (status.health.latency > 2000 || status.health.errorRate > 0.1) {
      return 'text-yellow-500';
    }
    return 'text-green-500';
  };

  const getHealthIcon = (status: ServerStatus) => {
    if (!status.health.responsive || status.status === 'error') {
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
    if (status.health.latency > 2000 || status.health.errorRate > 0.1) {
      return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    }
    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  const formatLatency = (latency: number) => {
    if (latency < 1000) {
      return `${Math.round(latency)}ms`;
    }
    return `${(latency / 1000).toFixed(1)}s`;
  };

  const formatUptime = (uptime?: number) => {
    if (!uptime) return 'N/A';
    
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
      return `${days}d ${hours % 24}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-white">System Monitoring</h2>
          <p className="text-slate-400 text-sm">
            Real-time status and performance metrics
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          {['1h', '6h', '24h', '7d'].map(range => (
            <Button
              key={range}
              variant={selectedTimeRange === range ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedTimeRange(range)}
            >
              {range}
            </Button>
          ))}
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Servers</p>
                <p className="text-2xl font-bold text-white">
                  {stats.healthyServers}/{stats.totalServers}
                </p>
              </div>
              <Server className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Avg Latency</p>
                <p className="text-2xl font-bold text-white">
                  {formatLatency(stats.avgLatency)}
                </p>
              </div>
              <Network className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Success Rate</p>
                <p className="text-2xl font-bold text-white">
                  {(stats.avgSuccessRate * 100).toFixed(1)}%
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Requests</p>
                <p className="text-2xl font-bold text-white">{stats.totalRequests}</p>
              </div>
              <Activity className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Monitoring */}
      <Tabs defaultValue="servers" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3 bg-slate-800">
          <TabsTrigger value="servers">Server Health</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="tools">Tool Usage</TabsTrigger>
        </TabsList>

        <TabsContent value="servers" className="space-y-4">
          <div className="grid gap-4">
            {servers.map(server => {
              const status = serverStatuses[server.id];
              if (!status) return null;

              return (
                <Card key={server.id} className="bg-slate-800 border-slate-700">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getHealthIcon(status)}
                        <div>
                          <CardTitle className="text-white text-lg">{server.name}</CardTitle>
                          <CardDescription className="text-slate-400">
                            {server.description}
                          </CardDescription>
                        </div>
                      </div>
                      <Badge 
                        variant={status.status === 'connected' ? 'default' : 'secondary'}
                        className="capitalize"
                      >
                        {status.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-slate-400">Latency:</span>
                        <span className={`ml-2 font-medium ${getHealthColor(status)}`}>
                          {formatLatency(status.health.latency)}
                        </span>
                      </div>
                      
                      <div>
                        <span className="text-slate-400">Error Rate:</span>
                        <span className="text-white ml-2 font-medium">
                          {(status.health.errorRate * 100).toFixed(1)}%
                        </span>
                      </div>
                      
                      {status.uptime && (
                        <div>
                          <span className="text-slate-400">Uptime:</span>
                          <span className="text-white ml-2 font-medium">
                            {formatUptime(status.uptime)}
                          </span>
                        </div>
                      )}
                      
                      {status.metrics && (
                        <div>
                          <span className="text-slate-400">Requests:</span>
                          <span className="text-white ml-2 font-medium">
                            {status.metrics.requestCount}
                          </span>
                        </div>
                      )}
                    </div>

                    {status.resources && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex items-center space-x-2">
                          <Cpu className="w-4 h-4 text-blue-500" />
                          <span className="text-slate-400 text-sm">CPU:</span>
                          <span className="text-white text-sm font-medium">
                            {status.resources.cpu.toFixed(1)}%
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <MemoryStick className="w-4 h-4 text-green-500" />
                          <span className="text-slate-400 text-sm">Memory:</span>
                          <span className="text-white text-sm font-medium">
                            {status.resources.memory.toFixed(1)}MB
                          </span>
                        </div>
                      </div>
                    )}

                    {status.lastError && (
                      <div className="p-3 bg-red-900/20 border border-red-700/30 rounded-md">
                        <div className="flex items-center space-x-2">
                          <AlertCircle className="w-4 h-4 text-red-500" />
                          <span className="text-red-400 text-sm font-medium">Last Error:</span>
                        </div>
                        <p className="text-red-300 text-xs mt-1">{status.lastError}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Performance Metrics</CardTitle>
              <CardDescription>
                Response times and throughput over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-slate-400">
                <div className="text-center">
                  <TrendingUp className="w-12 h-12 mx-auto mb-4" />
                  <p>Performance charts will be displayed here</p>
                  <p className="text-sm">Real-time metrics visualization</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tools" className="space-y-4">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Tool Usage Statistics</CardTitle>
              <CardDescription>
                Most used tools and execution patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {tools
                  .sort((a, b) => b.usageCount - a.usageCount)
                  .slice(0, 10)
                  .map(tool => (
                    <div key={tool.id} className="flex items-center justify-between p-3 bg-slate-700/50 rounded-md">
                      <div>
                        <h4 className="text-white font-medium">{tool.name}</h4>
                        <p className="text-slate-400 text-sm">{tool.serverName}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-white font-medium">{tool.usageCount} uses</div>
                        {tool.lastUsed && (
                          <div className="text-slate-400 text-xs">
                            {formatDistanceToNow(tool.lastUsed, { addSuffix: true })}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                
                {tools.length === 0 && (
                  <div className="text-center py-8 text-slate-400">
                    <Zap className="w-12 h-12 mx-auto mb-4" />
                    <p>No tool usage data available</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default StatusDashboard;
