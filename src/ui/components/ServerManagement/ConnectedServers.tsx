import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Play, 
  Square, 
  Settings, 
  BarChart3,
  RefreshCw,
  Zap,
  Wifi,
  WifiOff
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface MCPServer {
  id: string;
  name: string;
  description: string;
  category: string;
  status: 'stopped' | 'starting' | 'running' | 'error' | 'connected' | 'disconnected';
  installed: boolean;
  configured: boolean;
  version?: string;
  author?: string;
  capabilities: string[];
  toolsCount: number;
  lastSeen?: Date;
}

interface ServerStatus {
  status: 'stopped' | 'starting' | 'running' | 'error' | 'connecting' | 'connected' | 'disconnected';
  pid?: number;
  uptime?: number;
  lastError?: string;
  lastSeen?: Date;
  health: {
    responsive: boolean;
    latency: number;
    errorRate: number;
  };
  resources?: {
    cpu: number;
    memory: number;
  };
  metrics?: {
    requestCount: number;
    successRate: number;
    avgResponseTime: number;
  };
}

interface ConnectedServersProps {
  servers: MCPServer[];
  serverStatuses: Record<string, ServerStatus>;
  onServerAction: (action: string, serverId: string) => void;
}

const ConnectedServers: React.FC<ConnectedServersProps> = ({
  servers,
  serverStatuses,
  onServerAction
}) => {
  const getStatusIcon = (server: MCPServer, status?: ServerStatus) => {
    const currentStatus = status?.status || server.status;
    
    switch (currentStatus) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'running':
        return <Play className="w-4 h-4 text-blue-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'starting':
      case 'connecting':
        return <Clock className="w-4 h-4 text-yellow-500 animate-spin" />;
      case 'disconnected':
        return <WifiOff className="w-4 h-4 text-gray-500" />;
      default:
        return <Square className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (server: MCPServer, status?: ServerStatus) => {
    const currentStatus = status?.status || server.status;
    
    switch (currentStatus) {
      case 'connected':
        return 'text-green-500';
      case 'running':
        return 'text-blue-500';
      case 'error':
        return 'text-red-500';
      case 'starting':
      case 'connecting':
        return 'text-yellow-500';
      case 'disconnected':
        return 'text-gray-500';
      default:
        return 'text-gray-500';
    }
  };

  const getHealthStatus = (status?: ServerStatus) => {
    if (!status) return 'unknown';
    
    if (!status.health.responsive || status.status === 'error') {
      return 'error';
    }
    
    if (status.health.latency > 2000 || status.health.errorRate > 0.1) {
      return 'warning';
    }
    
    return 'healthy';
  };

  const formatLatency = (latency: number) => {
    if (latency < 1000) {
      return `${latency}ms`;
    }
    return `${(latency / 1000).toFixed(1)}s`;
  };

  const formatUptime = (uptime?: number) => {
    if (!uptime) return 'N/A';
    
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  if (servers.length === 0) {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-8 text-center">
          <Wifi className="w-12 h-12 text-slate-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No Connected Servers</h3>
          <p className="text-slate-400 mb-4">
            Connect to MCP servers to see them here
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-white">Connected Servers</h2>
          <p className="text-slate-400 text-sm">
            {servers.length} server{servers.length !== 1 ? 's' : ''} connected
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            servers.forEach(server => {
              onServerAction('refresh-status', server.id);
            });
          }}
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh All
        </Button>
      </div>

      <div className="space-y-3">
        {servers.map(server => {
          const status = serverStatuses[server.id];
          const healthStatus = getHealthStatus(status);
          
          return (
            <Card key={server.id} className="bg-slate-800 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  {/* Server Info */}
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(server, status)}
                      <div>
                        <h3 className="text-white font-medium">{server.name}</h3>
                        <p className="text-slate-400 text-sm">{server.description}</p>
                      </div>
                    </div>
                  </div>

                  {/* Status and Actions */}
                  <div className="flex items-center space-x-4">
                    {/* Status Details */}
                    <div className="text-right text-sm">
                      <div className={`font-medium ${getStatusColor(server, status)}`}>
                        {status?.status || server.status}
                      </div>
                      {status?.health && (
                        <div className="text-slate-400">
                          {formatLatency(status.health.latency)} latency
                        </div>
                      )}
                    </div>

                    {/* Health Badge */}
                    <Badge 
                      variant={
                        healthStatus === 'healthy' ? 'default' :
                        healthStatus === 'warning' ? 'secondary' : 'destructive'
                      }
                      className="text-xs"
                    >
                      {healthStatus}
                    </Badge>

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2">
                      {server.status === 'connected' ? (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onServerAction('disconnect', server.id)}
                        >
                          Disconnect
                        </Button>
                      ) : (
                        <Button
                          size="sm"
                          onClick={() => onServerAction('connect', server.id)}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          Connect
                        </Button>
                      )}
                      
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => onServerAction('configure', server.id)}
                      >
                        <Settings className="w-4 h-4" />
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => onServerAction('view-metrics', server.id)}
                      >
                        <BarChart3 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Expanded Details */}
                {status && (
                  <div className="mt-4 pt-4 border-t border-slate-700">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-slate-400">Tools:</span>
                        <span className="text-white ml-2">{server.toolsCount}</span>
                      </div>
                      
                      {status.uptime && (
                        <div>
                          <span className="text-slate-400">Uptime:</span>
                          <span className="text-white ml-2">{formatUptime(status.uptime)}</span>
                        </div>
                      )}
                      
                      {status.metrics && (
                        <>
                          <div>
                            <span className="text-slate-400">Requests:</span>
                            <span className="text-white ml-2">{status.metrics.requestCount}</span>
                          </div>
                          
                          <div>
                            <span className="text-slate-400">Success Rate:</span>
                            <span className="text-white ml-2">
                              {(status.metrics.successRate * 100).toFixed(1)}%
                            </span>
                          </div>
                        </>
                      )}
                      
                      {status.resources && (
                        <>
                          <div>
                            <span className="text-slate-400">CPU:</span>
                            <span className="text-white ml-2">{status.resources.cpu.toFixed(1)}%</span>
                          </div>
                          
                          <div>
                            <span className="text-slate-400">Memory:</span>
                            <span className="text-white ml-2">{status.resources.memory.toFixed(1)}MB</span>
                          </div>
                        </>
                      )}
                      
                      {status.lastSeen && (
                        <div>
                          <span className="text-slate-400">Last Seen:</span>
                          <span className="text-white ml-2">
                            {formatDistanceToNow(status.lastSeen, { addSuffix: true })}
                          </span>
                        </div>
                      )}
                      
                      {status.lastError && (
                        <div className="col-span-2 md:col-span-4">
                          <span className="text-slate-400">Last Error:</span>
                          <span className="text-red-400 ml-2 text-xs">{status.lastError}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default ConnectedServers;
