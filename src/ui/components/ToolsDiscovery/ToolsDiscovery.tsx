import React, { useState, use<PERSON>emo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { 
  Search, 
  Filter, 
  Play, 
  Clock, 
  TrendingUp,
  Zap,
  Grid3X3,
  List,
  ExternalLink,
  Code,
  FileText
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface ToolParameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  default?: any;
  enum?: string[];
}

interface ToolExample {
  name: string;
  description: string;
  parameters: Record<string, any>;
  expectedResult?: string;
}

interface Tool {
  id: string;
  name: string;
  description: string;
  serverId: string;
  serverName: string;
  category: string;
  tags: string[];
  parameters: ToolParameter[];
  examples: ToolExample[];
  usageCount: number;
  lastUsed?: Date;
  averageExecutionTime?: number;
  successRate?: number;
}

interface MCPServer {
  id: string;
  name: string;
  description: string;
  category: string;
  status: string;
  installed: boolean;
  configured: boolean;
  capabilities: string[];
  toolsCount: number;
}

interface ToolsDiscoveryProps {
  tools: Tool[];
  servers: MCPServer[];
  loading: boolean;
}

const ToolsDiscovery: React.FC<ToolsDiscoveryProps> = ({
  tools,
  servers,
  loading
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [serverFilter, setServerFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);

  // Get unique categories and servers
  const categories = useMemo(() => {
    const cats = Array.from(new Set(tools.map(t => t.category)));
    return cats.sort();
  }, [tools]);

  const connectedServers = useMemo(() => {
    return servers.filter(s => s.status === 'connected');
  }, [servers]);

  // Filter and sort tools
  const filteredAndSortedTools = useMemo(() => {
    let filtered = tools.filter(tool => {
      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch = 
          tool.name.toLowerCase().includes(query) ||
          tool.description.toLowerCase().includes(query) ||
          tool.category.toLowerCase().includes(query) ||
          tool.tags.some(tag => tag.toLowerCase().includes(query)) ||
          tool.serverName.toLowerCase().includes(query);
        
        if (!matchesSearch) return false;
      }

      // Server filter
      if (serverFilter !== 'all' && tool.serverId !== serverFilter) {
        return false;
      }

      // Category filter
      if (categoryFilter !== 'all' && tool.category !== categoryFilter) {
        return false;
      }

      return true;
    });

    // Sort tools
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'usage':
          return b.usageCount - a.usageCount;
        case 'recent':
          if (!a.lastUsed && !b.lastUsed) return 0;
          if (!a.lastUsed) return 1;
          if (!b.lastUsed) return -1;
          return b.lastUsed.getTime() - a.lastUsed.getTime();
        case 'server':
          return a.serverName.localeCompare(b.serverName);
        default:
          return 0;
      }
    });

    return filtered;
  }, [tools, searchQuery, serverFilter, categoryFilter, sortBy]);

  const getToolIcon = (tool: Tool) => {
    if (tool.category.includes('file') || tool.category.includes('content')) {
      return <FileText className="w-5 h-5" />;
    }
    if (tool.category.includes('code') || tool.category.includes('repository')) {
      return <Code className="w-5 h-5" />;
    }
    return <Zap className="w-5 h-5" />;
  };

  const ToolCard: React.FC<{ tool: Tool }> = ({ tool }) => (
    <Card 
      className="bg-slate-800 border-slate-700 hover:border-slate-600 transition-colors cursor-pointer"
      onClick={() => setSelectedTool(tool)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            {getToolIcon(tool)}
            <div>
              <CardTitle className="text-white text-base">{tool.name}</CardTitle>
              <CardDescription className="text-slate-400 text-sm">
                {tool.serverName}
              </CardDescription>
            </div>
          </div>
          <Badge variant="secondary" className="text-xs">
            {tool.category}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <p className="text-slate-300 text-sm line-clamp-2">{tool.description}</p>
        
        <div className="flex flex-wrap gap-1">
          {tool.tags.slice(0, 3).map(tag => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {tool.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{tool.tags.length - 3}
            </Badge>
          )}
        </div>

        <div className="flex items-center justify-between text-xs text-slate-400">
          <div className="flex items-center space-x-3">
            <span>Used {tool.usageCount}x</span>
            {tool.lastUsed && (
              <span>{formatDistanceToNow(tool.lastUsed, { addSuffix: true })}</span>
            )}
          </div>
          {tool.successRate && (
            <span>{(tool.successRate * 100).toFixed(0)}% success</span>
          )}
        </div>

        <Button size="sm" className="w-full" variant="outline">
          <Play className="w-4 h-4 mr-2" />
          Execute Tool
        </Button>
      </CardContent>
    </Card>
  );

  const ToolListItem: React.FC<{ tool: Tool }> = ({ tool }) => (
    <Card 
      className="bg-slate-800 border-slate-700 hover:border-slate-600 transition-colors cursor-pointer"
      onClick={() => setSelectedTool(tool)}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {getToolIcon(tool)}
            <div>
              <h3 className="text-white font-medium">{tool.name}</h3>
              <p className="text-slate-400 text-sm">{tool.description}</p>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="secondary" className="text-xs">{tool.category}</Badge>
                <span className="text-slate-500 text-xs">{tool.serverName}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-right text-sm text-slate-400">
              <div>Used {tool.usageCount} times</div>
              {tool.lastUsed && (
                <div>{formatDistanceToNow(tool.lastUsed, { addSuffix: true })}</div>
              )}
            </div>
            <Button size="sm" variant="outline">
              <Play className="w-4 h-4 mr-2" />
              Execute
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-slate-400">Loading tools...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-white">Available Tools</h2>
          <p className="text-slate-400 text-sm">
            {filteredAndSortedTools.length} of {tools.length} tools
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid3X3 className="w-4 h-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
          <Input
            placeholder="Search tools..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-slate-800 border-slate-700 text-white"
          />
        </div>
        
        <Select value={serverFilter} onValueChange={setServerFilter}>
          <SelectTrigger className="w-48 bg-slate-800 border-slate-700 text-white">
            <SelectValue placeholder="All Servers" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Servers</SelectItem>
            {connectedServers.map(server => (
              <SelectItem key={server.id} value={server.id}>
                {server.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-48 bg-slate-800 border-slate-700 text-white">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map(category => (
              <SelectItem key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-48 bg-slate-800 border-slate-700 text-white">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="name">Name</SelectItem>
            <SelectItem value="usage">Most Used</SelectItem>
            <SelectItem value="recent">Recently Used</SelectItem>
            <SelectItem value="server">Server</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Tools List */}
      {filteredAndSortedTools.length === 0 ? (
        <div className="text-center py-12">
          <Zap className="w-12 h-12 text-slate-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No tools found</h3>
          <p className="text-slate-400">
            {tools.length === 0 
              ? 'Connect to MCP servers to discover available tools'
              : 'Try adjusting your search or filters'
            }
          </p>
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? 'grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
            : 'space-y-3'
        }>
          {filteredAndSortedTools.map(tool => 
            viewMode === 'grid' 
              ? <ToolCard key={tool.id} tool={tool} />
              : <ToolListItem key={tool.id} tool={tool} />
          )}
        </div>
      )}
    </div>
  );
};

export default ToolsDiscovery;
