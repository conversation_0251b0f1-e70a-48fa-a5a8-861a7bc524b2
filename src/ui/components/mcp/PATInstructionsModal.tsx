// PAT Instructions Modal - Detailed instructions for creating GitHub PAT
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { <PERSON><PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { 
  ExternalLink, 
  Key, 
  Shield, 
  CheckCircle,
  AlertTriangle,
  Info,
  Copy
} from 'lucide-react';

interface PATInstructionsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const PATInstructionsModal: React.FC<PATInstructionsModalProps> = ({
  open,
  onOpenChange
}) => {
  const openGitHubTokenPage = () => {
    window.api.openExternal('https://github.com/settings/tokens/new?scopes=repo,user,read:org&description=Alpine%20Intellect%20Desktop');
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const requiredScopes = [
    {
      scope: 'repo',
      description: 'Full control of private repositories',
      reason: 'Required to create repositories, manage files, and access private repos'
    },
    {
      scope: 'user',
      description: 'Read/write user data',
      reason: 'Required to access user profile information and settings'
    },
    {
      scope: 'read:org',
      description: 'Read org and team membership',
      reason: 'Required to access organization repositories and team information'
    }
  ];

  const steps = [
    {
      step: 1,
      title: 'Go to GitHub Settings',
      description: 'Navigate to GitHub → Settings → Developer settings → Personal access tokens → Tokens (classic)',
      action: 'Click the button below to open GitHub directly'
    },
    {
      step: 2,
      title: 'Generate New Token',
      description: 'Click "Generate new token" and select "Generate new token (classic)"',
      action: 'Choose the classic token option for compatibility'
    },
    {
      step: 3,
      title: 'Configure Token',
      description: 'Set a descriptive note like "Alpine Intellect Desktop" and choose an expiration',
      action: 'We recommend 90 days or "No expiration" for convenience'
    },
    {
      step: 4,
      title: 'Select Scopes',
      description: 'Select the required scopes listed below',
      action: 'Make sure all three scopes are checked'
    },
    {
      step: 5,
      title: 'Generate and Copy',
      description: 'Click "Generate token" and copy the token immediately',
      action: 'You won\'t be able to see the token again after leaving the page'
    },
    {
      step: 6,
      title: 'Paste in Alpine Intellect',
      description: 'Return to Alpine Intellect and paste the token in the connection dialog',
      action: 'The token will be securely stored in your system keychain'
    }
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5" />
            <span>GitHub Personal Access Token Setup</span>
          </DialogTitle>
          <DialogDescription>
            Follow these steps to create a GitHub Personal Access Token for Alpine Intellect
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-96">
          <div className="space-y-6">
            {/* Security Notice */}
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start space-x-3">
                <Shield className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="text-sm font-semibold text-blue-900">Security & Privacy</h3>
                  <p className="text-sm text-blue-700 mt-1">
                    Your Personal Access Token will be encrypted and stored securely in your system's 
                    credential manager. It will never be logged, saved to disk, or transmitted anywhere 
                    except to GitHub's official API.
                  </p>
                </div>
              </div>
            </div>

            {/* Quick Start Button */}
            <div className="text-center">
              <Button onClick={openGitHubTokenPage} className="w-full">
                <ExternalLink className="h-4 w-4 mr-2" />
                Create New PAT on GitHub (Opens with correct settings)
              </Button>
            </div>

            <Separator />

            {/* Required Scopes */}
            <div>
              <h3 className="text-lg font-semibold mb-3 flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span>Required Scopes</span>
              </h3>
              <div className="space-y-3">
                {requiredScopes.map((scope) => (
                  <div key={scope.scope} className="p-3 border border-gray-200 rounded-lg">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <Badge variant="outline" className="font-mono text-xs">
                            {scope.scope}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(scope.scope)}
                            className="h-6 w-6 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                        <p className="text-sm font-medium text-gray-900">{scope.description}</p>
                        <p className="text-xs text-gray-600 mt-1">{scope.reason}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Step-by-Step Instructions */}
            <div>
              <h3 className="text-lg font-semibold mb-3 flex items-center space-x-2">
                <Info className="h-5 w-5 text-blue-500" />
                <span>Step-by-Step Instructions</span>
              </h3>
              <div className="space-y-4">
                {steps.map((step) => (
                  <div key={step.step} className="flex space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                        {step.step}
                      </div>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-semibold text-gray-900">{step.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{step.description}</p>
                      <p className="text-xs text-blue-600 mt-1 italic">{step.action}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Important Notes */}
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="text-sm font-semibold text-yellow-900">Important Notes</h3>
                  <ul className="text-sm text-yellow-700 mt-1 space-y-1">
                    <li>• Copy your token immediately after generation - you won't see it again</li>
                    <li>• Choose "No expiration" or set a long expiration to avoid frequent renewals</li>
                    <li>• Keep your token secure - treat it like a password</li>
                    <li>• You can revoke the token anytime from GitHub settings</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Troubleshooting */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Troubleshooting</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <strong>Token validation fails:</strong>
                  <p className="text-gray-600">Make sure all required scopes are selected and the token is copied correctly.</p>
                </div>
                <div>
                  <strong>Can't access repositories:</strong>
                  <p className="text-gray-600">Ensure the 'repo' scope is selected for private repository access.</p>
                </div>
                <div>
                  <strong>Organization access issues:</strong>
                  <p className="text-gray-600">Make sure 'read:org' scope is selected and your organization allows PAT access.</p>
                </div>
              </div>
            </div>
          </div>
        </ScrollArea>

        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          <Button onClick={openGitHubTokenPage}>
            <ExternalLink className="h-4 w-4 mr-2" />
            Open GitHub
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
