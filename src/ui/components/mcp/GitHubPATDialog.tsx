// GitHub PAT Dialog - Secure input dialog for GitHub Personal Access Token
import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Alert, AlertDescription } from '../ui/alert';
import { Separator } from '../ui/separator';
import { 
  Eye, 
  EyeOff, 
  AlertCircle, 
  CheckCircle, 
  ExternalLink,
  Info,
  Shield,
  Key
} from 'lucide-react';

interface GitHubPATDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (pat: string) => Promise<void>;
}

export const GitHubPATDialog: React.FC<GitHubPATDialogProps> = ({
  open,
  onOpenChange,
  onSubmit
}) => {
  const [pat, setPat] = useState('');
  const [showPat, setShowPat] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [validationSuccess, setValidationSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (open) {
      setPat('');
      setShowPat(false);
      setValidationError(null);
      setValidationSuccess(false);
      setIsValidating(false);
      setIsSubmitting(false);
    }
  }, [open]);

  // Validate PAT format as user types
  useEffect(() => {
    if (pat.length === 0) {
      setValidationError(null);
      setValidationSuccess(false);
      return;
    }

    const timeoutId = setTimeout(() => {
      validatePATFormat(pat);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [pat]);

  const validatePATFormat = (token: string) => {
    // Basic format validation
    const patRegex = /^(ghp_|github_pat_)[a-zA-Z0-9]{35,}$/;
    
    if (!patRegex.test(token)) {
      setValidationError('Invalid PAT format. Must start with ghp_ or github_pat_ and be at least 40 characters.');
      setValidationSuccess(false);
      return;
    }

    if (token.length < 40) {
      setValidationError('PAT must be at least 40 characters long.');
      setValidationSuccess(false);
      return;
    }

    setValidationError(null);
    setValidationSuccess(true);
  };

  const handleSubmit = async () => {
    if (!pat || validationError || isSubmitting) {
      return;
    }

    try {
      setIsSubmitting(true);
      setIsValidating(true);
      
      await onSubmit(pat);
      
      // Close dialog on success
      onOpenChange(false);
      
    } catch (error) {
      setValidationError(
        error instanceof Error ? error.message : 'Failed to validate PAT'
      );
    } finally {
      setIsSubmitting(false);
      setIsValidating(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const openGitHubTokenPage = () => {
    window.api.openExternal('https://github.com/settings/tokens/new?scopes=repo,user,read:org&description=Alpine%20Intellect%20Desktop');
  };

  const isFormValid = pat.length > 0 && !validationError && validationSuccess;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5" />
            <span>Connect GitHub MCP Server</span>
          </DialogTitle>
          <DialogDescription>
            Enter your GitHub Personal Access Token to connect to the GitHub MCP server.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Security Notice */}
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              Your PAT will be securely stored in your system's credential manager and never logged or saved to disk.
            </AlertDescription>
          </Alert>

          {/* PAT Input */}
          <div className="space-y-2">
            <Label htmlFor="pat">Personal Access Token</Label>
            <div className="relative">
              <Input
                id="pat"
                type={showPat ? 'text' : 'password'}
                placeholder="ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                value={pat}
                onChange={(e) => setPat(e.target.value)}
                className={`pr-10 ${
                  validationError 
                    ? 'border-red-500 focus:border-red-500' 
                    : validationSuccess 
                    ? 'border-green-500 focus:border-green-500' 
                    : ''
                }`}
                disabled={isSubmitting}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPat(!showPat)}
                disabled={isSubmitting}
              >
                {showPat ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>

            {/* Validation Feedback */}
            {validationError && (
              <div className="flex items-start space-x-2 text-sm text-red-600">
                <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <span>{validationError}</span>
              </div>
            )}

            {validationSuccess && !validationError && (
              <div className="flex items-center space-x-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>PAT format is valid</span>
              </div>
            )}
          </div>

          <Separator />

          {/* Instructions */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Info className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium">Required Scopes</span>
            </div>
            
            <div className="text-sm text-gray-600 space-y-1">
              <p>Your PAT must have the following scopes:</p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li><code className="bg-gray-100 px-1 rounded">repo</code> - Full control of private repositories</li>
                <li><code className="bg-gray-100 px-1 rounded">user</code> - Read/write user data</li>
                <li><code className="bg-gray-100 px-1 rounded">read:org</code> - Read org and team membership</li>
              </ul>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={openGitHubTokenPage}
              className="w-full"
              disabled={isSubmitting}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Create New PAT on GitHub
            </Button>
          </div>

          {/* Validation Status */}
          {isValidating && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Validating PAT with GitHub API...
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!isFormValid || isSubmitting}
          >
            {isSubmitting ? 'Connecting...' : 'Connect'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
