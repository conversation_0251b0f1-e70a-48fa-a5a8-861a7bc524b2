// Server Connection Status - Displays real-time connection status
import React from 'react';
import { Badge } from '../ui/badge';
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Square,
  Wifi,
  WifiOff
} from 'lucide-react';

interface ServerConnectionStatusProps {
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  error?: string;
  lastConnected?: Date;
  className?: string;
  showDetails?: boolean;
}

export const ServerConnectionStatus: React.FC<ServerConnectionStatusProps> = ({
  status,
  error,
  lastConnected,
  className = '',
  showDetails = false
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return {
          icon: <CheckCircle className="h-4 w-4" />,
          text: 'Connected',
          color: 'bg-green-100 text-green-800 border-green-200',
          iconColor: 'text-green-500'
        };
      case 'connecting':
        return {
          icon: <Clock className="h-4 w-4 animate-spin" />,
          text: 'Connecting',
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          iconColor: 'text-yellow-500'
        };
      case 'error':
        return {
          icon: <AlertCircle className="h-4 w-4" />,
          text: 'Error',
          color: 'bg-red-100 text-red-800 border-red-200',
          iconColor: 'text-red-500'
        };
      default:
        return {
          icon: <Square className="h-4 w-4" />,
          text: 'Disconnected',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          iconColor: 'text-gray-400'
        };
    }
  };

  const config = getStatusConfig();

  if (!showDetails) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className={config.iconColor}>
          {config.icon}
        </div>
        <Badge className={config.color}>
          {config.text}
        </Badge>
      </div>
    );
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Status Badge */}
      <div className="flex items-center space-x-2">
        <div className={config.iconColor}>
          {status === 'connected' ? <Wifi className="h-4 w-4" /> : <WifiOff className="h-4 w-4" />}
        </div>
        <Badge className={config.color}>
          {config.text}
        </Badge>
      </div>

      {/* Error Details */}
      {status === 'error' && error && (
        <div className="p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
          {error}
        </div>
      )}

      {/* Last Connected */}
      {lastConnected && status !== 'connected' && (
        <p className="text-xs text-gray-500">
          Last connected: {lastConnected.toLocaleString()}
        </p>
      )}

      {/* Connection Time */}
      {status === 'connected' && lastConnected && (
        <p className="text-xs text-green-600">
          Connected since: {lastConnected.toLocaleString()}
        </p>
      )}
    </div>
  );
};
