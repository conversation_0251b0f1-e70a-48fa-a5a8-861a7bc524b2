
import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Minus, Square, X } from "lucide-react";

interface TitleBarProps {
  title?: string;
}

const TitleBar = ({ title = "Alpine Intellect" }: TitleBarProps) => {
  const [isMac, setIsMac] = useState<boolean | null>(null);

  // Memoize platform detection to prevent re-computation
  const platformDetection = useMemo(() => {
    return typeof window !== 'undefined' && navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  }, []);

  useEffect(() => {
    setIsMac(platformDetection);
  }, [platformDetection]);

  const handleMinimize = () => {
    window.electron.sendFrameAction("MINIMIZE");
    console.log("Minimize window");
    // In a real Electron app, this would call ipcRenderer.invoke('minimize-window')
  };

  const handleMaximize = () => {
    window.electron.sendFrameAction("MAXIMIZE");
    console.log("Maximize/restore window");
    // In a real Electron app, this would call ipcRenderer.invoke('maximize-window')
  };

  const handleClose = () => {
    console.log("Close window");
    window.electron.sendFrameAction("CLOSE")
    // In a real Electron app, this would call ipcRenderer.invoke('close-window')
  };

  // Prevent rendering until platform is detected to avoid flickering
  if (isMac === null) {
    return (
      <div className="h-8 bg-slate-800 border-b border-slate-700 flex items-center justify-center drag-region select-none">
        <span className="text-slate-300 text-sm font-medium">{title}</span>
      </div>
    );
  }

  if (isMac) {
    return (
      <div className="h-8 bg-slate-800 border-b border-slate-700 flex items-center justify-between drag-region select-none">
        {/* macOS traffic lights on the left */}
        <div className="flex items-center space-x-2 pl-3">
          <button
            onClick={handleClose}
            className="w-3 h-3 bg-red-500 rounded-full hover:bg-red-600 transition-colors"
            aria-label="Close"
          />
          <button
            onClick={handleMinimize}
            className="w-3 h-3 bg-yellow-500 rounded-full hover:bg-yellow-600 transition-colors"
            aria-label="Minimize"
          />
          <button
            onClick={handleMaximize}
            className="w-3 h-3 bg-green-500 rounded-full hover:bg-green-600 transition-colors"
            aria-label="Maximize"
          />
        </div>

        {/* Centered title */}
        <div className="absolute left-1/2 transform -translate-x-1/2">
          <span className="text-slate-300 text-sm font-medium">{title}</span>
        </div>

        {/* Empty space to balance layout */}
        <div className="w-16"></div>
      </div>
    );
  }

  // Windows/Linux style
  return (
    <div className="h-8 bg-slate-800 border-b border-slate-700 flex items-center justify-between drag-region select-none">
      {/* Title on the left */}
      <div className="flex items-center pl-3">
        <div className="w-4 h-4 bg-blue-500 rounded-sm flex items-center justify-center mr-2">
          <span className="text-white font-bold text-xs">AI</span>
        </div>
        <span className="text-slate-300 text-sm font-medium">{title}</span>
      </div>

      {/* Windows controls on the right */}
      <div className="flex">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleMinimize}
          className="h-8 w-8 p-0 rounded-none hover:bg-slate-700 text-slate-400 hover:text-white"
        >
          <Minus className="h-3 w-3" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleMaximize}
          className="h-8 w-8 p-0 rounded-none hover:bg-slate-700 text-slate-400 hover:text-white"
        >
          <Square className="h-3 w-3" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-8 w-8 p-0 rounded-none hover:bg-red-600 text-slate-400 hover:text-white"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
};

export { TitleBar };
