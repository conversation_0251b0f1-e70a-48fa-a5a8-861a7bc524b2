import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { 
  Search, 
  Filter, 
  Download, 
  Settings, 
  Play, 
  Square, 
  Trash2, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Zap,
  Grid3X3,
  List
} from 'lucide-react';

interface MCPServer {
  id: string;
  name: string;
  description: string;
  category: string;
  status: 'stopped' | 'starting' | 'running' | 'error' | 'connected' | 'disconnected';
  installed: boolean;
  configured: boolean;
  version?: string;
  author?: string;
  capabilities: string[];
  toolsCount: number;
  lastSeen?: Date;
}

interface ServerDirectoryProps {
  servers: MCPServer[];
  onServerAction: (action: string, serverId: string) => void;
  compact?: boolean;
}

const ServerDirectory: React.FC<ServerDirectoryProps> = ({
  servers,
  onServerAction,
  compact = false
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Get unique categories
  const categories = useMemo(() => {
    const cats = Array.from(new Set(servers.map(s => s.category)));
    return cats.sort();
  }, [servers]);

  // Filter servers based on search and filters
  const filteredServers = useMemo(() => {
    return servers.filter(server => {
      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch = 
          server.name.toLowerCase().includes(query) ||
          server.description.toLowerCase().includes(query) ||
          server.category.toLowerCase().includes(query) ||
          server.capabilities.some(cap => cap.toLowerCase().includes(query));
        
        if (!matchesSearch) return false;
      }

      // Category filter
      if (categoryFilter !== 'all' && server.category !== categoryFilter) {
        return false;
      }

      // Status filter
      if (statusFilter !== 'all') {
        switch (statusFilter) {
          case 'connected':
            return server.status === 'connected';
          case 'installed':
            return server.installed;
          case 'available':
            return !server.installed;
          default:
            return true;
        }
      }

      return true;
    });
  }, [servers, searchQuery, categoryFilter, statusFilter]);

  const getStatusIcon = (server: MCPServer) => {
    switch (server.status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'running':
        return <Play className="w-4 h-4 text-blue-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'starting':
        return <Clock className="w-4 h-4 text-yellow-500 animate-spin" />;
      default:
        return <Square className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (server: MCPServer) => {
    switch (server.status) {
      case 'connected':
        return 'bg-green-500';
      case 'running':
        return 'bg-blue-500';
      case 'error':
        return 'bg-red-500';
      case 'starting':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getActionButtons = (server: MCPServer) => {
    const buttons = [];

    if (!server.installed) {
      buttons.push(
        <Button
          key="install"
          size="sm"
          onClick={() => onServerAction('install', server.id)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Download className="w-4 h-4 mr-1" />
          Install
        </Button>
      );
    } else {
      if (!server.configured) {
        buttons.push(
          <Button
            key="configure"
            size="sm"
            variant="outline"
            onClick={() => onServerAction('configure', server.id)}
          >
            <Settings className="w-4 h-4 mr-1" />
            Configure
          </Button>
        );
      } else {
        if (server.status === 'connected') {
          buttons.push(
            <Button
              key="disconnect"
              size="sm"
              variant="outline"
              onClick={() => onServerAction('disconnect', server.id)}
            >
              Disconnect
            </Button>
          );
        } else {
          buttons.push(
            <Button
              key="connect"
              size="sm"
              onClick={() => onServerAction('connect', server.id)}
              className="bg-green-600 hover:bg-green-700"
            >
              Connect
            </Button>
          );
        }

        buttons.push(
          <Button
            key="configure"
            size="sm"
            variant="ghost"
            onClick={() => onServerAction('configure', server.id)}
          >
            <Settings className="w-4 h-4" />
          </Button>
        );

        buttons.push(
          <Button
            key="remove"
            size="sm"
            variant="ghost"
            onClick={() => onServerAction('remove', server.id)}
            className="text-red-500 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        );
      }
    }

    return buttons;
  };

  const ServerCard: React.FC<{ server: MCPServer }> = ({ server }) => (
    <Card className="bg-slate-800 border-slate-700 hover:border-slate-600 transition-colors">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${getStatusColor(server)}`} />
            <div>
              <CardTitle className="text-white text-lg">{server.name}</CardTitle>
              <CardDescription className="text-slate-400">
                {server.category} • {server.author}
              </CardDescription>
            </div>
          </div>
          {getStatusIcon(server)}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <p className="text-slate-300 text-sm">{server.description}</p>
        
        <div className="flex flex-wrap gap-1">
          {server.capabilities.slice(0, 3).map(cap => (
            <Badge key={cap} variant="secondary" className="text-xs">
              {cap.replace(/_/g, ' ')}
            </Badge>
          ))}
          {server.capabilities.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{server.capabilities.length - 3} more
            </Badge>
          )}
        </div>

        <div className="flex items-center justify-between text-sm text-slate-400">
          <span>{server.toolsCount} tools</span>
          {server.version && <span>v{server.version}</span>}
        </div>

        <div className="flex items-center space-x-2">
          {getActionButtons(server)}
        </div>
      </CardContent>
    </Card>
  );

  const ServerListItem: React.FC<{ server: MCPServer }> = ({ server }) => (
    <Card className="bg-slate-800 border-slate-700">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`w-3 h-3 rounded-full ${getStatusColor(server)}`} />
            <div>
              <h3 className="text-white font-medium">{server.name}</h3>
              <p className="text-slate-400 text-sm">{server.description}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-right text-sm text-slate-400">
              <div>{server.toolsCount} tools</div>
              <div>{server.category}</div>
            </div>
            <div className="flex items-center space-x-2">
              {getActionButtons(server)}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-white">
            {compact ? 'Server Store' : 'MCP Server Directory'}
          </h2>
          <p className="text-slate-400 text-sm">
            {filteredServers.length} of {servers.length} servers
          </p>
        </div>
        
        {!compact && (
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        )}
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
          <Input
            placeholder="Search servers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-slate-800 border-slate-700 text-white"
          />
        </div>
        
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-48 bg-slate-800 border-slate-700 text-white">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map(category => (
              <SelectItem key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48 bg-slate-800 border-slate-700 text-white">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="connected">Connected</SelectItem>
            <SelectItem value="installed">Installed</SelectItem>
            <SelectItem value="available">Available</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Server List */}
      {filteredServers.length === 0 ? (
        <div className="text-center py-12">
          <Zap className="w-12 h-12 text-slate-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No servers found</h3>
          <p className="text-slate-400">
            {searchQuery || categoryFilter !== 'all' || statusFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'No MCP servers are available'}
          </p>
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? `grid gap-4 ${compact ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'}`
            : 'space-y-3'
        }>
          {filteredServers.map(server => 
            viewMode === 'grid' 
              ? <ServerCard key={server.id} server={server} />
              : <ServerListItem key={server.id} server={server} />
          )}
        </div>
      )}
    </div>
  );
};

export default ServerDirectory;
